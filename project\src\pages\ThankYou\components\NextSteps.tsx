import React from 'react';
import { motion } from 'framer-motion';
import { Phone, Calendar, Mail, ArrowRight } from 'lucide-react';

const steps = [
  {
    icon: Phone,
    title: 'Initial Contact',
    description: 'Our team will call you within 24 hours to discuss your needs',
    color: 'from-blue-500 to-indigo-600',
    iconBg: 'bg-blue-100',
    iconColor: 'text-blue-600',
  },
  {
    icon: Calendar,
    title: 'Schedule Confirmation',
    description: 'We\'ll confirm your preferred date and time or suggest alternatives',
    color: 'from-emerald-500 to-green-600',
    iconBg: 'bg-emerald-100',
    iconColor: 'text-emerald-600',
  },
  {
    icon: Mail,
    title: 'Service Details',
    description: 'You\'ll receive a detailed email with your service confirmation',
    color: 'from-purple-500 to-pink-600',
    iconBg: 'bg-purple-100',
    iconColor: 'text-purple-600',
  },
];

export function NextSteps() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.4 }}
      className="bg-white rounded-2xl shadow-xl overflow-hidden mb-8"
    >
      <div className="p-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-8">
          What Happens Next?
        </h2>
        
        <div className="space-y-8">
          {steps.map((step, index) => (
            <motion.div
              key={step.title}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.6 + (index * 0.1) }}
              className="relative"
            >
              {/* Connector Line */}
              {index < steps.length - 1 && (
                <div className="absolute left-6 top-14 bottom-0 w-px bg-gradient-to-b from-gray-200 to-transparent" />
              )}

              <div className="relative flex items-start space-x-6">
                {/* Icon */}
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  className={`relative flex-shrink-0 w-12 h-12 ${step.iconBg} rounded-xl`}
                >
                  <step.icon className={`w-6 h-6 ${step.iconColor} absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2`} />
                  <div className={`absolute inset-0 bg-gradient-to-br ${step.color} opacity-0 
                                group-hover:opacity-10 rounded-xl transition-opacity`} />
                </motion.div>

                {/* Content */}
                <div className="flex-1">
                  <div className="flex items-center mb-2">
                    <h3 className="text-lg font-semibold text-gray-900 mr-2">
                      {step.title}
                    </h3>
                    <ArrowRight className="w-4 h-4 text-gray-400" />
                  </div>
                  <p className="text-gray-600 leading-relaxed">
                    {step.description}
                  </p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </motion.div>
  );
}