name: Release

on:
  push:
    branches:
      - main
  workflow_call:

permissions:
  contents: write
  packages: write

jobs:
  settings:
    runs-on: ubuntu-latest
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    outputs:
      release_tag: ${{ steps.prerelease.outputs.tagName }}
    steps:
      - uses: actions/checkout@v4
      - id: prerelease
        run: |
          gh release list --limit 1 --json tagName --jq \
          '.[]|to_entries|map("\(.key)=\(.value|tostring)")|.[]' >> $GITHUB_OUTPUT
      - run: gh release edit ${{ steps.prerelease.outputs.tagName }} --latest --prerelease=false

  commit:
    name: Publish Brew and Scoop
    needs:
      - settings
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          cache: true
      - run: go run tools/publish/main.go ${{ needs.settings.outputs.release_tag }}
        env:
          GITHUB_TOKEN: ${{ secrets.GH_PAT }}

  publish:
    name: Publish NPM
    needs:
      - settings
    uses: ./.github/workflows/tag-npm.yml
    with:
      release: ${{ needs.settings.outputs.release_tag }}
    secrets: inherit

  compose:
    name: Bump self-hosted versions
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          cache: true
      - run: go run tools/selfhost/main.go
        env:
          GITHUB_TOKEN: ${{ secrets.GH_PAT }}

  changelog:
    name: Publish changelog
    needs:
      - commit
      - publish
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          cache: true
      - run: go run tools/changelog/main.go ${{ secrets.SLACK_CHANNEL }}
        env:
          GITHUB_TOKEN: ${{ secrets.GH_PAT }}
          SLACK_TOKEN: ${{ secrets.SLACK_TOKEN }}

  docs:
    name: Publish reference docs
    needs:
      - settings
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          cache: true
      - run: go run docs/main.go ${{ needs.settings.outputs.release_tag }} | go run tools/bumpdoc/main.go apps/docs/spec/cli_v1_commands.yaml
        env:
          GITHUB_TOKEN: ${{ secrets.GH_PAT }}
