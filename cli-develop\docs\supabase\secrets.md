## supabase-secrets

Provides tools for managing environment variables and secrets for your Supabase project.

This command group allows you to set, unset, and list secrets that are securely stored and made available to Edge Functions as environment variables.

Secrets management through the CLI is useful for:
- Setting environment-specific configuration
- Managing sensitive credentials securely

Secrets can be set individually or loaded from .env files for convenience.
