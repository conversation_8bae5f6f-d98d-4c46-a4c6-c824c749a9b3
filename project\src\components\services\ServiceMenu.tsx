import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  X, ArrowRight, Star, Shield, Clock,
  Building2, Sparkles, GlassWater, Brush,
  Construction, Sprout, Grid, Waves, Hammer
} from 'lucide-react';

interface ServiceMenuProps {
  isOpen: boolean;
  onClose: () => void;
  onServiceSelect: (serviceId: string) => void;
  highlightedServices?: string[];
  industryContext?: string;
}

export function ServiceMenu({ 
  isOpen, 
  onClose, 
  highlightedServices, 
  industryContext 
}: ServiceMenuProps) {
  const navigate = useNavigate();

  const services = [
    {
      id: 'office',
      icon: Building2,
      title: 'Office Cleaning',
      description: 'Professional cleaning solutions for offices of all sizes',
      features: ['Daily/Weekly Service', 'Eco-Friendly Products', 'Trained Staff']
    },
    {
      id: 'deep',
      icon: Sparkles,
      title: 'Deep Cleaning',
      description: 'Thorough cleaning and sanitization services',
      features: ['Complete Sanitization', 'Deep Scrubbing', 'Air Purification']
    },
    {
      id: 'window',
      icon: GlassWater,
      title: 'Window Cleaning',
      description: 'Professional window cleaning for commercial buildings',
      features: ['Interior & Exterior', 'High-Rise Capable', 'Streak-Free Results']
    },
    {
      id: 'carpet',
      icon: Brush,
      title: 'Carpet Cleaning',
      description: 'Deep carpet cleaning and stain removal',
      features: ['Deep Extraction', 'Stain Treatment', 'Deodorizing']
    },
    {
      id: 'construction',
      icon: Construction,
      title: 'Post-Construction',
      description: 'Detailed cleaning after construction or renovation',
      features: ['Debris Removal', 'Dust Control', 'Final Inspection']
    },
    {
      id: 'sanitization',
      icon: Sprout,
      title: 'Sanitization',
      description: 'Commercial-grade sanitization and disinfection',
      features: ['Medical-Grade Products', 'High-Touch Surfaces', 'EPA Approved']
    },
    {
      id: 'tile',
      icon: Grid,
      title: 'Tile & Grout',
      description: 'Professional tile and grout cleaning services',
      features: ['Deep Cleaning', 'Grout Sealing', 'Color Restoration']
    },
    {
      id: 'pressure',
      icon: Waves,
      title: 'Pressure Washing',
      description: 'High-pressure cleaning for exterior surfaces',
      features: ['Building Exterior', 'Concrete & Pavement', 'Graffiti Removal']
    },
    {
      id: 'floor',
      icon: Hammer,
      title: 'Floor Restoration',
      description: 'Stripping, sealing, waxing, or refinishing floors',
      features: ['Stripping & Waxing', 'Buffing & Polishing', 'Sealing']
    }
  ];

  const handleServiceSelect = (serviceId: string) => {
    onClose();
    navigate(`/service-form/${serviceId}`);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50"
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ type: "spring", duration: 0.5 }}
            className="fixed inset-0 z-50 overflow-y-auto"
          >
            <div className="min-h-screen px-4 text-center flex items-center justify-center">
              <div className="relative w-full max-w-6xl bg-white rounded-3xl shadow-2xl p-8">
                {/* Close Button */}
                <motion.button
                  whileHover={{ rotate: 90 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={onClose}
                  className="absolute top-4 right-4 p-2 rounded-full hover:bg-gray-100"
                >
                  <X className="w-6 h-6 text-gray-500" />
                </motion.button>

                {/* Header */}
                <div className="text-center mb-12">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    <h2 className="text-3xl font-bold text-gray-900 mb-4">
                      {industryContext 
                        ? `Recommended Solutions for ${industryContext}`
                        : 'Select Your Service'}
                    </h2>
                    <p className="text-lg text-gray-600">
                      {industryContext
                        ? 'These services are specially curated for your industry'
                        : 'Choose from our professional cleaning solutions'}
                    </p>
                  </motion.div>

                  {/* Trust Badges */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="flex justify-center gap-6 mt-6"
                  >
                    <div className="flex items-center space-x-1">
                      <Shield className="w-5 h-5 text-brand-600" />
                      <span className="text-sm text-gray-600">Licensed & Insured</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-5 h-5 text-brand-600" />
                      <span className="text-sm text-gray-600">24/7 Service</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Star className="w-5 h-5 text-yellow-400 fill-current" />
                      <span className="text-sm text-gray-600">4.9/5 Rating</span>
                    </div>
                  </motion.div>
                </div>

                {/* Services Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {services.map((service, index) => {
                    const Icon = service.icon;
                    const isHighlighted = highlightedServices?.includes(service.id);
                    
                    return (
                      <motion.button
                        key={service.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 + (index * 0.1) }}
                        onClick={() => handleServiceSelect(service.id)}
                        className={`group relative w-full p-6 rounded-xl text-left transition-all 
                                 bg-white border ${isHighlighted ? 'border-brand-300 shadow-lg' : 'border-gray-200'} 
                                 hover:border-brand-300 hover:shadow-lg
                                 active:scale-[0.98] touch-manipulation`}
                      >
                        <div className="flex flex-col">
                          {/* Icon */}
                          <div className={`rounded-xl bg-brand-50 p-4 w-fit mb-4 
                                      group-hover:bg-brand-100 transition-colors`}>
                            <Icon className="w-8 h-8 text-brand-600" />
                          </div>

                          {/* Content */}
                          <h3 className="text-xl font-semibold text-gray-900 mb-2">
                            {service.title}
                          </h3>
                          <p className="text-gray-600 mb-4">
                            {service.description}
                          </p>

                          {/* Features */}
                          <div className="space-y-2 mb-6">
                            {service.features.map((feature, i) => (
                              <div key={i} className="flex items-center text-gray-600">
                                <div className="w-1.5 h-1.5 rounded-full bg-brand-500 mr-2" />
                                <span className="text-sm">{feature}</span>
                              </div>
                            ))}
                          </div>

                          {/* Action Button */}
                          <div className="flex items-center justify-between mt-auto">
                            <span className="font-medium text-brand-600">
                              Get Started
                            </span>
                            <ArrowRight className="w-5 h-5 text-brand-600 
                                               group-hover:translate-x-1 transition-transform" />
                          </div>
                        </div>

                        {/* Highlight Effect */}
                        {isHighlighted && (
                          <div className="absolute -top-1 -right-1">
                            <Star className="w-6 h-6 text-brand-500 fill-current" />
                          </div>
                        )}
                      </motion.button>
                    );
                  })}
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}