import type { FormData as CarpetFormData } from '../../pages/ServiceForm/forms/carpet/types';
import type { FormData as WindowFormData } from '../../pages/ServiceForm/forms/window/types';
import type { FormData as ConstructionFormData } from '../../pages/ServiceForm/forms/construction/types';
import type { FormData as ResidentialFormData } from '../../pages/Residential/forms/RegularCleaning/types';

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export function useFormValidation() {
  const validateStep = (step: number, formData: unknown): ValidationResult => {
    const errors: string[] = [];

    // Handle residential form validation
    if (formData && typeof formData === 'object' && 'servicePreferences' in formData) {
      const residentialData = formData as ResidentialFormData;
      
      switch (step) {
        case 0: // Service Type
          if (!residentialData.serviceType || residentialData.serviceType.trim() === '') {
            errors.push('Please select a service type');
          }
          break;

        case 1: // Property Details
          if (!residentialData.propertyDetails?.propertyType) {
            errors.push('Property type is required');
          }
          if (!residentialData.propertyDetails?.squareFootage || residentialData.propertyDetails.squareFootage <= 0) {
            errors.push('Valid square footage is required');
          }
          if (!residentialData.propertyDetails?.propertyAddress) {
            errors.push('Property address is required');
          }
          if (!residentialData.propertyDetails?.bedrooms || residentialData.propertyDetails.bedrooms < 0) {
            errors.push('Number of bedrooms is required');
          }
          if (!residentialData.propertyDetails?.bathrooms || residentialData.propertyDetails.bathrooms < 0) {
            errors.push('Number of bathrooms is required');
          }
          break;

        case 2: // Service Preferences
          if (!residentialData.servicePreferences?.frequency) {
            errors.push('Service frequency is required');
          }
          if (!residentialData.servicePreferences?.preferredTime) {
            errors.push('Preferred time is required');
          }
          if (!residentialData.servicePreferences?.cleaningFocus || 
              residentialData.servicePreferences.cleaningFocus.length === 0) {
            errors.push('Please select at least one cleaning focus area');
          }
          break;

        case 3: // Schedule
          if (!residentialData.schedule?.date) {
            errors.push('Service date is required');
          } else {
            const selectedDate = new Date(residentialData.schedule.date);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            if (selectedDate < today) {
              errors.push('Service date cannot be in the past');
            }
          }
          if (!residentialData.schedule?.timeSlot) {
            errors.push('Time slot is required');
          }
          break;

        case 4: // Contact
          if (!residentialData.contact?.fullName) {
            errors.push('Full name is required');
          }
          if (!residentialData.contact?.email) {
            errors.push('Email address is required');
          } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(residentialData.contact.email)) {
            errors.push('Please enter a valid email address');
          }
          if (!residentialData.contact?.phone) {
            errors.push('Phone number is required');
          } else if (!/^\(\d{3}\) \d{3}-\d{4}$/.test(residentialData.contact.phone)) {
            errors.push('Please enter a valid phone number format: (XXX) XXX-XXXX');
          }
          if (!residentialData.contact?.preferredContact) {
            errors.push('Preferred contact method is required');
          }
          break;
      }
      
      return { isValid: errors.length === 0, errors };
    }

    // Check if it's a window cleaning form
    if (formData && typeof formData === 'object' && 'windowDetails' in formData && 'accessDetails' in formData) {
      const windowData = formData as WindowFormData;
      
      switch (step) {
        case 0: // Service Type
          if (!windowData.serviceType) {
            errors.push('Please select a service type');
          }
          break;

        case 1: // Property Details
          if (!windowData.propertyDetails?.propertyType) {
            errors.push('Property type is required');
          }
          if (!windowData.propertyDetails?.industryType) {
            errors.push('Industry type is required');
          }
          if (!windowData.propertyDetails?.propertyAddress) {
            errors.push('Property address is required');
          }
          if (!windowData.propertyDetails?.squareFootage || windowData.propertyDetails.squareFootage <= 0) {
            errors.push('Valid square footage is required');
          }
          break;

        case 2: // Window Details
          if (!windowData.windowDetails?.windowTypes || windowData.windowDetails.windowTypes.length === 0) {
            errors.push('Please select at least one window type');
          }
          if (!windowData.windowDetails?.serviceScope) {
            errors.push('Service scope is required');
          }
          if (!windowData.windowDetails?.buildingHeight) {
            errors.push('Building height is required');
          }
          break;

        case 3: // Access Details
          if (!windowData.accessDetails?.heightAccess) {
            errors.push('Height access information is required');
          }
          if (!windowData.accessDetails?.workingHours) {
            errors.push('Working hours are required');
          }
          if (!windowData.accessDetails?.accessInstructions) {
            errors.push('Access instructions are required');
          }
          break;

        case 4: // Schedule
          if (!windowData.schedule?.date) {
            errors.push('Service date is required');
          } else {
            const selectedDate = new Date(windowData.schedule.date);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            if (selectedDate < today) {
              errors.push('Service date cannot be in the past');
            }
          }
          if (!windowData.schedule?.timeSlot) {
            errors.push('Time slot is required');
          }
          break;

        case 5: // Contact
          if (!windowData.contact?.fullName) {
            errors.push('Full name is required');
          }
          if (!windowData.contact?.email) {
            errors.push('Email address is required');
          } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(windowData.contact.email)) {
            errors.push('Please enter a valid email address');
          }
          if (!windowData.contact?.phone) {
            errors.push('Phone number is required');
          } else if (!/^\(\d{3}\) \d{3}-\d{4}$/.test(windowData.contact.phone)) {
            errors.push('Please enter a valid phone number format: (XXX) XXX-XXXX');
          }
          if (!windowData.contact?.companyName) {
            errors.push('Company name is required');
          }
          break;
      }
      
      return { isValid: errors.length === 0, errors };
    }

    // Check if it's a carpet cleaning form
    if (formData && typeof formData === 'object' && 'carpetDetails' in formData && 'stainTreatment' in formData) {
      const carpetData = formData as CarpetFormData;
      
      switch (step) {
        case 0: // Service Type
          if (!carpetData.serviceType) {
            errors.push('Please select a service type');
          }
          break;

        case 1: // Property Details
          if (!carpetData.propertyDetails?.propertyType) {
            errors.push('Property type is required');
          }
          if (!carpetData.propertyDetails?.industryType) {
            errors.push('Industry type is required');
          }
          if (!carpetData.propertyDetails?.propertyAddress) {
            errors.push('Property address is required');
          }
          if (!carpetData.propertyDetails?.squareFootage || carpetData.propertyDetails.squareFootage <= 0) {
            errors.push('Valid square footage is required');
          }
          break;

        case 2: // Carpet Details
          if (!carpetData.carpetDetails?.material) {
            errors.push('Carpet material is required');
          }
          if (!carpetData.carpetDetails?.condition) {
            errors.push('Current condition is required');
          }
          if (!carpetData.carpetDetails?.age) {
            errors.push('Carpet age is required');
          }
          if (!carpetData.carpetDetails?.traffic) {
            errors.push('Traffic level is required');
          }
          break;

        case 3: // Stain Treatment
          // Optional section, no required fields
          break;

        case 4: // Schedule
          if (!carpetData.schedule?.date) {
            errors.push('Service date is required');
          } else {
            const selectedDate = new Date(carpetData.schedule.date);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            if (selectedDate < today) {
              errors.push('Service date cannot be in the past');
            }
          }
          if (!carpetData.schedule?.timeSlot) {
            errors.push('Time slot is required');
          }
          break;

        case 5: // Contact
          if (!carpetData.contact?.fullName) {
            errors.push('Full name is required');
          }
          if (!carpetData.contact?.email) {
            errors.push('Email address is required');
          } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(carpetData.contact.email)) {
            errors.push('Please enter a valid email address');
          }
          if (!carpetData.contact?.phone) {
            errors.push('Phone number is required');
          } else if (!/^\(\d{3}\) \d{3}-\d{4}$/.test(carpetData.contact.phone)) {
            errors.push('Please enter a valid phone number format: (XXX) XXX-XXXX');
          }
          if (!carpetData.contact?.companyName) {
            errors.push('Company name is required');
          }
          break;
      }
      
      return { isValid: errors.length === 0, errors };
    }

    // Check if it's a construction cleaning form
    if (formData && typeof formData === 'object' && 'cleaningScope' in formData && 'debrisDetails' in formData) {
      const constructionData = formData as ConstructionFormData;
      
      switch (step) {
        case 0: // Service Type
          if (!constructionData.serviceType) {
            errors.push('Please select a service type');
          }
          break;

        case 1: // Property Details
          if (!constructionData.propertyDetails?.propertyType) {
            errors.push('Property type is required');
          }
          if (!constructionData.propertyDetails?.industryType) {
            errors.push('Industry type is required');
          }
          if (!constructionData.propertyDetails?.propertyAddress) {
            errors.push('Property address is required');
          }
          if (!constructionData.propertyDetails?.squareFootage || constructionData.propertyDetails.squareFootage <= 0) {
            errors.push('Valid square footage is required');
          }
          break;

        case 2: // Cleaning Scope
          if (!constructionData.cleaningScope?.phase) {
            errors.push('Cleaning phase is required');
          }
          if (!constructionData.cleaningScope?.areas || constructionData.cleaningScope.areas.length === 0) {
            errors.push('Please select at least one area to clean');
          }
          break;

        case 3: // Debris Details
          if (!constructionData.debrisDetails?.debrisTypes || constructionData.debrisDetails.debrisTypes.length === 0) {
            errors.push('Please select at least one debris type');
          }
          break;

        case 4: // Schedule
          if (!constructionData.schedule?.date) {
            errors.push('Service date is required');
          } else {
            const selectedDate = new Date(constructionData.schedule.date);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            if (selectedDate < today) {
              errors.push('Service date cannot be in the past');
            }
          }
          if (!constructionData.schedule?.timeSlot) {
            errors.push('Time slot is required');
          }
          if (!constructionData.schedule?.projectDeadline) {
            errors.push('Project deadline is required');
          }
          break;

        case 5: // Contact
          if (!constructionData.contact?.fullName) {
            errors.push('Full name is required');
          }
          if (!constructionData.contact?.email) {
            errors.push('Email address is required');
          } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(constructionData.contact.email)) {
            errors.push('Please enter a valid email address');
          }
          if (!constructionData.contact?.phone) {
            errors.push('Phone number is required');
          } else if (!/^\(\d{3}\) \d{3}-\d{4}$/.test(constructionData.contact.phone)) {
            errors.push('Please enter a valid phone number format: (XXX) XXX-XXXX');
          }
          if (!constructionData.contact?.companyName) {
            errors.push('Company name is required');
          }
          if (!constructionData.contact?.role) {
            errors.push('Role is required');
          }
          if (!constructionData.contact?.preferredContact) {
            errors.push('Preferred contact method is required');
          }
          break;
      }
      
      return { isValid: errors.length === 0, errors };
    }

    // Generic validation for other form types
    switch (step) {
      case 0: // Service Type
        if (!formData.serviceType) {
          errors.push('Please select a service type');
        }
        break;

      case 1: // Property Details
        if (!formData.propertyDetails?.propertyType) {
          errors.push('Property type is required');
        }
        if (!formData.propertyDetails?.industryType) {
          errors.push('Industry type is required');
        }
        if (!formData.propertyDetails?.propertyAddress) {
          errors.push('Property address is required');
        }
        if (!formData.propertyDetails?.squareFootage || formData.propertyDetails.squareFootage <= 0) {
          errors.push('Valid square footage is required');
        }
        break;

      case 4: // Schedule
        if (!formData.schedule?.date) {
          errors.push('Service date is required');
        } else {
          const selectedDate = new Date(formData.schedule.date);
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          
          if (selectedDate < today) {
            errors.push('Service date cannot be in the past');
          }
        }
        if (!formData.schedule?.timeSlot) {
          errors.push('Time slot is required');
        }
        break;

      case 5: // Contact
        if (!formData.contact?.fullName) {
          errors.push('Full name is required');
        }
        if (!formData.contact?.email) {
          errors.push('Email address is required');
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.contact.email)) {
          errors.push('Please enter a valid email address');
        }
        if (!formData.contact?.phone) {
          errors.push('Phone number is required');
        } else if (!/^\(\d{3}\) \d{3}-\d{4}$/.test(formData.contact.phone)) {
          errors.push('Please enter a valid phone number format: (XXX) XXX-XXXX');
        }
        break;
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  return { validateStep };
}