// Form data types for better type safety

export interface FormData {
  [key: string]: string | number | boolean | string[] | FormData;
}

export interface ContactInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  companyName?: string;
}

export interface PropertyDetails {
  address: string;
  city: string;
  state: string;
  zipCode: string;
  propertyType: string;
  squareFootage?: number;
  floors?: number;
  rooms?: number;
  bathrooms?: number;
  accessInstructions?: string;
}

export interface ServiceScheduling {
  preferredDate: string;
  preferredTime: string;
  frequency?: string;
  urgency?: string;
  flexibleScheduling?: boolean;
}

export interface ServiceDetails {
  serviceType: string;
  description?: string;
  specialRequests?: string;
  estimatedDuration?: number;
  priority?: 'low' | 'medium' | 'high';
}

export interface CarpetCleaningDetails {
  carpetType: string;
  roomCount: number;
  totalSquareFootage: number;
  stainTreatment: boolean;
  petOdorTreatment: boolean;
  protectionTreatment: boolean;
  lastCleaning?: string;
  heavyTrafficAreas?: string[];
}

export interface WindowCleaningDetails {
  windowCount: number;
  floors: number;
  interiorCleaning: boolean;
  exteriorCleaning: boolean;
  screenCleaning: boolean;
  sillCleaning: boolean;
  accessType: string;
  windowTypes: string[];
}

export interface OfficeCleaningDetails {
  officeType: string;
  employeeCount: number;
  workstations: number;
  meetingRooms: number;
  restrooms: number;
  kitchenAreas: number;
  specialAreas: string[];
  cleaningFrequency: string;
}

export interface PressureWashingDetails {
  surfaceTypes: string[];
  totalArea: number;
  surfaceCondition: string;
  cleaningType: string;
  accessibilityNotes?: string;
}

export interface SanitizationDetails {
  sanitizationType: string;
  areaTypes: string[];
  frequency: string;
  specialRequirements: string[];
  certificationNeeded: boolean;
}

export interface FloorRestorationDetails {
  floorType: string;
  totalArea: number;
  condition: string;
  restorationScope: string[];
  finishType?: string;
  trafficLevel: string;
}

export interface TileGroutDetails {
  tileType: string;
  groutCondition: string;
  totalArea: number;
  sealingRequired: boolean;
  colorRestoration: boolean;
  problemAreas: string[];
}

export interface DeepCleaningDetails {
  cleaningScope: string[];
  roomTypes: string[];
  specialFocus: string[];
  frequency: string;
  additionalServices: string[];
}

export interface ConstructionCleaningDetails {
  constructionType: string;
  projectStage: string;
  debrisTypes: string[];
  cleaningScope: string[];
  safetyRequirements: string[];
  timeline: string;
}

// Combined form data interface
export interface ServiceFormData extends ContactInfo, PropertyDetails, ServiceScheduling, ServiceDetails {
  // Service-specific details (only one will be populated based on service type)
  carpetCleaning?: CarpetCleaningDetails;
  windowCleaning?: WindowCleaningDetails;
  officeCleaning?: OfficeCleaningDetails;
  pressureWashing?: PressureWashingDetails;
  sanitization?: SanitizationDetails;
  floorRestoration?: FloorRestorationDetails;
  tileGrout?: TileGroutDetails;
  deepCleaning?: DeepCleaningDetails;
  constructionCleaning?: ConstructionCleaningDetails;
  
  // Additional metadata
  submittedAt?: string;
  estimatedCost?: number;
  status?: 'draft' | 'submitted' | 'processing' | 'confirmed' | 'completed';
}

// Event handler types
export interface FormFieldProps {
  formData: ServiceFormData;
  updateFormData: (updates: Partial<ServiceFormData>) => void;
  errors?: Record<string, string>;
}

export interface FormStepProps extends FormFieldProps {
  onNext?: () => void;
  onBack?: () => void;
  isValid?: boolean;
}

// API response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface BookingResponse {
  id: string;
  confirmationNumber: string;
  estimatedCost: number;
  scheduledDate: string;
  status: string;
}

// Payment related types
export interface PaymentData {
  amount: number;
  currency?: string;
  description?: string;
  customerEmail?: string;
  customerName?: string;
  serviceType?: string;
  metadata?: Record<string, unknown>;
}

export interface PaymentResponse {
  success: boolean;
  paymentLink?: PaymentLink | string;
  paymentId?: string;
  error?: string;
}

export interface PaymentLink {
  id: string;
  url: string;
  orderId?: string;
  createdAt?: string;
  expiresAt?: string;
}

export interface PaymentResult {
  success: boolean;
  transactionId?: string;
  error?: string;
  redirectUrl?: string;
  payment?: unknown;
}

export interface ProcessPaymentParams {
  sourceId: string;
  amount: number;
  currency?: string;
  buyerVerificationToken?: string;
  orderId?: string;
  customerEmail?: string;
  customerName?: string;
  locationId?: string;
  idempotencyKey?: string;
}

export interface PaymentFormData {
  amount: number;
  description: string;
  customerEmail?: string;
  formData: ServiceFormData;
  serviceType?: string;
  orderId?: string;
}
