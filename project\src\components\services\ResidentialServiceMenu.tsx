import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  X, ArrowRight, Star, Shield, Clock,
  Home, Sparkles, Truck, Construction, Calendar, Brush,
  GlassWater, Waves, Droplets, Flame, Sofa
} from 'lucide-react';

interface ResidentialServiceMenuProps {
  isOpen: boolean;
  onClose: () => void;
  onServiceSelect: (serviceId: string) => void;
}

export function ResidentialServiceMenu({
  isOpen,
  onClose,
  onServiceSelect: _onServiceSelect
}: ResidentialServiceMenuProps) {
  const navigate = useNavigate();

  const services = [
    {
      id: 'regular',
      icon: Home,
      title: 'Regular Cleaning',
      description: 'Comprehensive home cleaning service',
      features: ['All living areas', 'Kitchen & bathrooms', 'Dusting & vacuuming']
    },
    {
      id: 'deep',
      icon: Sparkles,
      title: 'Deep Cleaning',
      description: 'Thorough deep cleaning service',
      features: ['Detailed cleaning', 'Hard to reach areas', 'Baseboards & trim']
    },
    {
      id: 'move',
      icon: Truck,
      title: 'Move In/Out',
      description: 'Specialized moving cleaning service',
      features: ['Cabinet cleaning', 'Appliance cleaning', 'Window cleaning']
    },
    {
      id: 'construction',
      icon: Construction,
      title: 'Post Construction',
      description: 'After construction cleanup',
      features: ['Debris removal', 'Dust control', 'Surface cleaning']
    },
    {
      id: 'event',
      icon: Calendar,
      title: 'Event Cleaning',
      description: 'Pre and post event cleaning',
      features: ['Setup assistance', 'Post-event cleanup', 'Quick turnaround']
    },
    {
      id: 'carpet',
      icon: Brush,
      title: 'Carpet Cleaning',
      description: 'Professional carpet cleaning',
      features: ['Deep extraction', 'Stain removal', 'Deodorizing']
    },
    {
      id: 'upholstery',
      icon: Sofa,
      title: 'Upholstery Cleaning',
      description: 'Furniture and upholstery cleaning',
      features: ['Sofa cleaning', 'Chair cleaning', 'Stain removal']
    },
    {
      id: 'window',
      icon: GlassWater,
      title: 'Window Cleaning',
      description: 'Interior & exterior window cleaning',
      features: ['Streak-free cleaning', 'Frame cleaning', 'Screen cleaning']
    },
    {
      id: 'pressure',
      icon: Waves,
      title: 'Pressure Washing',
      description: 'Exterior surface cleaning',
      features: ['Driveway cleaning', 'Deck/patio cleaning', 'Siding cleaning']
    },
    {
      id: 'sanitization',
      icon: Droplets,
      title: 'Sanitization & Disinfection',
      description: 'Deep sanitization services',
      features: ['Surface disinfection', 'High-touch areas', 'Odor elimination']
    },
    {
      id: 'pool',
      icon: Droplets,
      title: 'Pool Cleaning (Subscription ONLY)',
      description: 'Regular pool maintenance service',
      features: ['Water testing', 'Chemical balancing', 'Filter cleaning']
    },
    {
      id: 'chimney',
      icon: Flame,
      title: 'Chimney Cleaning',
      description: 'Subscription or one-time service',
      features: ['Soot removal', 'Creosote removal', 'Safety inspection']
    }
  ];

  const handleServiceSelect = (serviceId: string) => {
    onClose();
    navigate(`/residential/${serviceId}?skip=1`);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50"
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ type: "spring", duration: 0.5 }}
            className="fixed inset-0 z-50 overflow-y-auto"
          >
            <div className="min-h-screen px-4 text-center flex items-center justify-center">
              <div className="relative w-full max-w-6xl bg-white rounded-3xl shadow-2xl p-8">
                {/* Close Button */}
                <motion.button
                  whileHover={{ rotate: 90 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={onClose}
                  className="absolute top-4 right-4 p-2 rounded-full hover:bg-gray-100"
                >
                  <X className="w-6 h-6 text-gray-500" />
                </motion.button>

                {/* Header */}
                <div className="text-center mb-12">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    <h2 className="text-3xl font-bold text-gray-900 mb-4">
                      Residential Cleaning Services
                    </h2>
                    <p className="text-lg text-gray-600">
                      Choose from our professional home cleaning solutions
                    </p>
                  </motion.div>

                  {/* Trust Badges */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="flex justify-center gap-6 mt-6"
                  >
                    <div className="flex items-center space-x-1">
                      <Shield className="w-5 h-5 text-brand-600" />
                      <span className="text-sm text-gray-600">Licensed & Insured</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-5 h-5 text-brand-600" />
                      <span className="text-sm text-gray-600">24/7 Service</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Star className="w-5 h-5 text-yellow-400 fill-current" />
                      <span className="text-sm text-gray-600">4.9/5 Rating</span>
                    </div>
                  </motion.div>
                </div>

                {/* Services Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {services.map((service, index) => {
                    const Icon = service.icon;
                    
                    return (
                      <motion.button
                        key={service.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 + (index * 0.1) }}
                        onClick={() => handleServiceSelect(service.id)}
                        className="group relative w-full p-6 rounded-xl text-left transition-all 
                                 bg-white border border-gray-200 
                                 hover:border-brand-300 hover:shadow-lg
                                 active:scale-[0.98] touch-manipulation"
                      >
                        <div className="flex flex-col">
                          {/* Icon */}
                          <div className="p-3 rounded-xl bg-brand-100 mb-4 w-fit">
                            <Icon className="w-6 h-6 text-brand-600" />
                          </div>

                          {/* Content */}
                          <h3 className="text-xl font-semibold text-gray-900 mb-2">{service.title}</h3>
                          <p className="text-gray-600 mb-4">{service.description}</p>

                          {/* Features */}
                          <div className="space-y-2 mb-6">
                            {service.features.map((feature, i) => (
                              <div key={i} className="flex items-center text-gray-600">
                                <div className="w-1.5 h-1.5 rounded-full bg-brand-500 mr-2" />
                                <span className="text-sm">{feature}</span>
                              </div>
                            ))}
                          </div>

                          {/* Action Button */}
                          <div className="flex items-center justify-between mt-auto">
                            <span className="font-medium text-brand-600">
                              Get Started
                            </span>
                            <ArrowRight className="w-5 h-5 text-brand-600 
                                               group-hover:translate-x-1 transition-transform" />
                          </div>
                        </div>
                      </motion.button>
                    );
                  })}
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}