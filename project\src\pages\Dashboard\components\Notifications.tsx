import React from 'react';
import { motion } from 'framer-motion';
import {
  Calendar, FileText, MessageSquare,
  AlertCircle, CheckCircle, Clock, Settings,
  X
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';

const notifications = [
  {
    id: 1,
    type: 'reminder',
    title: 'Upcoming Service',
    message: 'Your next cleaning service is scheduled for tomorrow at 9:00 AM',
    date: '2024-01-16T09:00:00',
    read: false,
    icon: Calendar,
    color: 'text-blue-600 bg-blue-50'
  },
  {
    id: 2,
    type: 'invoice',
    title: 'New Invoice Available',
    message: 'Your invoice for January services is ready for review',
    date: '2024-01-15T14:30:00',
    read: false,
    icon: FileText,
    color: 'text-green-600 bg-green-50'
  },
  {
    id: 3,
    type: 'message',
    title: 'New Message',
    message: 'Support team: Your request has been processed',
    date: '2024-01-15T11:20:00',
    read: true,
    icon: MessageSquare,
    color: 'text-purple-600 bg-purple-50'
  },
  {
    id: 4,
    type: 'alert',
    title: 'Service Update',
    message: 'Schedule change request confirmed for next week',
    date: '2024-01-14T16:45:00',
    read: true,
    icon: AlertCircle,
    color: 'text-yellow-600 bg-yellow-50'
  }
];

export function Notifications() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <h2 className="text-2xl font-semibold text-gray-900">Notifications</h2>
          <span className="px-2.5 py-0.5 rounded-full text-sm font-medium bg-brand-100 text-brand-600">
            {notifications.filter(n => !n.read).length} New
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </Button>
          <Button size="sm">
            Mark All Read
          </Button>
        </div>
      </div>

      {/* Notifications List */}
      <div className="space-y-4">
        {notifications.map((notification, index) => {
          const Icon = notification.icon;
          
          return (
            <motion.div
              key={notification.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`relative bg-white rounded-xl shadow-sm hover:shadow-md transition-all p-6 
                         ${notification.read ? 'opacity-75' : ''}`}
            >
              {!notification.read && (
                <div className="absolute top-4 right-4 w-2 h-2 rounded-full bg-brand-500" />
              )}
              
              <div className="flex items-start space-x-4">
                <div className={`p-2 rounded-lg ${notification.color}`}>
                  <Icon className="w-5 h-5" />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h3 className="text-lg font-medium text-gray-900">
                      {notification.title}
                    </h3>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-500">
                        {new Date(notification.date).toLocaleTimeString([], { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })}
                      </span>
                      <button className="p-1 hover:bg-gray-100 rounded-lg">
                        <X className="w-4 h-4 text-gray-400" />
                      </button>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 mb-2">
                    {notification.message}
                  </p>
                  
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center text-sm text-gray-500">
                      <Clock className="w-4 h-4 mr-1" />
                      {new Date(notification.date).toLocaleDateString()}
                    </div>
                    {!notification.read && (
                      <Button variant="outline" size="sm">
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Mark as Read
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Load More */}
      <div className="text-center">
        <Button variant="outline">
          Load More Notifications
        </Button>
      </div>
    </div>
  );
}