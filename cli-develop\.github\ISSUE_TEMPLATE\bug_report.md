---
name: Bug report
about: Create a report to help us improve
title: ''
labels: ''
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**System information**
Rerun the failing command with `--create-ticket` flag.
 - Ticket ID: [e.g. ab1ac733e31e4f928a4d7c8402543712]
 - Version of OS: [e.g. Ubuntu 22.04]
 - Version of CLI: [e.g. v1.60.0]
 - Version of Docker: [e.g. v25.0.3]
 - Versions of services: [output from `supabase services` command]

**Additional context**
If applicable, add any other context about the problem here.
 - Browser [e.g. chrome, safari]
 - Version of supabase-js [e.g. v2.22.0]
 - Version of Node.js [e.g. v16.20.0]
