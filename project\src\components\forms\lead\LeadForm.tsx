import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { ServiceSelection } from './steps/ServiceSelection';
import { FrequencySelection } from './steps/FrequencySelection';
import { AdditionalServices } from './steps/AdditionalServices';
import { OfficeDetails } from './steps/OfficeDetails';
import { ScheduleSelection } from './steps/ScheduleSelection';
import { ContactInfo } from './steps/ContactInfo';
import { Summary } from './steps/Summary';
import { ProgressBar } from './ui/ProgressBar';
import { initialFormData } from './types/form';
import type { FormData } from './types';
import { submitLead } from '../../../lib/api/leads';
import { useAuth } from '../../../lib/auth/AuthProvider';

interface LeadFormProps {
  onBack: () => void;
}

const steps = [
  'Services',
  'Frequency',
  'Additional',
  'Office',
  'Schedule',
  'Contact',
  'Review'
];

export function LeadForm({ onBack: _onBack }: LeadFormProps) {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<FormData>(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const nextStep = () => setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
  const prevStep = () => setCurrentStep((prev) => Math.max(prev - 1, 0));

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      setError(null);
      
      // Prepare lead data
      const leadData = {
        full_name: formData.contact.name,
        email: formData.contact.email,
        phone: formData.contact.phone,
        company_name: formData.contact.company || formData.officeDetails.businessName,
        zip_code: formData.officeDetails.zipCode || '00000',
        square_footage: parseInt(formData.officeDetails.squareFootage) || undefined,
        service_interests: formData.services,
        service_frequency: formData.frequency,
        preferred_start_date: formData.schedule.preferredDate,
        preferred_contact_method: formData.contact.contactMethod || 'any',
        notes: formData.contact.message,
        property_type: formData.officeDetails.propertyType,
        industry_type: formData.officeDetails.industryType,
        special_requirements: formData.additionalServices,
        custom_requests: formData.contact.message
      };
      
      await submitLead(leadData, user);
      navigate('/thank-you', { state: { formData } });
    } catch (err) {
      console.error('Error submitting lead:', err);
      setError(err instanceof Error ? err.message : 'Failed to submit lead');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-brand-100">
      <main className="relative pt-24 pb-12 px-4">
        <div className="max-w-3xl mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-6 sm:p-8">
            <ProgressBar 
              steps={steps} 
              currentStep={currentStep}
              color="#93C572"
            />
            
            <div className="mt-8 min-h-[400px]">
              {error && (
                <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg text-red-600">
                  {error}
                </div>
              )}
              
              <div className="transition-opacity duration-300">
                {currentStep === 0 && (
                  <ServiceSelection
                    selected={formData.services}
                    onChange={(services) => setFormData({ ...formData, services })}
                  />
                )}
                {currentStep === 1 && (
                  <FrequencySelection
                    selected={formData.frequency}
                    onChange={(frequency) => setFormData({ ...formData, frequency })}
                  />
                )}
                {currentStep === 2 && (
                  <AdditionalServices
                    selected={formData.additionalServices}
                    onChange={(additionalServices) => setFormData({ ...formData, additionalServices })}
                  />
                )}
                {currentStep === 3 && (
                  <OfficeDetails
                    details={formData.officeDetails}
                    onChange={(officeDetails) => setFormData({ ...formData, officeDetails })}
                  />
                )}
                {currentStep === 4 && (
                  <ScheduleSelection
                    schedule={formData.schedule}
                    onChange={(schedule) => setFormData({ ...formData, schedule })}
                  />
                )}
                {currentStep === 5 && (
                  <ContactInfo
                    contact={formData.contact}
                    onChange={(contact) => setFormData({ ...formData, contact })}
                  />
                )}
                {currentStep === 6 && (
                  <Summary
                    formData={formData}
                    onSubmit={handleSubmit}
                    isSubmitting={isSubmitting}
                  />
                )}
              </div>

              <div className="mt-8 flex justify-between">
                {currentStep > 0 && (
                  <button
                    onClick={prevStep}
                    disabled={isSubmitting}
                    className="px-6 py-2 text-gray-600 hover:text-gray-900 transition-colors flex items-center touch-manipulation disabled:opacity-50"
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back
                  </button>
                )}
                {currentStep < steps.length - 1 && (
                  <button
                    onClick={nextStep}
                    disabled={isSubmitting}
                    className="ml-auto px-6 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 
                             transition-all hover:shadow-lg transform hover:-translate-y-0.5
                             touch-manipulation disabled:opacity-50"
                  >
                    Continue
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}