import React from 'react';
import { motion } from 'framer-motion';
import {
  Calendar, MapPin, Clock, Building2,
  Star, Download, FileText,
  ArrowRight, Filter, Search
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';

const serviceHistory = [
  {
    id: 1,
    serviceType: 'Office Cleaning',
    date: '2024-01-10',
    location: '123 Business Ave, Suite 100',
    status: 'completed',
    rating: 5,
    price: '$209.99',
    report: true,
    notes: 'Excellent service as always'
  },
  {
    id: 2,
    serviceType: 'Deep Cleaning',
    date: '2023-12-15',
    location: '456 Corporate Blvd',
    status: 'completed',
    rating: 5,
    price: '$419.99',
    report: true,
    notes: 'Thorough deep cleaning of all areas'
  },
  {
    id: 3,
    serviceType: 'Window Cleaning',
    date: '2023-11-20',
    location: '789 Office Park Dr',
    status: 'completed',
    rating: 4,
    price: '$159.99',
    report: true,
    notes: 'Interior and exterior windows cleaned'
  }
];

export function ServiceHistory() {
  return (
    <div className="space-y-6">
      {/* Header with Actions */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h2 className="text-2xl font-semibold text-gray-900">Service History</h2>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <FileText className="w-4 h-4 mr-2" />
            Export History
          </Button>
        </div>
      </div>

      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
        <input
          type="text"
          placeholder="Search service history..."
          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
        />
      </div>

      {/* Service History List */}
      <div className="space-y-4">
        {serviceHistory.map((service, index) => (
          <motion.div
            key={service.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow p-6"
          >
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div className="space-y-4 md:space-y-2">
                <div className="flex items-center space-x-3">
                  <Building2 className="w-5 h-5 text-brand-600" />
                  <h3 className="text-lg font-medium text-gray-900">
                    {service.serviceType}
                  </h3>
                  <div className="flex items-center">
                    {[...Array(service.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                    ))}
                  </div>
                </div>

                <div className="flex flex-wrap gap-4">
                  <div className="flex items-center space-x-2 text-gray-600">
                    <Calendar className="w-4 h-4" />
                    <span>{new Date(service.date).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-600">
                    <MapPin className="w-4 h-4" />
                    <span>{service.location}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-gray-600">
                    <Clock className="w-4 h-4" />
                    <span>Completed</span>
                  </div>
                </div>

                <p className="text-sm text-gray-600">{service.notes}</p>
              </div>

              <div className="mt-4 md:mt-0 flex items-center space-x-4">
                <div className="text-lg font-semibold text-gray-900">
                  {service.price}
                </div>
                <div className="space-x-2">
                  {service.report && (
                    <Button variant="outline" size="sm">
                      <Download className="w-4 h-4 mr-2" />
                      Report
                    </Button>
                  )}
                  <Button size="sm">
                    Book Again
                    <ArrowRight className="ml-2 w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Pagination */}
      <div className="flex justify-center mt-8">
        <nav className="flex items-center space-x-2">
          <button className="px-3 py-1 rounded-lg border border-gray-300 text-gray-600 hover:bg-gray-50">
            Previous
          </button>
          <button className="px-3 py-1 rounded-lg bg-brand-50 text-brand-600 border border-brand-200">
            1
          </button>
          <button className="px-3 py-1 rounded-lg border border-gray-300 text-gray-600 hover:bg-gray-50">
            2
          </button>
          <button className="px-3 py-1 rounded-lg border border-gray-300 text-gray-600 hover:bg-gray-50">
            3
          </button>
          <button className="px-3 py-1 rounded-lg border border-gray-300 text-gray-600 hover:bg-gray-50">
            Next
          </button>
        </nav>
      </div>
    </div>
  );
}