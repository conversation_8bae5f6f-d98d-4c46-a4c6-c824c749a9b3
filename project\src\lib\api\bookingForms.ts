import { supabase, isSupabaseConfigured } from '../supabase/client';
import type { User } from '@supabase/supabase-js';
import type { ServiceFormData } from '../../types/forms';

export async function submitBookingForm(formData: ServiceFormData, serviceType: string, user: User | null) {
  try {
    if (!isSupabaseConfigured) {
      throw new Error('Database connection not configured. Please connect to Supabase first.');
    }

    if (!supabase) {
      throw new Error('Supabase client is not initialized');
    }

    // Prepare the request data
    const requestData = {
      user_id: user?.id || null,
      service_type: serviceType,
      property_details: formData.propertyDetails || {},
      service_details: getServiceSpecificDetails(formData, serviceType),
      schedule: formData.schedule || {},
      contact: formData.contact || {},
      status: 'pending'
    };

    console.log('Submitting booking form:', requestData);

    // Insert the booking using Supabase client
    const { data, error } = await supabase
      .from('booking_forms')
      .insert([requestData])
      .select();

    if (error) {
      console.error('Supabase error:', error);
      
      // Check if the error is related to the http_post function
      if (error.message && (
        error.message.includes('http_post') || 
        error.message.includes('webhook') ||
        error.message.includes('extension "http"')
      )) {
        // The booking was likely created but the webhook notification failed
        // Try to fetch the booking to confirm
        const { data: bookingData, error: fetchError } = await supabase
          .from('booking_forms')
          .select()
          .match({ 
            user_id: user?.id,
            service_type: serviceType
          })
          .order('created_at', { ascending: false })
          .limit(1);
          
        if (!fetchError && bookingData && bookingData.length > 0) {
          // Booking was created successfully despite webhook error
          console.log('Booking created successfully despite webhook error:', bookingData[0]);
          
          // Try to manually send the webhook
          try {
            // Format the data according to the required format
            const webhookPayload = {
              id: bookingData[0].id,
              status: 'sent',
              service_type: bookingData[0].service_type,
              property_details: bookingData[0].property_details,
              schedule: bookingData[0].schedule,
              contact: bookingData[0].contact,
              created_at: bookingData[0].created_at
            };
            
            const response = await fetch('https://mohhzaman.app.n8n.cloud/webhook/22aa5359-f353-4f62-8558-03d475bdade9', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(webhookPayload)
            });
            
            if (response.ok) {
              console.log('Manual webhook notification sent successfully');
            } else {
              console.error('Manual webhook notification failed with status:', response.status);
            }
          } catch (webhookError) {
            console.error('Failed to send manual webhook notification:', webhookError);
          }
          
          return bookingData[0];
        }
        
        // If we couldn't confirm the booking was created, show a more helpful error
        throw new Error('Your booking was received, but we encountered an issue with our notification system. Your booking is still being processed. You can check your dashboard for updates.');
      }
      
      throw new Error(error.message || 'Failed to submit request');
    }

    if (!data || data.length === 0) {
      throw new Error('No data returned from booking submission');
    }

    // If the booking was created successfully, try to manually send the webhook as a backup
    try {
      // Format the data according to the required format
      const webhookPayload = {
        id: data[0].id,
        status: 'sent',
        service_type: data[0].service_type,
        property_details: data[0].property_details,
        schedule: data[0].schedule,
        contact: data[0].contact,
        created_at: data[0].created_at
      };
      
      const response = await fetch('https://mohhzaman.app.n8n.cloud/webhook/22aa5359-f353-4f62-8558-03d475bdade9', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(webhookPayload)
      });
      
      if (response.ok) {
        console.log('Backup webhook notification sent successfully');
      } else {
        console.error('Backup webhook notification failed with status:', response.status);
      }
    } catch (webhookError) {
      console.error('Failed to send backup webhook notification:', webhookError);
      // Don't throw an error here, as the booking was still created successfully
    }

    return data[0];
  } catch (error) {
    console.error('Error submitting booking form:', error);
    throw error;
  }
}

// Helper function to extract service-specific details based on form type
function getServiceSpecificDetails(formData: ServiceFormData, serviceType: string): Record<string, unknown> {
  const details: Record<string, unknown> = {};

  switch (serviceType) {
    case 'window':
      details.windowDetails = formData.windowDetails || {};
      details.accessDetails = formData.accessDetails || {};
      break;
      
    case 'carpet':
      details.carpetDetails = formData.carpetDetails || {};
      details.stainTreatment = formData.stainTreatment || {};
      break;
      
    case 'residential-carpet':
      details.carpetDetails = formData.carpetDetails || {};
      details.stainTreatment = formData.stainTreatment || {};
      break;
      
    case 'sanitization':
      details.protocolDetails = formData.protocolDetails || {};
      details.sanitizationScope = formData.sanitizationScope || {};
      break;
      
    case 'construction':
      details.projectDetails = formData.propertyDetails || {};
      details.cleaningScope = formData.cleaningScope || {};
      details.debrisDetails = formData.debrisDetails || {};
      break;
      
    case 'tile':
      details.tileDetails = formData.tileDetails || {};
      details.groutCondition = formData.groutCondition || {};
      break;
      
    case 'deep':
      details.cleaningScope = formData.cleaningScope || {};
      details.additionalServices = formData.services?.types || [];
      break;
      
    case 'floor':
      details.floorDetails = formData.floorDetails || {};
      details.restorationScope = formData.restorationScope || {};
      break;
      
    case 'pressure':
      details.surfaceDetails = formData.surfaceDetails || {};
      details.pressureScope = formData.pressureScope || {};
      break;
      
    case 'office':
      details.cleaningDetails = formData.cleaningDetails || {};
      details.services = formData.services || {};
      break;
      
    default:
      details.serviceDetails = formData.serviceDetails || {};
  }

  return details;
}