import React from 'react';
import { Form, FormField } from '../components/FormFields';

export function SanitizationForm() {
  return (
    <Form onSubmit={(data: Record<string, unknown>) => console.log(data)}>
      <FormField label="Facility Type" required>
        <select
          name="facilityType"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        >
          <option value="office">Office Space</option>
          <option value="medical">Medical Facility</option>
          <option value="retail">Retail Space</option>
          <option value="school">Educational Facility</option>
          <option value="gym">Gym/Fitness Center</option>
        </select>
      </FormField>

      <FormField label="Square Footage" required>
        <input
          type="number"
          name="squareFootage"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        />
      </FormField>

      <FormField label="High-Touch Areas">
        <div className="space-y-2">
          {[
            'Door Handles',
            'Light Switches',
            'Elevator Buttons',
            'Countertops',
            'Bathroom Fixtures',
            'Shared Equipment'
          ].map((area) => (
            <label key={area} className="flex items-center">
              <input
                type="checkbox"
                name="highTouchAreas"
                value={area.toLowerCase().replace(/\s+/g, '-')}
                className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
              />
              <span className="ml-2 text-gray-700">{area}</span>
            </label>
          ))}
        </div>
      </FormField>

      <FormField label="Special Instructions">
        <textarea
          name="instructions"
          rows={3}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          placeholder="Any specific sanitization requirements or concerns?"
        />
      </FormField>
    </Form>
  );
}
