import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Clock, Star, ArrowRight, Building2, Users, Sparkles } from 'lucide-react';
import { Button } from '../ui/Button';

interface HeroProps {
  onBook: () => void;
}

export function Hero({ onBook }: HeroProps) {
  return (
    <>
      {/* Desktop Hero Section */}
      <section className="relative hidden sm:flex min-h-[90vh] items-center mt-0 sm:mt-16">
        {/* Background Image with Overlay */}
        <div className="absolute inset-0">
          <img
            src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-1.2.1&auto=format&fit=crop&w=2850&q=80"
            alt="Professional office cleaning"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-brand-900/95 via-brand-800/90 to-accent-900/85" />
        </div>

        {/* Content */}
        <div className="relative z-10 w-full">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-16 md:py-24">
            <div className="text-center max-w-3xl mx-auto mb-8 sm:mb-12">
              <h1 className="text-3xl sm:text-4xl md:text-6xl font-bold text-white leading-tight mb-4 sm:mb-6 animate-fade-in">
                Professional Commercial Cleaning Services
              </h1>
              
              <p className="text-lg sm:text-xl md:text-2xl text-brand-100 mb-8 sm:mb-12 animate-fade-in" style={{ animationDelay: '0.2s' }}>
                Transforming over 1,400 commercial facilities across America with professional cleaning solutions for 250+ satisfied clients
              </p>

              {/* Search Bar */}
              <div className="animate-fade-in" style={{ animationDelay: '0.4s' }}>
                <Button size="lg" onClick={onBook} className="w-full sm:w-auto">
                  Get Started
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </div>
            </div>

            {/* Trust Badges */}
            <div className="max-w-4xl mx-auto px-4">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-8 animate-fade-in" style={{ animationDelay: '0.6s' }}>
                {[
                  {
                    icon: Shield,
                    title: 'Licensed & Insured',
                    subtitle: 'For your peace of mind'
                  },
                  {
                    icon: Clock,
                    title: '24/7 Service',
                    subtitle: 'Always available'
                  },
                  {
                    icon: Star,
                    title: '5-Star Service',
                    subtitle: '100% satisfaction guaranteed'
                  }
                ].map((badge) => (
                  <div 
                    key={badge.title}
                    className="bg-white/10 backdrop-blur-sm rounded-xl p-4 sm:p-6 border border-white/20 shadow-lg hover:shadow-xl transition-all touch-manipulation"
                  >
                    <div className="flex items-center space-x-3 sm:space-x-4">
                      <div className="p-2 sm:p-3 rounded-xl bg-brand-400/20">
                        <badge.icon className="w-5 h-5 sm:w-6 sm:h-6 text-brand-100" />
                      </div>
                      <div>
                        <div className="font-semibold text-white text-sm sm:text-base">{badge.title}</div>
                        <div className="text-xs sm:text-sm text-brand-100">{badge.subtitle}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mobile Hero - Updated Spacing */}
      <section className="relative sm:hidden min-h-screen">
        {/* Safe area for header - increased */}
        <div className="h-24" />

        <div className="absolute inset-0 bg-gradient-to-b from-brand-600 to-brand-800">
          {/* Background patterns and floating icons */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,_rgba(255,255,255,0.15)_1px,_transparent_0)] bg-[size:20px_20px]" />
          </div>
          
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1 }}
            className="absolute inset-0 overflow-hidden pointer-events-none"
          >
            {[Sparkles, Building2, Users].map((Icon, index) => (
              <motion.div
                key={index}
                className="absolute"
                initial={{ opacity: 0, y: 50 }}
                animate={{ 
                  opacity: [0.2, 0.5, 0.2],
                  y: [0, -30, 0]
                }}
                transition={{
                  duration: 3,
                  delay: index * 0.5,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                style={{
                  left: `${20 + (index * 30)}%`,
                  top: `${20 + (index * 15)}%`
                }}
              >
                <Icon className="w-12 h-12 text-white/20" />
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Content - Adjusted spacing */}
        <div className="relative z-10 px-6 flex flex-col min-h-[calc(100vh-6rem)]">
          {/* Main Content */}
          <div className="flex-1 flex flex-col justify-center pb-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center mb-8"
            >
              <h1 className="text-4xl font-bold text-white leading-tight">
                Professional
                <span className="block mt-2 text-brand-100">
                  Cleaning Solutions
                </span>
              </h1>
              <p className="text-lg text-brand-200 mt-4 mx-auto max-w-xs">
                Transform your space with our expert services
              </p>
            </motion.div>

            {/* Action Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="mb-8"
            >
              <button
                onClick={onBook}
                className="w-full bg-white text-brand-600 font-semibold py-4 px-6 rounded-2xl shadow-lg 
                         hover:bg-brand-50 active:scale-[0.98] transition-all duration-300
                         flex items-center justify-center space-x-2"
              >
                <span>Get Free Quote</span>
                <ArrowRight className="w-5 h-5" />
              </button>
            </motion.div>

            {/* Stats Cards - Reduced size */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="grid grid-cols-2 gap-3 mb-6"
            >
              {[
                { label: 'Buildings Serviced', value: '1,400+' },
                { label: 'Happy Clients', value: '250+' },
                { label: 'Years Experience', value: '8+' },
                { label: 'Service Rating', value: '4.9/5' }
              ].map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.4 + (index * 0.1) }}
                  className="bg-white/10 backdrop-blur-sm rounded-xl p-3 text-center"
                >
                  <div className="text-lg font-bold text-white">{stat.value}</div>
                  <div className="text-xs text-brand-200">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>
          </div>

          {/* Trust Badges - Moved to bottom with reduced size */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="space-y-2 mb-6"
          >
            {[
              { icon: Shield, text: 'Licensed & Insured' },
              { icon: Clock, text: '24/7 Service Available' },
              { icon: Star, text: 'Satisfaction Guaranteed' }
            ].map((badge, index) => (
              <motion.div
                key={badge.text}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.7 + (index * 0.1) }}
                className="flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-xl p-3"
              >
                <badge.icon className="w-4 h-4 text-brand-100" />
                <span className="text-white text-xs">{badge.text}</span>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>
    </>
  );
}