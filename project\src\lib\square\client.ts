import { Client as _Client, Environment as _Environment } from 'square';

// Check if Square API keys are configured
export const isSquareConfigured = () => {
  const hasAccessToken = Boolean(import.meta.env.VITE_SQUARE_ACCESS_TOKEN);
  const hasApplicationId = Boolean(import.meta.env.VITE_SQUARE_APPLICATION_ID);
  const hasLocationId = Boolean(import.meta.env.VITE_SQUARE_LOCATION_ID);

  if (!hasAccessToken || !hasApplicationId || !hasLocationId) {
    console.warn('Square configuration is incomplete. Missing:', {
      accessToken: !hasAccessToken,
      applicationId: !hasApplicationId,
      locationId: !hasLocationId
    });
    return false;
  }

  return true;
};

// Get Square application ID
export const squareApplicationId = import.meta.env.VITE_SQUARE_APPLICATION_ID;

// Get Square location ID
export const squareLocationId = import.meta.env.VITE_SQUARE_LOCATION_ID;

// This function is kept for backward compatibility but will be deprecated
// in favor of using Supabase Functions for Square API calls
export async function createPaymentLink(
  amount: number,
  currency: string = 'USD',
  orderId?: string,
  customerEmail?: string,
  description?: string
) {
  try {
    console.warn('Direct Square API calls from the frontend are deprecated. Use Supabase Functions instead.');
    
    // Call the Supabase Function instead
    const { data, error } = await fetch('/api/create-payment-link', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount,
        currency,
        orderId,
        customerEmail,
        description
      }),
    }).then(res => res.json());

    if (error) {
      throw new Error(error);
    }

    return data?.paymentLink;
  } catch (error) {
    throw new Error(`Square API Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// These functions are kept for backward compatibility but will be deprecated
export async function createCustomer(
  _firstName: string,
  _lastName: string,
  _email: string,
  _phoneNumber?: string,
  _companyName?: string
) {
  // Direct Square API calls from the frontend are deprecated. Use Supabase Functions instead.
  return null;
}

export async function createOrder(
  _lineItems: Array<unknown>,
  _customerId?: string
) {
  // Direct Square API calls from the frontend are deprecated. Use Supabase Functions instead.
  return null;
}