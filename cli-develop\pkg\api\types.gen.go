// Package api provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package api

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/oapi-codegen/nullable"
	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

const (
	BearerScopes = "bearer.Scopes"
	Oauth2Scopes = "oauth2.Scopes"
)

// Defines values for ApiKeyResponseType.
const (
	ApiKeyResponseTypeLegacy      ApiKeyResponseType = "legacy"
	ApiKeyResponseTypePublishable ApiKeyResponseType = "publishable"
	ApiKeyResponseTypeSecret      ApiKeyResponseType = "secret"
)

// Defines values for ApplyProjectAddonBodyAddonType.
const (
	ApplyProjectAddonBodyAddonTypeAuthMfaPhone    ApplyProjectAddonBodyAddonType = "auth_mfa_phone"
	ApplyProjectAddonBodyAddonTypeAuthMfaWebAuthn ApplyProjectAddonBodyAddonType = "auth_mfa_web_authn"
	ApplyProjectAddonBodyAddonTypeComputeInstance ApplyProjectAddonBodyAddonType = "compute_instance"
	ApplyProjectAddonBodyAddonTypeCustomDomain    ApplyProjectAddonBodyAddonType = "custom_domain"
	ApplyProjectAddonBodyAddonTypeIpv4            ApplyProjectAddonBodyAddonType = "ipv4"
	ApplyProjectAddonBodyAddonTypeLogDrain        ApplyProjectAddonBodyAddonType = "log_drain"
	ApplyProjectAddonBodyAddonTypePitr            ApplyProjectAddonBodyAddonType = "pitr"
)

// Defines values for ApplyProjectAddonBodyAddonVariant0.
const (
	ApplyProjectAddonBodyAddonVariant0Ci12xlarge                ApplyProjectAddonBodyAddonVariant0 = "ci_12xlarge"
	ApplyProjectAddonBodyAddonVariant0Ci16xlarge                ApplyProjectAddonBodyAddonVariant0 = "ci_16xlarge"
	ApplyProjectAddonBodyAddonVariant0Ci24xlarge                ApplyProjectAddonBodyAddonVariant0 = "ci_24xlarge"
	ApplyProjectAddonBodyAddonVariant0Ci24xlargeHighMemory      ApplyProjectAddonBodyAddonVariant0 = "ci_24xlarge_high_memory"
	ApplyProjectAddonBodyAddonVariant0Ci24xlargeOptimizedCpu    ApplyProjectAddonBodyAddonVariant0 = "ci_24xlarge_optimized_cpu"
	ApplyProjectAddonBodyAddonVariant0Ci24xlargeOptimizedMemory ApplyProjectAddonBodyAddonVariant0 = "ci_24xlarge_optimized_memory"
	ApplyProjectAddonBodyAddonVariant0Ci2xlarge                 ApplyProjectAddonBodyAddonVariant0 = "ci_2xlarge"
	ApplyProjectAddonBodyAddonVariant0Ci48xlarge                ApplyProjectAddonBodyAddonVariant0 = "ci_48xlarge"
	ApplyProjectAddonBodyAddonVariant0Ci48xlargeHighMemory      ApplyProjectAddonBodyAddonVariant0 = "ci_48xlarge_high_memory"
	ApplyProjectAddonBodyAddonVariant0Ci48xlargeOptimizedCpu    ApplyProjectAddonBodyAddonVariant0 = "ci_48xlarge_optimized_cpu"
	ApplyProjectAddonBodyAddonVariant0Ci48xlargeOptimizedMemory ApplyProjectAddonBodyAddonVariant0 = "ci_48xlarge_optimized_memory"
	ApplyProjectAddonBodyAddonVariant0Ci4xlarge                 ApplyProjectAddonBodyAddonVariant0 = "ci_4xlarge"
	ApplyProjectAddonBodyAddonVariant0Ci8xlarge                 ApplyProjectAddonBodyAddonVariant0 = "ci_8xlarge"
	ApplyProjectAddonBodyAddonVariant0CiLarge                   ApplyProjectAddonBodyAddonVariant0 = "ci_large"
	ApplyProjectAddonBodyAddonVariant0CiMedium                  ApplyProjectAddonBodyAddonVariant0 = "ci_medium"
	ApplyProjectAddonBodyAddonVariant0CiMicro                   ApplyProjectAddonBodyAddonVariant0 = "ci_micro"
	ApplyProjectAddonBodyAddonVariant0CiSmall                   ApplyProjectAddonBodyAddonVariant0 = "ci_small"
	ApplyProjectAddonBodyAddonVariant0CiXlarge                  ApplyProjectAddonBodyAddonVariant0 = "ci_xlarge"
)

// Defines values for ApplyProjectAddonBodyAddonVariant1.
const (
	ApplyProjectAddonBodyAddonVariant1CdDefault ApplyProjectAddonBodyAddonVariant1 = "cd_default"
)

// Defines values for ApplyProjectAddonBodyAddonVariant2.
const (
	ApplyProjectAddonBodyAddonVariant2Pitr14 ApplyProjectAddonBodyAddonVariant2 = "pitr_14"
	ApplyProjectAddonBodyAddonVariant2Pitr28 ApplyProjectAddonBodyAddonVariant2 = "pitr_28"
	ApplyProjectAddonBodyAddonVariant2Pitr7  ApplyProjectAddonBodyAddonVariant2 = "pitr_7"
)

// Defines values for ApplyProjectAddonBodyAddonVariant3.
const (
	ApplyProjectAddonBodyAddonVariant3Ipv4Default ApplyProjectAddonBodyAddonVariant3 = "ipv4_default"
)

// Defines values for AuthConfigResponsePasswordRequiredCharacters.
const (
	AuthConfigResponsePasswordRequiredCharactersAbcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ**********  AuthConfigResponsePasswordRequiredCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ:**********"
	AuthConfigResponsePasswordRequiredCharactersAbcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ**********1 AuthConfigResponsePasswordRequiredCharacters = "abcdefghijklmnopqrstuvwxyz:ABCDEFGHIJKLMNOPQRSTUVWXYZ:**********"
	AuthConfigResponsePasswordRequiredCharactersAbcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ**********2 AuthConfigResponsePasswordRequiredCharacters = "abcdefghijklmnopqrstuvwxyz:ABCDEFGHIJKLMNOPQRSTUVWXYZ:**********:!@#$%^&*()_+-=[]{};'\\\\:\"|<>?,./`~"
	AuthConfigResponsePasswordRequiredCharactersEmpty                                                           AuthConfigResponsePasswordRequiredCharacters = ""
)

// Defines values for AuthConfigResponseSecurityCaptchaProvider.
const (
	AuthConfigResponseSecurityCaptchaProviderHcaptcha  AuthConfigResponseSecurityCaptchaProvider = "hcaptcha"
	AuthConfigResponseSecurityCaptchaProviderTurnstile AuthConfigResponseSecurityCaptchaProvider = "turnstile"
)

// Defines values for AuthConfigResponseSmsProvider.
const (
	AuthConfigResponseSmsProviderMessagebird  AuthConfigResponseSmsProvider = "messagebird"
	AuthConfigResponseSmsProviderTextlocal    AuthConfigResponseSmsProvider = "textlocal"
	AuthConfigResponseSmsProviderTwilio       AuthConfigResponseSmsProvider = "twilio"
	AuthConfigResponseSmsProviderTwilioVerify AuthConfigResponseSmsProvider = "twilio_verify"
	AuthConfigResponseSmsProviderVonage       AuthConfigResponseSmsProvider = "vonage"
)

// Defines values for BranchDeleteResponseMessage.
const (
	BranchDeleteResponseMessageOk BranchDeleteResponseMessage = "ok"
)

// Defines values for BranchDetailResponseStatus.
const (
	BranchDetailResponseStatusACTIVEHEALTHY   BranchDetailResponseStatus = "ACTIVE_HEALTHY"
	BranchDetailResponseStatusACTIVEUNHEALTHY BranchDetailResponseStatus = "ACTIVE_UNHEALTHY"
	BranchDetailResponseStatusCOMINGUP        BranchDetailResponseStatus = "COMING_UP"
	BranchDetailResponseStatusGOINGDOWN       BranchDetailResponseStatus = "GOING_DOWN"
	BranchDetailResponseStatusINACTIVE        BranchDetailResponseStatus = "INACTIVE"
	BranchDetailResponseStatusINITFAILED      BranchDetailResponseStatus = "INIT_FAILED"
	BranchDetailResponseStatusPAUSEFAILED     BranchDetailResponseStatus = "PAUSE_FAILED"
	BranchDetailResponseStatusPAUSING         BranchDetailResponseStatus = "PAUSING"
	BranchDetailResponseStatusREMOVED         BranchDetailResponseStatus = "REMOVED"
	BranchDetailResponseStatusRESIZING        BranchDetailResponseStatus = "RESIZING"
	BranchDetailResponseStatusRESTARTING      BranchDetailResponseStatus = "RESTARTING"
	BranchDetailResponseStatusRESTOREFAILED   BranchDetailResponseStatus = "RESTORE_FAILED"
	BranchDetailResponseStatusRESTORING       BranchDetailResponseStatus = "RESTORING"
	BranchDetailResponseStatusUNKNOWN         BranchDetailResponseStatus = "UNKNOWN"
	BranchDetailResponseStatusUPGRADING       BranchDetailResponseStatus = "UPGRADING"
)

// Defines values for BranchResponseStatus.
const (
	BranchResponseStatusCREATINGPROJECT   BranchResponseStatus = "CREATING_PROJECT"
	BranchResponseStatusFUNCTIONSDEPLOYED BranchResponseStatus = "FUNCTIONS_DEPLOYED"
	BranchResponseStatusFUNCTIONSFAILED   BranchResponseStatus = "FUNCTIONS_FAILED"
	BranchResponseStatusMIGRATIONSFAILED  BranchResponseStatus = "MIGRATIONS_FAILED"
	BranchResponseStatusMIGRATIONSPASSED  BranchResponseStatus = "MIGRATIONS_PASSED"
	BranchResponseStatusRUNNINGMIGRATIONS BranchResponseStatus = "RUNNING_MIGRATIONS"
)

// Defines values for BranchUpdateResponseMessage.
const (
	BranchUpdateResponseMessageOk BranchUpdateResponseMessage = "ok"
)

// Defines values for BulkUpdateFunctionBodyStatus.
const (
	BulkUpdateFunctionBodyStatusACTIVE    BulkUpdateFunctionBodyStatus = "ACTIVE"
	BulkUpdateFunctionBodyStatusREMOVED   BulkUpdateFunctionBodyStatus = "REMOVED"
	BulkUpdateFunctionBodyStatusTHROTTLED BulkUpdateFunctionBodyStatus = "THROTTLED"
)

// Defines values for BulkUpdateFunctionResponseFunctionsStatus.
const (
	BulkUpdateFunctionResponseFunctionsStatusACTIVE    BulkUpdateFunctionResponseFunctionsStatus = "ACTIVE"
	BulkUpdateFunctionResponseFunctionsStatusREMOVED   BulkUpdateFunctionResponseFunctionsStatus = "REMOVED"
	BulkUpdateFunctionResponseFunctionsStatusTHROTTLED BulkUpdateFunctionResponseFunctionsStatus = "THROTTLED"
)

// Defines values for CreateApiKeyBodyType.
const (
	CreateApiKeyBodyTypePublishable CreateApiKeyBodyType = "publishable"
	CreateApiKeyBodyTypeSecret      CreateApiKeyBodyType = "secret"
)

// Defines values for CreateBranchBodyDesiredInstanceSize.
const (
	CreateBranchBodyDesiredInstanceSizeLarge                    CreateBranchBodyDesiredInstanceSize = "large"
	CreateBranchBodyDesiredInstanceSizeMedium                   CreateBranchBodyDesiredInstanceSize = "medium"
	CreateBranchBodyDesiredInstanceSizeMicro                    CreateBranchBodyDesiredInstanceSize = "micro"
	CreateBranchBodyDesiredInstanceSizeN12xlarge                CreateBranchBodyDesiredInstanceSize = "12xlarge"
	CreateBranchBodyDesiredInstanceSizeN16xlarge                CreateBranchBodyDesiredInstanceSize = "16xlarge"
	CreateBranchBodyDesiredInstanceSizeN24xlarge                CreateBranchBodyDesiredInstanceSize = "24xlarge"
	CreateBranchBodyDesiredInstanceSizeN24xlargeHighMemory      CreateBranchBodyDesiredInstanceSize = "24xlarge_high_memory"
	CreateBranchBodyDesiredInstanceSizeN24xlargeOptimizedCpu    CreateBranchBodyDesiredInstanceSize = "24xlarge_optimized_cpu"
	CreateBranchBodyDesiredInstanceSizeN24xlargeOptimizedMemory CreateBranchBodyDesiredInstanceSize = "24xlarge_optimized_memory"
	CreateBranchBodyDesiredInstanceSizeN2xlarge                 CreateBranchBodyDesiredInstanceSize = "2xlarge"
	CreateBranchBodyDesiredInstanceSizeN48xlarge                CreateBranchBodyDesiredInstanceSize = "48xlarge"
	CreateBranchBodyDesiredInstanceSizeN48xlargeHighMemory      CreateBranchBodyDesiredInstanceSize = "48xlarge_high_memory"
	CreateBranchBodyDesiredInstanceSizeN48xlargeOptimizedCpu    CreateBranchBodyDesiredInstanceSize = "48xlarge_optimized_cpu"
	CreateBranchBodyDesiredInstanceSizeN48xlargeOptimizedMemory CreateBranchBodyDesiredInstanceSize = "48xlarge_optimized_memory"
	CreateBranchBodyDesiredInstanceSizeN4xlarge                 CreateBranchBodyDesiredInstanceSize = "4xlarge"
	CreateBranchBodyDesiredInstanceSizeN8xlarge                 CreateBranchBodyDesiredInstanceSize = "8xlarge"
	CreateBranchBodyDesiredInstanceSizeNano                     CreateBranchBodyDesiredInstanceSize = "nano"
	CreateBranchBodyDesiredInstanceSizePico                     CreateBranchBodyDesiredInstanceSize = "pico"
	CreateBranchBodyDesiredInstanceSizeSmall                    CreateBranchBodyDesiredInstanceSize = "small"
	CreateBranchBodyDesiredInstanceSizeXlarge                   CreateBranchBodyDesiredInstanceSize = "xlarge"
)

// Defines values for CreateBranchBodyPostgresEngine.
const (
	CreateBranchBodyPostgresEngineN15       CreateBranchBodyPostgresEngine = "15"
	CreateBranchBodyPostgresEngineN17       CreateBranchBodyPostgresEngine = "17"
	CreateBranchBodyPostgresEngineN17Oriole CreateBranchBodyPostgresEngine = "17-oriole"
)

// Defines values for CreateBranchBodyReleaseChannel.
const (
	CreateBranchBodyReleaseChannelAlpha     CreateBranchBodyReleaseChannel = "alpha"
	CreateBranchBodyReleaseChannelBeta      CreateBranchBodyReleaseChannel = "beta"
	CreateBranchBodyReleaseChannelGa        CreateBranchBodyReleaseChannel = "ga"
	CreateBranchBodyReleaseChannelInternal  CreateBranchBodyReleaseChannel = "internal"
	CreateBranchBodyReleaseChannelPreview   CreateBranchBodyReleaseChannel = "preview"
	CreateBranchBodyReleaseChannelWithdrawn CreateBranchBodyReleaseChannel = "withdrawn"
)

// Defines values for CreateProviderBodyType.
const (
	Saml CreateProviderBodyType = "saml"
)

// Defines values for CreateSigningKeyBodyAlgorithm.
const (
	CreateSigningKeyBodyAlgorithmES256 CreateSigningKeyBodyAlgorithm = "ES256"
	CreateSigningKeyBodyAlgorithmEdDSA CreateSigningKeyBodyAlgorithm = "EdDSA"
	CreateSigningKeyBodyAlgorithmHS256 CreateSigningKeyBodyAlgorithm = "HS256"
	CreateSigningKeyBodyAlgorithmRS256 CreateSigningKeyBodyAlgorithm = "RS256"
)

// Defines values for CreateSigningKeyBodyStatus.
const (
	CreateSigningKeyBodyStatusInUse   CreateSigningKeyBodyStatus = "in_use"
	CreateSigningKeyBodyStatusStandby CreateSigningKeyBodyStatus = "standby"
)

// Defines values for DatabaseUpgradeStatusResponseDatabaseUpgradeStatusError.
const (
	N1UpgradedInstanceLaunchFailed                 DatabaseUpgradeStatusResponseDatabaseUpgradeStatusError = "1_upgraded_instance_launch_failed"
	N2VolumeDetachchmentFromUpgradedInstanceFailed DatabaseUpgradeStatusResponseDatabaseUpgradeStatusError = "2_volume_detachchment_from_upgraded_instance_failed"
	N3VolumeAttachmentToOriginalInstanceFailed     DatabaseUpgradeStatusResponseDatabaseUpgradeStatusError = "3_volume_attachment_to_original_instance_failed"
	N4DataUpgradeInitiationFailed                  DatabaseUpgradeStatusResponseDatabaseUpgradeStatusError = "4_data_upgrade_initiation_failed"
	N5DataUpgradeCompletionFailed                  DatabaseUpgradeStatusResponseDatabaseUpgradeStatusError = "5_data_upgrade_completion_failed"
	N6VolumeDetachchmentFromOriginalInstanceFailed DatabaseUpgradeStatusResponseDatabaseUpgradeStatusError = "6_volume_detachchment_from_original_instance_failed"
	N7VolumeAttachmentToUpgradedInstanceFailed     DatabaseUpgradeStatusResponseDatabaseUpgradeStatusError = "7_volume_attachment_to_upgraded_instance_failed"
	N8UpgradeCompletionFailed                      DatabaseUpgradeStatusResponseDatabaseUpgradeStatusError = "8_upgrade_completion_failed"
	N9PostPhysicalBackupFailed                     DatabaseUpgradeStatusResponseDatabaseUpgradeStatusError = "9_post_physical_backup_failed"
)

// Defines values for DatabaseUpgradeStatusResponseDatabaseUpgradeStatusProgress.
const (
	N0Requested                          DatabaseUpgradeStatusResponseDatabaseUpgradeStatusProgress = "0_requested"
	N10CompletedPostPhysicalBackup       DatabaseUpgradeStatusResponseDatabaseUpgradeStatusProgress = "10_completed_post_physical_backup"
	N1Started                            DatabaseUpgradeStatusResponseDatabaseUpgradeStatusProgress = "1_started"
	N2LaunchedUpgradedInstance           DatabaseUpgradeStatusResponseDatabaseUpgradeStatusProgress = "2_launched_upgraded_instance"
	N3DetachedVolumeFromUpgradedInstance DatabaseUpgradeStatusResponseDatabaseUpgradeStatusProgress = "3_detached_volume_from_upgraded_instance"
	N4AttachedVolumeToOriginalInstance   DatabaseUpgradeStatusResponseDatabaseUpgradeStatusProgress = "4_attached_volume_to_original_instance"
	N5InitiatedDataUpgrade               DatabaseUpgradeStatusResponseDatabaseUpgradeStatusProgress = "5_initiated_data_upgrade"
	N6CompletedDataUpgrade               DatabaseUpgradeStatusResponseDatabaseUpgradeStatusProgress = "6_completed_data_upgrade"
	N7DetachedVolumeFromOriginalInstance DatabaseUpgradeStatusResponseDatabaseUpgradeStatusProgress = "7_detached_volume_from_original_instance"
	N8AttachedVolumeToUpgradedInstance   DatabaseUpgradeStatusResponseDatabaseUpgradeStatusProgress = "8_attached_volume_to_upgraded_instance"
	N9CompletedUpgrade                   DatabaseUpgradeStatusResponseDatabaseUpgradeStatusProgress = "9_completed_upgrade"
)

// Defines values for DeployFunctionResponseStatus.
const (
	DeployFunctionResponseStatusACTIVE    DeployFunctionResponseStatus = "ACTIVE"
	DeployFunctionResponseStatusREMOVED   DeployFunctionResponseStatus = "REMOVED"
	DeployFunctionResponseStatusTHROTTLED DeployFunctionResponseStatus = "THROTTLED"
)

// Defines values for FunctionResponseStatus.
const (
	FunctionResponseStatusACTIVE    FunctionResponseStatus = "ACTIVE"
	FunctionResponseStatusREMOVED   FunctionResponseStatus = "REMOVED"
	FunctionResponseStatusTHROTTLED FunctionResponseStatus = "THROTTLED"
)

// Defines values for FunctionSlugResponseStatus.
const (
	FunctionSlugResponseStatusACTIVE    FunctionSlugResponseStatus = "ACTIVE"
	FunctionSlugResponseStatusREMOVED   FunctionSlugResponseStatus = "REMOVED"
	FunctionSlugResponseStatusTHROTTLED FunctionSlugResponseStatus = "THROTTLED"
)

// Defines values for GetProjectAvailableRestoreVersionsResponseAvailableVersionsPostgresEngine.
const (
	GetProjectAvailableRestoreVersionsResponseAvailableVersionsPostgresEngineN13       GetProjectAvailableRestoreVersionsResponseAvailableVersionsPostgresEngine = "13"
	GetProjectAvailableRestoreVersionsResponseAvailableVersionsPostgresEngineN14       GetProjectAvailableRestoreVersionsResponseAvailableVersionsPostgresEngine = "14"
	GetProjectAvailableRestoreVersionsResponseAvailableVersionsPostgresEngineN15       GetProjectAvailableRestoreVersionsResponseAvailableVersionsPostgresEngine = "15"
	GetProjectAvailableRestoreVersionsResponseAvailableVersionsPostgresEngineN17       GetProjectAvailableRestoreVersionsResponseAvailableVersionsPostgresEngine = "17"
	GetProjectAvailableRestoreVersionsResponseAvailableVersionsPostgresEngineN17Oriole GetProjectAvailableRestoreVersionsResponseAvailableVersionsPostgresEngine = "17-oriole"
)

// Defines values for GetProjectAvailableRestoreVersionsResponseAvailableVersionsReleaseChannel.
const (
	GetProjectAvailableRestoreVersionsResponseAvailableVersionsReleaseChannelAlpha     GetProjectAvailableRestoreVersionsResponseAvailableVersionsReleaseChannel = "alpha"
	GetProjectAvailableRestoreVersionsResponseAvailableVersionsReleaseChannelBeta      GetProjectAvailableRestoreVersionsResponseAvailableVersionsReleaseChannel = "beta"
	GetProjectAvailableRestoreVersionsResponseAvailableVersionsReleaseChannelGa        GetProjectAvailableRestoreVersionsResponseAvailableVersionsReleaseChannel = "ga"
	GetProjectAvailableRestoreVersionsResponseAvailableVersionsReleaseChannelInternal  GetProjectAvailableRestoreVersionsResponseAvailableVersionsReleaseChannel = "internal"
	GetProjectAvailableRestoreVersionsResponseAvailableVersionsReleaseChannelPreview   GetProjectAvailableRestoreVersionsResponseAvailableVersionsReleaseChannel = "preview"
	GetProjectAvailableRestoreVersionsResponseAvailableVersionsReleaseChannelWithdrawn GetProjectAvailableRestoreVersionsResponseAvailableVersionsReleaseChannel = "withdrawn"
)

// Defines values for ListProjectAddonsResponseAvailableAddonsType.
const (
	ListProjectAddonsResponseAvailableAddonsTypeAuthMfaPhone    ListProjectAddonsResponseAvailableAddonsType = "auth_mfa_phone"
	ListProjectAddonsResponseAvailableAddonsTypeAuthMfaWebAuthn ListProjectAddonsResponseAvailableAddonsType = "auth_mfa_web_authn"
	ListProjectAddonsResponseAvailableAddonsTypeComputeInstance ListProjectAddonsResponseAvailableAddonsType = "compute_instance"
	ListProjectAddonsResponseAvailableAddonsTypeCustomDomain    ListProjectAddonsResponseAvailableAddonsType = "custom_domain"
	ListProjectAddonsResponseAvailableAddonsTypeIpv4            ListProjectAddonsResponseAvailableAddonsType = "ipv4"
	ListProjectAddonsResponseAvailableAddonsTypeLogDrain        ListProjectAddonsResponseAvailableAddonsType = "log_drain"
	ListProjectAddonsResponseAvailableAddonsTypePitr            ListProjectAddonsResponseAvailableAddonsType = "pitr"
)

// Defines values for ListProjectAddonsResponseAvailableAddonsVariantsId0.
const (
	ListProjectAddonsResponseAvailableAddonsVariantsId0Ci12xlarge                ListProjectAddonsResponseAvailableAddonsVariantsId0 = "ci_12xlarge"
	ListProjectAddonsResponseAvailableAddonsVariantsId0Ci16xlarge                ListProjectAddonsResponseAvailableAddonsVariantsId0 = "ci_16xlarge"
	ListProjectAddonsResponseAvailableAddonsVariantsId0Ci24xlarge                ListProjectAddonsResponseAvailableAddonsVariantsId0 = "ci_24xlarge"
	ListProjectAddonsResponseAvailableAddonsVariantsId0Ci24xlargeHighMemory      ListProjectAddonsResponseAvailableAddonsVariantsId0 = "ci_24xlarge_high_memory"
	ListProjectAddonsResponseAvailableAddonsVariantsId0Ci24xlargeOptimizedCpu    ListProjectAddonsResponseAvailableAddonsVariantsId0 = "ci_24xlarge_optimized_cpu"
	ListProjectAddonsResponseAvailableAddonsVariantsId0Ci24xlargeOptimizedMemory ListProjectAddonsResponseAvailableAddonsVariantsId0 = "ci_24xlarge_optimized_memory"
	ListProjectAddonsResponseAvailableAddonsVariantsId0Ci2xlarge                 ListProjectAddonsResponseAvailableAddonsVariantsId0 = "ci_2xlarge"
	ListProjectAddonsResponseAvailableAddonsVariantsId0Ci48xlarge                ListProjectAddonsResponseAvailableAddonsVariantsId0 = "ci_48xlarge"
	ListProjectAddonsResponseAvailableAddonsVariantsId0Ci48xlargeHighMemory      ListProjectAddonsResponseAvailableAddonsVariantsId0 = "ci_48xlarge_high_memory"
	ListProjectAddonsResponseAvailableAddonsVariantsId0Ci48xlargeOptimizedCpu    ListProjectAddonsResponseAvailableAddonsVariantsId0 = "ci_48xlarge_optimized_cpu"
	ListProjectAddonsResponseAvailableAddonsVariantsId0Ci48xlargeOptimizedMemory ListProjectAddonsResponseAvailableAddonsVariantsId0 = "ci_48xlarge_optimized_memory"
	ListProjectAddonsResponseAvailableAddonsVariantsId0Ci4xlarge                 ListProjectAddonsResponseAvailableAddonsVariantsId0 = "ci_4xlarge"
	ListProjectAddonsResponseAvailableAddonsVariantsId0Ci8xlarge                 ListProjectAddonsResponseAvailableAddonsVariantsId0 = "ci_8xlarge"
	ListProjectAddonsResponseAvailableAddonsVariantsId0CiLarge                   ListProjectAddonsResponseAvailableAddonsVariantsId0 = "ci_large"
	ListProjectAddonsResponseAvailableAddonsVariantsId0CiMedium                  ListProjectAddonsResponseAvailableAddonsVariantsId0 = "ci_medium"
	ListProjectAddonsResponseAvailableAddonsVariantsId0CiMicro                   ListProjectAddonsResponseAvailableAddonsVariantsId0 = "ci_micro"
	ListProjectAddonsResponseAvailableAddonsVariantsId0CiSmall                   ListProjectAddonsResponseAvailableAddonsVariantsId0 = "ci_small"
	ListProjectAddonsResponseAvailableAddonsVariantsId0CiXlarge                  ListProjectAddonsResponseAvailableAddonsVariantsId0 = "ci_xlarge"
)

// Defines values for ListProjectAddonsResponseAvailableAddonsVariantsId1.
const (
	ListProjectAddonsResponseAvailableAddonsVariantsId1CdDefault ListProjectAddonsResponseAvailableAddonsVariantsId1 = "cd_default"
)

// Defines values for ListProjectAddonsResponseAvailableAddonsVariantsId2.
const (
	ListProjectAddonsResponseAvailableAddonsVariantsId2Pitr14 ListProjectAddonsResponseAvailableAddonsVariantsId2 = "pitr_14"
	ListProjectAddonsResponseAvailableAddonsVariantsId2Pitr28 ListProjectAddonsResponseAvailableAddonsVariantsId2 = "pitr_28"
	ListProjectAddonsResponseAvailableAddonsVariantsId2Pitr7  ListProjectAddonsResponseAvailableAddonsVariantsId2 = "pitr_7"
)

// Defines values for ListProjectAddonsResponseAvailableAddonsVariantsId3.
const (
	ListProjectAddonsResponseAvailableAddonsVariantsId3Ipv4Default ListProjectAddonsResponseAvailableAddonsVariantsId3 = "ipv4_default"
)

// Defines values for ListProjectAddonsResponseAvailableAddonsVariantsId4.
const (
	ListProjectAddonsResponseAvailableAddonsVariantsId4AuthMfaPhoneDefault ListProjectAddonsResponseAvailableAddonsVariantsId4 = "auth_mfa_phone_default"
)

// Defines values for ListProjectAddonsResponseAvailableAddonsVariantsId5.
const (
	ListProjectAddonsResponseAvailableAddonsVariantsId5AuthMfaWebAuthnDefault ListProjectAddonsResponseAvailableAddonsVariantsId5 = "auth_mfa_web_authn_default"
)

// Defines values for ListProjectAddonsResponseAvailableAddonsVariantsId6.
const (
	ListProjectAddonsResponseAvailableAddonsVariantsId6LogDrainDefault ListProjectAddonsResponseAvailableAddonsVariantsId6 = "log_drain_default"
)

// Defines values for ListProjectAddonsResponseAvailableAddonsVariantsPriceInterval.
const (
	ListProjectAddonsResponseAvailableAddonsVariantsPriceIntervalHourly  ListProjectAddonsResponseAvailableAddonsVariantsPriceInterval = "hourly"
	ListProjectAddonsResponseAvailableAddonsVariantsPriceIntervalMonthly ListProjectAddonsResponseAvailableAddonsVariantsPriceInterval = "monthly"
)

// Defines values for ListProjectAddonsResponseAvailableAddonsVariantsPriceType.
const (
	ListProjectAddonsResponseAvailableAddonsVariantsPriceTypeFixed ListProjectAddonsResponseAvailableAddonsVariantsPriceType = "fixed"
	ListProjectAddonsResponseAvailableAddonsVariantsPriceTypeUsage ListProjectAddonsResponseAvailableAddonsVariantsPriceType = "usage"
)

// Defines values for ListProjectAddonsResponseSelectedAddonsType.
const (
	AuthMfaPhone    ListProjectAddonsResponseSelectedAddonsType = "auth_mfa_phone"
	AuthMfaWebAuthn ListProjectAddonsResponseSelectedAddonsType = "auth_mfa_web_authn"
	ComputeInstance ListProjectAddonsResponseSelectedAddonsType = "compute_instance"
	CustomDomain    ListProjectAddonsResponseSelectedAddonsType = "custom_domain"
	Ipv4            ListProjectAddonsResponseSelectedAddonsType = "ipv4"
	LogDrain        ListProjectAddonsResponseSelectedAddonsType = "log_drain"
	Pitr            ListProjectAddonsResponseSelectedAddonsType = "pitr"
)

// Defines values for ListProjectAddonsResponseSelectedAddonsVariantId0.
const (
	Ci12xlarge                ListProjectAddonsResponseSelectedAddonsVariantId0 = "ci_12xlarge"
	Ci16xlarge                ListProjectAddonsResponseSelectedAddonsVariantId0 = "ci_16xlarge"
	Ci24xlarge                ListProjectAddonsResponseSelectedAddonsVariantId0 = "ci_24xlarge"
	Ci24xlargeHighMemory      ListProjectAddonsResponseSelectedAddonsVariantId0 = "ci_24xlarge_high_memory"
	Ci24xlargeOptimizedCpu    ListProjectAddonsResponseSelectedAddonsVariantId0 = "ci_24xlarge_optimized_cpu"
	Ci24xlargeOptimizedMemory ListProjectAddonsResponseSelectedAddonsVariantId0 = "ci_24xlarge_optimized_memory"
	Ci2xlarge                 ListProjectAddonsResponseSelectedAddonsVariantId0 = "ci_2xlarge"
	Ci48xlarge                ListProjectAddonsResponseSelectedAddonsVariantId0 = "ci_48xlarge"
	Ci48xlargeHighMemory      ListProjectAddonsResponseSelectedAddonsVariantId0 = "ci_48xlarge_high_memory"
	Ci48xlargeOptimizedCpu    ListProjectAddonsResponseSelectedAddonsVariantId0 = "ci_48xlarge_optimized_cpu"
	Ci48xlargeOptimizedMemory ListProjectAddonsResponseSelectedAddonsVariantId0 = "ci_48xlarge_optimized_memory"
	Ci4xlarge                 ListProjectAddonsResponseSelectedAddonsVariantId0 = "ci_4xlarge"
	Ci8xlarge                 ListProjectAddonsResponseSelectedAddonsVariantId0 = "ci_8xlarge"
	CiLarge                   ListProjectAddonsResponseSelectedAddonsVariantId0 = "ci_large"
	CiMedium                  ListProjectAddonsResponseSelectedAddonsVariantId0 = "ci_medium"
	CiMicro                   ListProjectAddonsResponseSelectedAddonsVariantId0 = "ci_micro"
	CiSmall                   ListProjectAddonsResponseSelectedAddonsVariantId0 = "ci_small"
	CiXlarge                  ListProjectAddonsResponseSelectedAddonsVariantId0 = "ci_xlarge"
)

// Defines values for ListProjectAddonsResponseSelectedAddonsVariantId1.
const (
	CdDefault ListProjectAddonsResponseSelectedAddonsVariantId1 = "cd_default"
)

// Defines values for ListProjectAddonsResponseSelectedAddonsVariantId2.
const (
	Pitr14 ListProjectAddonsResponseSelectedAddonsVariantId2 = "pitr_14"
	Pitr28 ListProjectAddonsResponseSelectedAddonsVariantId2 = "pitr_28"
	Pitr7  ListProjectAddonsResponseSelectedAddonsVariantId2 = "pitr_7"
)

// Defines values for ListProjectAddonsResponseSelectedAddonsVariantId3.
const (
	Ipv4Default ListProjectAddonsResponseSelectedAddonsVariantId3 = "ipv4_default"
)

// Defines values for ListProjectAddonsResponseSelectedAddonsVariantId4.
const (
	ListProjectAddonsResponseSelectedAddonsVariantId4AuthMfaPhoneDefault ListProjectAddonsResponseSelectedAddonsVariantId4 = "auth_mfa_phone_default"
)

// Defines values for ListProjectAddonsResponseSelectedAddonsVariantId5.
const (
	ListProjectAddonsResponseSelectedAddonsVariantId5AuthMfaWebAuthnDefault ListProjectAddonsResponseSelectedAddonsVariantId5 = "auth_mfa_web_authn_default"
)

// Defines values for ListProjectAddonsResponseSelectedAddonsVariantId6.
const (
	ListProjectAddonsResponseSelectedAddonsVariantId6LogDrainDefault ListProjectAddonsResponseSelectedAddonsVariantId6 = "log_drain_default"
)

// Defines values for ListProjectAddonsResponseSelectedAddonsVariantPriceInterval.
const (
	ListProjectAddonsResponseSelectedAddonsVariantPriceIntervalHourly  ListProjectAddonsResponseSelectedAddonsVariantPriceInterval = "hourly"
	ListProjectAddonsResponseSelectedAddonsVariantPriceIntervalMonthly ListProjectAddonsResponseSelectedAddonsVariantPriceInterval = "monthly"
)

// Defines values for ListProjectAddonsResponseSelectedAddonsVariantPriceType.
const (
	ListProjectAddonsResponseSelectedAddonsVariantPriceTypeFixed ListProjectAddonsResponseSelectedAddonsVariantPriceType = "fixed"
	ListProjectAddonsResponseSelectedAddonsVariantPriceTypeUsage ListProjectAddonsResponseSelectedAddonsVariantPriceType = "usage"
)

// Defines values for NetworkRestrictionsResponseEntitlement.
const (
	Allowed    NetworkRestrictionsResponseEntitlement = "allowed"
	Disallowed NetworkRestrictionsResponseEntitlement = "disallowed"
)

// Defines values for NetworkRestrictionsResponseStatus.
const (
	Applied NetworkRestrictionsResponseStatus = "applied"
	Stored  NetworkRestrictionsResponseStatus = "stored"
)

// Defines values for OAuthTokenBodyGrantType.
const (
	AuthorizationCode OAuthTokenBodyGrantType = "authorization_code"
	RefreshToken      OAuthTokenBodyGrantType = "refresh_token"
)

// Defines values for OAuthTokenResponseTokenType.
const (
	Bearer OAuthTokenResponseTokenType = "Bearer"
)

// Defines values for OrganizationProjectClaimResponsePreviewSourceSubscriptionPlan.
const (
	OrganizationProjectClaimResponsePreviewSourceSubscriptionPlanEnterprise OrganizationProjectClaimResponsePreviewSourceSubscriptionPlan = "enterprise"
	OrganizationProjectClaimResponsePreviewSourceSubscriptionPlanFree       OrganizationProjectClaimResponsePreviewSourceSubscriptionPlan = "free"
	OrganizationProjectClaimResponsePreviewSourceSubscriptionPlanPro        OrganizationProjectClaimResponsePreviewSourceSubscriptionPlan = "pro"
	OrganizationProjectClaimResponsePreviewSourceSubscriptionPlanTeam       OrganizationProjectClaimResponsePreviewSourceSubscriptionPlan = "team"
)

// Defines values for OrganizationProjectClaimResponsePreviewTargetSubscriptionPlan.
const (
	OrganizationProjectClaimResponsePreviewTargetSubscriptionPlanEnterprise OrganizationProjectClaimResponsePreviewTargetSubscriptionPlan = "enterprise"
	OrganizationProjectClaimResponsePreviewTargetSubscriptionPlanFree       OrganizationProjectClaimResponsePreviewTargetSubscriptionPlan = "free"
	OrganizationProjectClaimResponsePreviewTargetSubscriptionPlanPro        OrganizationProjectClaimResponsePreviewTargetSubscriptionPlan = "pro"
	OrganizationProjectClaimResponsePreviewTargetSubscriptionPlanTeam       OrganizationProjectClaimResponsePreviewTargetSubscriptionPlan = "team"
)

// Defines values for PostgresConfigResponseSessionReplicationRole.
const (
	PostgresConfigResponseSessionReplicationRoleLocal   PostgresConfigResponseSessionReplicationRole = "local"
	PostgresConfigResponseSessionReplicationRoleOrigin  PostgresConfigResponseSessionReplicationRole = "origin"
	PostgresConfigResponseSessionReplicationRoleReplica PostgresConfigResponseSessionReplicationRole = "replica"
)

// Defines values for ProjectUpgradeEligibilityResponseCurrentAppVersionReleaseChannel.
const (
	ProjectUpgradeEligibilityResponseCurrentAppVersionReleaseChannelAlpha     ProjectUpgradeEligibilityResponseCurrentAppVersionReleaseChannel = "alpha"
	ProjectUpgradeEligibilityResponseCurrentAppVersionReleaseChannelBeta      ProjectUpgradeEligibilityResponseCurrentAppVersionReleaseChannel = "beta"
	ProjectUpgradeEligibilityResponseCurrentAppVersionReleaseChannelGa        ProjectUpgradeEligibilityResponseCurrentAppVersionReleaseChannel = "ga"
	ProjectUpgradeEligibilityResponseCurrentAppVersionReleaseChannelInternal  ProjectUpgradeEligibilityResponseCurrentAppVersionReleaseChannel = "internal"
	ProjectUpgradeEligibilityResponseCurrentAppVersionReleaseChannelPreview   ProjectUpgradeEligibilityResponseCurrentAppVersionReleaseChannel = "preview"
	ProjectUpgradeEligibilityResponseCurrentAppVersionReleaseChannelWithdrawn ProjectUpgradeEligibilityResponseCurrentAppVersionReleaseChannel = "withdrawn"
)

// Defines values for ProjectUpgradeEligibilityResponseTargetUpgradeVersionsPostgresVersion.
const (
	N15       ProjectUpgradeEligibilityResponseTargetUpgradeVersionsPostgresVersion = "15"
	N17       ProjectUpgradeEligibilityResponseTargetUpgradeVersionsPostgresVersion = "17"
	N17Oriole ProjectUpgradeEligibilityResponseTargetUpgradeVersionsPostgresVersion = "17-oriole"
)

// Defines values for ProjectUpgradeEligibilityResponseTargetUpgradeVersionsReleaseChannel.
const (
	ProjectUpgradeEligibilityResponseTargetUpgradeVersionsReleaseChannelAlpha     ProjectUpgradeEligibilityResponseTargetUpgradeVersionsReleaseChannel = "alpha"
	ProjectUpgradeEligibilityResponseTargetUpgradeVersionsReleaseChannelBeta      ProjectUpgradeEligibilityResponseTargetUpgradeVersionsReleaseChannel = "beta"
	ProjectUpgradeEligibilityResponseTargetUpgradeVersionsReleaseChannelGa        ProjectUpgradeEligibilityResponseTargetUpgradeVersionsReleaseChannel = "ga"
	ProjectUpgradeEligibilityResponseTargetUpgradeVersionsReleaseChannelInternal  ProjectUpgradeEligibilityResponseTargetUpgradeVersionsReleaseChannel = "internal"
	ProjectUpgradeEligibilityResponseTargetUpgradeVersionsReleaseChannelPreview   ProjectUpgradeEligibilityResponseTargetUpgradeVersionsReleaseChannel = "preview"
	ProjectUpgradeEligibilityResponseTargetUpgradeVersionsReleaseChannelWithdrawn ProjectUpgradeEligibilityResponseTargetUpgradeVersionsReleaseChannel = "withdrawn"
)

// Defines values for SetUpReadReplicaBodyReadReplicaRegion.
const (
	SetUpReadReplicaBodyReadReplicaRegionApEast1      SetUpReadReplicaBodyReadReplicaRegion = "ap-east-1"
	SetUpReadReplicaBodyReadReplicaRegionApNortheast1 SetUpReadReplicaBodyReadReplicaRegion = "ap-northeast-1"
	SetUpReadReplicaBodyReadReplicaRegionApNortheast2 SetUpReadReplicaBodyReadReplicaRegion = "ap-northeast-2"
	SetUpReadReplicaBodyReadReplicaRegionApSouth1     SetUpReadReplicaBodyReadReplicaRegion = "ap-south-1"
	SetUpReadReplicaBodyReadReplicaRegionApSoutheast1 SetUpReadReplicaBodyReadReplicaRegion = "ap-southeast-1"
	SetUpReadReplicaBodyReadReplicaRegionApSoutheast2 SetUpReadReplicaBodyReadReplicaRegion = "ap-southeast-2"
	SetUpReadReplicaBodyReadReplicaRegionCaCentral1   SetUpReadReplicaBodyReadReplicaRegion = "ca-central-1"
	SetUpReadReplicaBodyReadReplicaRegionEuCentral1   SetUpReadReplicaBodyReadReplicaRegion = "eu-central-1"
	SetUpReadReplicaBodyReadReplicaRegionEuCentral2   SetUpReadReplicaBodyReadReplicaRegion = "eu-central-2"
	SetUpReadReplicaBodyReadReplicaRegionEuNorth1     SetUpReadReplicaBodyReadReplicaRegion = "eu-north-1"
	SetUpReadReplicaBodyReadReplicaRegionEuWest1      SetUpReadReplicaBodyReadReplicaRegion = "eu-west-1"
	SetUpReadReplicaBodyReadReplicaRegionEuWest2      SetUpReadReplicaBodyReadReplicaRegion = "eu-west-2"
	SetUpReadReplicaBodyReadReplicaRegionEuWest3      SetUpReadReplicaBodyReadReplicaRegion = "eu-west-3"
	SetUpReadReplicaBodyReadReplicaRegionSaEast1      SetUpReadReplicaBodyReadReplicaRegion = "sa-east-1"
	SetUpReadReplicaBodyReadReplicaRegionUsEast1      SetUpReadReplicaBodyReadReplicaRegion = "us-east-1"
	SetUpReadReplicaBodyReadReplicaRegionUsEast2      SetUpReadReplicaBodyReadReplicaRegion = "us-east-2"
	SetUpReadReplicaBodyReadReplicaRegionUsWest1      SetUpReadReplicaBodyReadReplicaRegion = "us-west-1"
	SetUpReadReplicaBodyReadReplicaRegionUsWest2      SetUpReadReplicaBodyReadReplicaRegion = "us-west-2"
)

// Defines values for SigningKeyResponseAlgorithm.
const (
	SigningKeyResponseAlgorithmES256 SigningKeyResponseAlgorithm = "ES256"
	SigningKeyResponseAlgorithmEdDSA SigningKeyResponseAlgorithm = "EdDSA"
	SigningKeyResponseAlgorithmHS256 SigningKeyResponseAlgorithm = "HS256"
	SigningKeyResponseAlgorithmRS256 SigningKeyResponseAlgorithm = "RS256"
)

// Defines values for SigningKeyResponseStatus.
const (
	SigningKeyResponseStatusInUse          SigningKeyResponseStatus = "in_use"
	SigningKeyResponseStatusPreviouslyUsed SigningKeyResponseStatus = "previously_used"
	SigningKeyResponseStatusRevoked        SigningKeyResponseStatus = "revoked"
	SigningKeyResponseStatusStandby        SigningKeyResponseStatus = "standby"
)

// Defines values for SigningKeysResponseKeysAlgorithm.
const (
	ES256 SigningKeysResponseKeysAlgorithm = "ES256"
	EdDSA SigningKeysResponseKeysAlgorithm = "EdDSA"
	HS256 SigningKeysResponseKeysAlgorithm = "HS256"
	RS256 SigningKeysResponseKeysAlgorithm = "RS256"
)

// Defines values for SigningKeysResponseKeysStatus.
const (
	SigningKeysResponseKeysStatusInUse          SigningKeysResponseKeysStatus = "in_use"
	SigningKeysResponseKeysStatusPreviouslyUsed SigningKeysResponseKeysStatus = "previously_used"
	SigningKeysResponseKeysStatusRevoked        SigningKeysResponseKeysStatus = "revoked"
	SigningKeysResponseKeysStatusStandby        SigningKeysResponseKeysStatus = "standby"
)

// Defines values for SnippetListDataType.
const (
	SnippetListDataTypeSql SnippetListDataType = "sql"
)

// Defines values for SnippetListDataVisibility.
const (
	SnippetListDataVisibilityOrg     SnippetListDataVisibility = "org"
	SnippetListDataVisibilityProject SnippetListDataVisibility = "project"
	SnippetListDataVisibilityPublic  SnippetListDataVisibility = "public"
	SnippetListDataVisibilityUser    SnippetListDataVisibility = "user"
)

// Defines values for SnippetResponseType.
const (
	SnippetResponseTypeSql SnippetResponseType = "sql"
)

// Defines values for SnippetResponseVisibility.
const (
	SnippetResponseVisibilityOrg     SnippetResponseVisibility = "org"
	SnippetResponseVisibilityProject SnippetResponseVisibility = "project"
	SnippetResponseVisibilityPublic  SnippetResponseVisibility = "public"
	SnippetResponseVisibilityUser    SnippetResponseVisibility = "user"
)

// Defines values for SupavisorConfigResponseDatabaseType.
const (
	PRIMARY     SupavisorConfigResponseDatabaseType = "PRIMARY"
	READREPLICA SupavisorConfigResponseDatabaseType = "READ_REPLICA"
)

// Defines values for SupavisorConfigResponsePoolMode.
const (
	SupavisorConfigResponsePoolModeSession     SupavisorConfigResponsePoolMode = "session"
	SupavisorConfigResponsePoolModeTransaction SupavisorConfigResponsePoolMode = "transaction"
)

// Defines values for UpdateAuthConfigBodyPasswordRequiredCharacters.
const (
	UpdateAuthConfigBodyPasswordRequiredCharactersAbcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ**********  UpdateAuthConfigBodyPasswordRequiredCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ:**********"
	UpdateAuthConfigBodyPasswordRequiredCharactersAbcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ**********1 UpdateAuthConfigBodyPasswordRequiredCharacters = "abcdefghijklmnopqrstuvwxyz:ABCDEFGHIJKLMNOPQRSTUVWXYZ:**********"
	UpdateAuthConfigBodyPasswordRequiredCharactersAbcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ**********2 UpdateAuthConfigBodyPasswordRequiredCharacters = "abcdefghijklmnopqrstuvwxyz:ABCDEFGHIJKLMNOPQRSTUVWXYZ:**********:!@#$%^&*()_+-=[]{};'\\\\:\"|<>?,./`~"
	UpdateAuthConfigBodyPasswordRequiredCharactersEmpty                                                           UpdateAuthConfigBodyPasswordRequiredCharacters = ""
)

// Defines values for UpdateAuthConfigBodySecurityCaptchaProvider.
const (
	UpdateAuthConfigBodySecurityCaptchaProviderHcaptcha  UpdateAuthConfigBodySecurityCaptchaProvider = "hcaptcha"
	UpdateAuthConfigBodySecurityCaptchaProviderTurnstile UpdateAuthConfigBodySecurityCaptchaProvider = "turnstile"
)

// Defines values for UpdateAuthConfigBodySmsProvider.
const (
	UpdateAuthConfigBodySmsProviderMessagebird  UpdateAuthConfigBodySmsProvider = "messagebird"
	UpdateAuthConfigBodySmsProviderTextlocal    UpdateAuthConfigBodySmsProvider = "textlocal"
	UpdateAuthConfigBodySmsProviderTwilio       UpdateAuthConfigBodySmsProvider = "twilio"
	UpdateAuthConfigBodySmsProviderTwilioVerify UpdateAuthConfigBodySmsProvider = "twilio_verify"
	UpdateAuthConfigBodySmsProviderVonage       UpdateAuthConfigBodySmsProvider = "vonage"
)

// Defines values for UpdateBranchBodyStatus.
const (
	UpdateBranchBodyStatusCREATINGPROJECT   UpdateBranchBodyStatus = "CREATING_PROJECT"
	UpdateBranchBodyStatusFUNCTIONSDEPLOYED UpdateBranchBodyStatus = "FUNCTIONS_DEPLOYED"
	UpdateBranchBodyStatusFUNCTIONSFAILED   UpdateBranchBodyStatus = "FUNCTIONS_FAILED"
	UpdateBranchBodyStatusMIGRATIONSFAILED  UpdateBranchBodyStatus = "MIGRATIONS_FAILED"
	UpdateBranchBodyStatusMIGRATIONSPASSED  UpdateBranchBodyStatus = "MIGRATIONS_PASSED"
	UpdateBranchBodyStatusRUNNINGMIGRATIONS UpdateBranchBodyStatus = "RUNNING_MIGRATIONS"
)

// Defines values for UpdateCustomHostnameResponseStatus.
const (
	N1NotStarted           UpdateCustomHostnameResponseStatus = "1_not_started"
	N2Initiated            UpdateCustomHostnameResponseStatus = "2_initiated"
	N3ChallengeVerified    UpdateCustomHostnameResponseStatus = "3_challenge_verified"
	N4OriginSetupCompleted UpdateCustomHostnameResponseStatus = "4_origin_setup_completed"
	N5ServicesReconfigured UpdateCustomHostnameResponseStatus = "5_services_reconfigured"
)

// Defines values for UpdatePostgresConfigBodySessionReplicationRole.
const (
	UpdatePostgresConfigBodySessionReplicationRoleLocal   UpdatePostgresConfigBodySessionReplicationRole = "local"
	UpdatePostgresConfigBodySessionReplicationRoleOrigin  UpdatePostgresConfigBodySessionReplicationRole = "origin"
	UpdatePostgresConfigBodySessionReplicationRoleReplica UpdatePostgresConfigBodySessionReplicationRole = "replica"
)

// Defines values for UpdateSigningKeyBodyStatus.
const (
	UpdateSigningKeyBodyStatusInUse          UpdateSigningKeyBodyStatus = "in_use"
	UpdateSigningKeyBodyStatusPreviouslyUsed UpdateSigningKeyBodyStatus = "previously_used"
	UpdateSigningKeyBodyStatusRevoked        UpdateSigningKeyBodyStatus = "revoked"
	UpdateSigningKeyBodyStatusStandby        UpdateSigningKeyBodyStatus = "standby"
)

// Defines values for UpdateSupavisorConfigBodyPoolMode.
const (
	UpdateSupavisorConfigBodyPoolModeSession     UpdateSupavisorConfigBodyPoolMode = "session"
	UpdateSupavisorConfigBodyPoolModeTransaction UpdateSupavisorConfigBodyPoolMode = "transaction"
)

// Defines values for UpgradeDatabaseBodyReleaseChannel.
const (
	UpgradeDatabaseBodyReleaseChannelAlpha     UpgradeDatabaseBodyReleaseChannel = "alpha"
	UpgradeDatabaseBodyReleaseChannelBeta      UpgradeDatabaseBodyReleaseChannel = "beta"
	UpgradeDatabaseBodyReleaseChannelGa        UpgradeDatabaseBodyReleaseChannel = "ga"
	UpgradeDatabaseBodyReleaseChannelInternal  UpgradeDatabaseBodyReleaseChannel = "internal"
	UpgradeDatabaseBodyReleaseChannelPreview   UpgradeDatabaseBodyReleaseChannel = "preview"
	UpgradeDatabaseBodyReleaseChannelWithdrawn UpgradeDatabaseBodyReleaseChannel = "withdrawn"
)

// Defines values for V1BackupsResponseBackupsStatus.
const (
	V1BackupsResponseBackupsStatusARCHIVED  V1BackupsResponseBackupsStatus = "ARCHIVED"
	V1BackupsResponseBackupsStatusCANCELLED V1BackupsResponseBackupsStatus = "CANCELLED"
	V1BackupsResponseBackupsStatusCOMPLETED V1BackupsResponseBackupsStatus = "COMPLETED"
	V1BackupsResponseBackupsStatusFAILED    V1BackupsResponseBackupsStatus = "FAILED"
	V1BackupsResponseBackupsStatusPENDING   V1BackupsResponseBackupsStatus = "PENDING"
	V1BackupsResponseBackupsStatusREMOVED   V1BackupsResponseBackupsStatus = "REMOVED"
)

// Defines values for V1CreateProjectBodyDesiredInstanceSize.
const (
	V1CreateProjectBodyDesiredInstanceSizeLarge                    V1CreateProjectBodyDesiredInstanceSize = "large"
	V1CreateProjectBodyDesiredInstanceSizeMedium                   V1CreateProjectBodyDesiredInstanceSize = "medium"
	V1CreateProjectBodyDesiredInstanceSizeMicro                    V1CreateProjectBodyDesiredInstanceSize = "micro"
	V1CreateProjectBodyDesiredInstanceSizeN12xlarge                V1CreateProjectBodyDesiredInstanceSize = "12xlarge"
	V1CreateProjectBodyDesiredInstanceSizeN16xlarge                V1CreateProjectBodyDesiredInstanceSize = "16xlarge"
	V1CreateProjectBodyDesiredInstanceSizeN24xlarge                V1CreateProjectBodyDesiredInstanceSize = "24xlarge"
	V1CreateProjectBodyDesiredInstanceSizeN24xlargeHighMemory      V1CreateProjectBodyDesiredInstanceSize = "24xlarge_high_memory"
	V1CreateProjectBodyDesiredInstanceSizeN24xlargeOptimizedCpu    V1CreateProjectBodyDesiredInstanceSize = "24xlarge_optimized_cpu"
	V1CreateProjectBodyDesiredInstanceSizeN24xlargeOptimizedMemory V1CreateProjectBodyDesiredInstanceSize = "24xlarge_optimized_memory"
	V1CreateProjectBodyDesiredInstanceSizeN2xlarge                 V1CreateProjectBodyDesiredInstanceSize = "2xlarge"
	V1CreateProjectBodyDesiredInstanceSizeN48xlarge                V1CreateProjectBodyDesiredInstanceSize = "48xlarge"
	V1CreateProjectBodyDesiredInstanceSizeN48xlargeHighMemory      V1CreateProjectBodyDesiredInstanceSize = "48xlarge_high_memory"
	V1CreateProjectBodyDesiredInstanceSizeN48xlargeOptimizedCpu    V1CreateProjectBodyDesiredInstanceSize = "48xlarge_optimized_cpu"
	V1CreateProjectBodyDesiredInstanceSizeN48xlargeOptimizedMemory V1CreateProjectBodyDesiredInstanceSize = "48xlarge_optimized_memory"
	V1CreateProjectBodyDesiredInstanceSizeN4xlarge                 V1CreateProjectBodyDesiredInstanceSize = "4xlarge"
	V1CreateProjectBodyDesiredInstanceSizeN8xlarge                 V1CreateProjectBodyDesiredInstanceSize = "8xlarge"
	V1CreateProjectBodyDesiredInstanceSizePico                     V1CreateProjectBodyDesiredInstanceSize = "pico"
	V1CreateProjectBodyDesiredInstanceSizeSmall                    V1CreateProjectBodyDesiredInstanceSize = "small"
	V1CreateProjectBodyDesiredInstanceSizeXlarge                   V1CreateProjectBodyDesiredInstanceSize = "xlarge"
)

// Defines values for V1CreateProjectBodyPlan.
const (
	V1CreateProjectBodyPlanFree V1CreateProjectBodyPlan = "free"
	V1CreateProjectBodyPlanPro  V1CreateProjectBodyPlan = "pro"
)

// Defines values for V1CreateProjectBodyRegion.
const (
	V1CreateProjectBodyRegionApEast1      V1CreateProjectBodyRegion = "ap-east-1"
	V1CreateProjectBodyRegionApNortheast1 V1CreateProjectBodyRegion = "ap-northeast-1"
	V1CreateProjectBodyRegionApNortheast2 V1CreateProjectBodyRegion = "ap-northeast-2"
	V1CreateProjectBodyRegionApSouth1     V1CreateProjectBodyRegion = "ap-south-1"
	V1CreateProjectBodyRegionApSoutheast1 V1CreateProjectBodyRegion = "ap-southeast-1"
	V1CreateProjectBodyRegionApSoutheast2 V1CreateProjectBodyRegion = "ap-southeast-2"
	V1CreateProjectBodyRegionCaCentral1   V1CreateProjectBodyRegion = "ca-central-1"
	V1CreateProjectBodyRegionEuCentral1   V1CreateProjectBodyRegion = "eu-central-1"
	V1CreateProjectBodyRegionEuCentral2   V1CreateProjectBodyRegion = "eu-central-2"
	V1CreateProjectBodyRegionEuNorth1     V1CreateProjectBodyRegion = "eu-north-1"
	V1CreateProjectBodyRegionEuWest1      V1CreateProjectBodyRegion = "eu-west-1"
	V1CreateProjectBodyRegionEuWest2      V1CreateProjectBodyRegion = "eu-west-2"
	V1CreateProjectBodyRegionEuWest3      V1CreateProjectBodyRegion = "eu-west-3"
	V1CreateProjectBodyRegionSaEast1      V1CreateProjectBodyRegion = "sa-east-1"
	V1CreateProjectBodyRegionUsEast1      V1CreateProjectBodyRegion = "us-east-1"
	V1CreateProjectBodyRegionUsEast2      V1CreateProjectBodyRegion = "us-east-2"
	V1CreateProjectBodyRegionUsWest1      V1CreateProjectBodyRegion = "us-west-1"
	V1CreateProjectBodyRegionUsWest2      V1CreateProjectBodyRegion = "us-west-2"
)

// Defines values for V1OrganizationSlugResponseAllowedReleaseChannels.
const (
	V1OrganizationSlugResponseAllowedReleaseChannelsAlpha     V1OrganizationSlugResponseAllowedReleaseChannels = "alpha"
	V1OrganizationSlugResponseAllowedReleaseChannelsBeta      V1OrganizationSlugResponseAllowedReleaseChannels = "beta"
	V1OrganizationSlugResponseAllowedReleaseChannelsGa        V1OrganizationSlugResponseAllowedReleaseChannels = "ga"
	V1OrganizationSlugResponseAllowedReleaseChannelsInternal  V1OrganizationSlugResponseAllowedReleaseChannels = "internal"
	V1OrganizationSlugResponseAllowedReleaseChannelsPreview   V1OrganizationSlugResponseAllowedReleaseChannels = "preview"
	V1OrganizationSlugResponseAllowedReleaseChannelsWithdrawn V1OrganizationSlugResponseAllowedReleaseChannels = "withdrawn"
)

// Defines values for V1OrganizationSlugResponseOptInTags.
const (
	AISQLGENERATOROPTIN V1OrganizationSlugResponseOptInTags = "AI_SQL_GENERATOR_OPT_IN"
)

// Defines values for V1OrganizationSlugResponsePlan.
const (
	V1OrganizationSlugResponsePlanEnterprise V1OrganizationSlugResponsePlan = "enterprise"
	V1OrganizationSlugResponsePlanFree       V1OrganizationSlugResponsePlan = "free"
	V1OrganizationSlugResponsePlanPro        V1OrganizationSlugResponsePlan = "pro"
	V1OrganizationSlugResponsePlanTeam       V1OrganizationSlugResponsePlan = "team"
)

// Defines values for V1PgbouncerConfigResponsePoolMode.
const (
	Session     V1PgbouncerConfigResponsePoolMode = "session"
	Statement   V1PgbouncerConfigResponsePoolMode = "statement"
	Transaction V1PgbouncerConfigResponsePoolMode = "transaction"
)

// Defines values for V1ProjectAdvisorsResponseLintsCategories.
const (
	PERFORMANCE V1ProjectAdvisorsResponseLintsCategories = "PERFORMANCE"
	SECURITY    V1ProjectAdvisorsResponseLintsCategories = "SECURITY"
)

// Defines values for V1ProjectAdvisorsResponseLintsFacing.
const (
	EXTERNAL V1ProjectAdvisorsResponseLintsFacing = "EXTERNAL"
)

// Defines values for V1ProjectAdvisorsResponseLintsLevel.
const (
	ERROR V1ProjectAdvisorsResponseLintsLevel = "ERROR"
	INFO  V1ProjectAdvisorsResponseLintsLevel = "INFO"
	WARN  V1ProjectAdvisorsResponseLintsLevel = "WARN"
)

// Defines values for V1ProjectAdvisorsResponseLintsMetadataType.
const (
	V1ProjectAdvisorsResponseLintsMetadataTypeAuth       V1ProjectAdvisorsResponseLintsMetadataType = "auth"
	V1ProjectAdvisorsResponseLintsMetadataTypeCompliance V1ProjectAdvisorsResponseLintsMetadataType = "compliance"
	V1ProjectAdvisorsResponseLintsMetadataTypeExtension  V1ProjectAdvisorsResponseLintsMetadataType = "extension"
	V1ProjectAdvisorsResponseLintsMetadataTypeFunction   V1ProjectAdvisorsResponseLintsMetadataType = "function"
	V1ProjectAdvisorsResponseLintsMetadataTypeTable      V1ProjectAdvisorsResponseLintsMetadataType = "table"
	V1ProjectAdvisorsResponseLintsMetadataTypeView       V1ProjectAdvisorsResponseLintsMetadataType = "view"
)

// Defines values for V1ProjectAdvisorsResponseLintsName.
const (
	AuthInsufficientMfaOptions    V1ProjectAdvisorsResponseLintsName = "auth_insufficient_mfa_options"
	AuthLeakedPasswordProtection  V1ProjectAdvisorsResponseLintsName = "auth_leaked_password_protection"
	AuthOtpLongExpiry             V1ProjectAdvisorsResponseLintsName = "auth_otp_long_expiry"
	AuthOtpShortLength            V1ProjectAdvisorsResponseLintsName = "auth_otp_short_length"
	AuthPasswordPolicyMissing     V1ProjectAdvisorsResponseLintsName = "auth_password_policy_missing"
	AuthRlsInitplan               V1ProjectAdvisorsResponseLintsName = "auth_rls_initplan"
	AuthUsersExposed              V1ProjectAdvisorsResponseLintsName = "auth_users_exposed"
	DuplicateIndex                V1ProjectAdvisorsResponseLintsName = "duplicate_index"
	ExtensionInPublic             V1ProjectAdvisorsResponseLintsName = "extension_in_public"
	ForeignTableInApi             V1ProjectAdvisorsResponseLintsName = "foreign_table_in_api"
	FunctionSearchPathMutable     V1ProjectAdvisorsResponseLintsName = "function_search_path_mutable"
	LeakedServiceKey              V1ProjectAdvisorsResponseLintsName = "leaked_service_key"
	MaterializedViewInApi         V1ProjectAdvisorsResponseLintsName = "materialized_view_in_api"
	MultiplePermissivePolicies    V1ProjectAdvisorsResponseLintsName = "multiple_permissive_policies"
	NetworkRestrictionsNotSet     V1ProjectAdvisorsResponseLintsName = "network_restrictions_not_set"
	NoBackupAdmin                 V1ProjectAdvisorsResponseLintsName = "no_backup_admin"
	NoPrimaryKey                  V1ProjectAdvisorsResponseLintsName = "no_primary_key"
	PasswordRequirementsMinLength V1ProjectAdvisorsResponseLintsName = "password_requirements_min_length"
	PitrNotEnabled                V1ProjectAdvisorsResponseLintsName = "pitr_not_enabled"
	PolicyExistsRlsDisabled       V1ProjectAdvisorsResponseLintsName = "policy_exists_rls_disabled"
	RlsDisabledInPublic           V1ProjectAdvisorsResponseLintsName = "rls_disabled_in_public"
	RlsEnabledNoPolicy            V1ProjectAdvisorsResponseLintsName = "rls_enabled_no_policy"
	RlsReferencesUserMetadata     V1ProjectAdvisorsResponseLintsName = "rls_references_user_metadata"
	SecurityDefinerView           V1ProjectAdvisorsResponseLintsName = "security_definer_view"
	SslNotEnforced                V1ProjectAdvisorsResponseLintsName = "ssl_not_enforced"
	UnindexedForeignKeys          V1ProjectAdvisorsResponseLintsName = "unindexed_foreign_keys"
	UnsupportedRegTypes           V1ProjectAdvisorsResponseLintsName = "unsupported_reg_types"
	UnusedIndex                   V1ProjectAdvisorsResponseLintsName = "unused_index"
)

// Defines values for V1ProjectResponseStatus.
const (
	V1ProjectResponseStatusACTIVEHEALTHY   V1ProjectResponseStatus = "ACTIVE_HEALTHY"
	V1ProjectResponseStatusACTIVEUNHEALTHY V1ProjectResponseStatus = "ACTIVE_UNHEALTHY"
	V1ProjectResponseStatusCOMINGUP        V1ProjectResponseStatus = "COMING_UP"
	V1ProjectResponseStatusGOINGDOWN       V1ProjectResponseStatus = "GOING_DOWN"
	V1ProjectResponseStatusINACTIVE        V1ProjectResponseStatus = "INACTIVE"
	V1ProjectResponseStatusINITFAILED      V1ProjectResponseStatus = "INIT_FAILED"
	V1ProjectResponseStatusPAUSEFAILED     V1ProjectResponseStatus = "PAUSE_FAILED"
	V1ProjectResponseStatusPAUSING         V1ProjectResponseStatus = "PAUSING"
	V1ProjectResponseStatusREMOVED         V1ProjectResponseStatus = "REMOVED"
	V1ProjectResponseStatusRESIZING        V1ProjectResponseStatus = "RESIZING"
	V1ProjectResponseStatusRESTARTING      V1ProjectResponseStatus = "RESTARTING"
	V1ProjectResponseStatusRESTOREFAILED   V1ProjectResponseStatus = "RESTORE_FAILED"
	V1ProjectResponseStatusRESTORING       V1ProjectResponseStatus = "RESTORING"
	V1ProjectResponseStatusUNKNOWN         V1ProjectResponseStatus = "UNKNOWN"
	V1ProjectResponseStatusUPGRADING       V1ProjectResponseStatus = "UPGRADING"
)

// Defines values for V1ProjectWithDatabaseResponseStatus.
const (
	V1ProjectWithDatabaseResponseStatusACTIVEHEALTHY   V1ProjectWithDatabaseResponseStatus = "ACTIVE_HEALTHY"
	V1ProjectWithDatabaseResponseStatusACTIVEUNHEALTHY V1ProjectWithDatabaseResponseStatus = "ACTIVE_UNHEALTHY"
	V1ProjectWithDatabaseResponseStatusCOMINGUP        V1ProjectWithDatabaseResponseStatus = "COMING_UP"
	V1ProjectWithDatabaseResponseStatusGOINGDOWN       V1ProjectWithDatabaseResponseStatus = "GOING_DOWN"
	V1ProjectWithDatabaseResponseStatusINACTIVE        V1ProjectWithDatabaseResponseStatus = "INACTIVE"
	V1ProjectWithDatabaseResponseStatusINITFAILED      V1ProjectWithDatabaseResponseStatus = "INIT_FAILED"
	V1ProjectWithDatabaseResponseStatusPAUSEFAILED     V1ProjectWithDatabaseResponseStatus = "PAUSE_FAILED"
	V1ProjectWithDatabaseResponseStatusPAUSING         V1ProjectWithDatabaseResponseStatus = "PAUSING"
	V1ProjectWithDatabaseResponseStatusREMOVED         V1ProjectWithDatabaseResponseStatus = "REMOVED"
	V1ProjectWithDatabaseResponseStatusRESIZING        V1ProjectWithDatabaseResponseStatus = "RESIZING"
	V1ProjectWithDatabaseResponseStatusRESTARTING      V1ProjectWithDatabaseResponseStatus = "RESTARTING"
	V1ProjectWithDatabaseResponseStatusRESTOREFAILED   V1ProjectWithDatabaseResponseStatus = "RESTORE_FAILED"
	V1ProjectWithDatabaseResponseStatusRESTORING       V1ProjectWithDatabaseResponseStatus = "RESTORING"
	V1ProjectWithDatabaseResponseStatusUNKNOWN         V1ProjectWithDatabaseResponseStatus = "UNKNOWN"
	V1ProjectWithDatabaseResponseStatusUPGRADING       V1ProjectWithDatabaseResponseStatus = "UPGRADING"
)

// Defines values for V1ServiceHealthResponseInfo0Name.
const (
	GoTrue V1ServiceHealthResponseInfo0Name = "GoTrue"
)

// Defines values for V1ServiceHealthResponseName.
const (
	V1ServiceHealthResponseNameAuth     V1ServiceHealthResponseName = "auth"
	V1ServiceHealthResponseNameDb       V1ServiceHealthResponseName = "db"
	V1ServiceHealthResponseNamePooler   V1ServiceHealthResponseName = "pooler"
	V1ServiceHealthResponseNameRealtime V1ServiceHealthResponseName = "realtime"
	V1ServiceHealthResponseNameRest     V1ServiceHealthResponseName = "rest"
	V1ServiceHealthResponseNameStorage  V1ServiceHealthResponseName = "storage"
)

// Defines values for V1ServiceHealthResponseStatus.
const (
	V1ServiceHealthResponseStatusACTIVEHEALTHY V1ServiceHealthResponseStatus = "ACTIVE_HEALTHY"
	V1ServiceHealthResponseStatusCOMINGUP      V1ServiceHealthResponseStatus = "COMING_UP"
	V1ServiceHealthResponseStatusUNHEALTHY     V1ServiceHealthResponseStatus = "UNHEALTHY"
)

// Defines values for VanitySubdomainConfigResponseStatus.
const (
	Active           VanitySubdomainConfigResponseStatus = "active"
	CustomDomainUsed VanitySubdomainConfigResponseStatus = "custom-domain-used"
	NotUsed          VanitySubdomainConfigResponseStatus = "not-used"
)

// Defines values for V1AuthorizeUserParamsResponseType.
const (
	Code         V1AuthorizeUserParamsResponseType = "code"
	IdTokenToken V1AuthorizeUserParamsResponseType = "id_token token"
	Token        V1AuthorizeUserParamsResponseType = "token"
)

// Defines values for V1AuthorizeUserParamsCodeChallengeMethod.
const (
	Plain  V1AuthorizeUserParamsCodeChallengeMethod = "plain"
	S256   V1AuthorizeUserParamsCodeChallengeMethod = "S256"
	Sha256 V1AuthorizeUserParamsCodeChallengeMethod = "sha256"
)

// Defines values for GetApiCountsParamsInterval.
const (
	N15min GetApiCountsParamsInterval = "15min"
	N1day  GetApiCountsParamsInterval = "1day"
	N1hr   GetApiCountsParamsInterval = "1hr"
	N30min GetApiCountsParamsInterval = "30min"
	N3day  GetApiCountsParamsInterval = "3day"
	N3hr   GetApiCountsParamsInterval = "3hr"
	N7day  GetApiCountsParamsInterval = "7day"
)

// Defines values for V1GetServicesHealthParamsServices.
const (
	Auth     V1GetServicesHealthParamsServices = "auth"
	Db       V1GetServicesHealthParamsServices = "db"
	Pooler   V1GetServicesHealthParamsServices = "pooler"
	Realtime V1GetServicesHealthParamsServices = "realtime"
	Rest     V1GetServicesHealthParamsServices = "rest"
	Storage  V1GetServicesHealthParamsServices = "storage"
)

// Defines values for V1ListAllSnippetsParamsSortBy.
const (
	InsertedAt V1ListAllSnippetsParamsSortBy = "inserted_at"
	Name       V1ListAllSnippetsParamsSortBy = "name"
)

// Defines values for V1ListAllSnippetsParamsSortOrder.
const (
	Asc  V1ListAllSnippetsParamsSortOrder = "asc"
	Desc V1ListAllSnippetsParamsSortOrder = "desc"
)

// ActivateVanitySubdomainResponse defines model for ActivateVanitySubdomainResponse.
type ActivateVanitySubdomainResponse struct {
	CustomDomain string `json:"custom_domain"`
}

// AnalyticsResponse defines model for AnalyticsResponse.
type AnalyticsResponse struct {
	Error  *AnalyticsResponse_Error `json:"error,omitempty"`
	Result *[]interface{}           `json:"result,omitempty"`
}

// AnalyticsResponseError0 defines model for .
type AnalyticsResponseError0 = string

// AnalyticsResponseError1 defines model for .
type AnalyticsResponseError1 struct {
	Code   float32 `json:"code"`
	Errors []struct {
		Domain       string `json:"domain"`
		Location     string `json:"location"`
		LocationType string `json:"locationType"`
		Message      string `json:"message"`
		Reason       string `json:"reason"`
	} `json:"errors"`
	Message string `json:"message"`
	Status  string `json:"status"`
}

// AnalyticsResponse_Error defines model for AnalyticsResponse.Error.
type AnalyticsResponse_Error struct {
	union json.RawMessage
}

// ApiKeyResponse defines model for ApiKeyResponse.
type ApiKeyResponse struct {
	ApiKey            string                       `json:"api_key"`
	Description       nullable.Nullable[string]    `json:"description,omitempty"`
	Hash              nullable.Nullable[string]    `json:"hash,omitempty"`
	Id                nullable.Nullable[string]    `json:"id,omitempty"`
	InsertedAt        nullable.Nullable[time.Time] `json:"inserted_at,omitempty"`
	Name              string                       `json:"name"`
	Prefix            nullable.Nullable[string]    `json:"prefix,omitempty"`
	SecretJwtTemplate nullable.Nullable[struct {
		Role string `json:"role"`
	}] `json:"secret_jwt_template,omitempty"`
	Type      nullable.Nullable[ApiKeyResponseType] `json:"type,omitempty"`
	UpdatedAt nullable.Nullable[time.Time]          `json:"updated_at,omitempty"`
}

// ApiKeyResponseType defines model for ApiKeyResponse.Type.
type ApiKeyResponseType string

// ApplyProjectAddonBody defines model for ApplyProjectAddonBody.
type ApplyProjectAddonBody struct {
	AddonType    ApplyProjectAddonBodyAddonType     `json:"addon_type"`
	AddonVariant ApplyProjectAddonBody_AddonVariant `json:"addon_variant"`
}

// ApplyProjectAddonBodyAddonType defines model for ApplyProjectAddonBody.AddonType.
type ApplyProjectAddonBodyAddonType string

// ApplyProjectAddonBodyAddonVariant0 defines model for ApplyProjectAddonBody.AddonVariant.0.
type ApplyProjectAddonBodyAddonVariant0 string

// ApplyProjectAddonBodyAddonVariant1 defines model for ApplyProjectAddonBody.AddonVariant.1.
type ApplyProjectAddonBodyAddonVariant1 string

// ApplyProjectAddonBodyAddonVariant2 defines model for ApplyProjectAddonBody.AddonVariant.2.
type ApplyProjectAddonBodyAddonVariant2 string

// ApplyProjectAddonBodyAddonVariant3 defines model for ApplyProjectAddonBody.AddonVariant.3.
type ApplyProjectAddonBodyAddonVariant3 string

// ApplyProjectAddonBody_AddonVariant defines model for ApplyProjectAddonBody.AddonVariant.
type ApplyProjectAddonBody_AddonVariant struct {
	union json.RawMessage
}

// AuthConfigResponse defines model for AuthConfigResponse.
type AuthConfigResponse struct {
	ApiMaxRequestDuration                         nullable.Nullable[int]                                          `json:"api_max_request_duration"`
	DbMaxPoolSize                                 nullable.Nullable[int]                                          `json:"db_max_pool_size"`
	DisableSignup                                 nullable.Nullable[bool]                                         `json:"disable_signup"`
	ExternalAnonymousUsersEnabled                 nullable.Nullable[bool]                                         `json:"external_anonymous_users_enabled"`
	ExternalAppleAdditionalClientIds              nullable.Nullable[string]                                       `json:"external_apple_additional_client_ids"`
	ExternalAppleClientId                         nullable.Nullable[string]                                       `json:"external_apple_client_id"`
	ExternalAppleEnabled                          nullable.Nullable[bool]                                         `json:"external_apple_enabled"`
	ExternalAppleSecret                           nullable.Nullable[string]                                       `json:"external_apple_secret"`
	ExternalAzureClientId                         nullable.Nullable[string]                                       `json:"external_azure_client_id"`
	ExternalAzureEnabled                          nullable.Nullable[bool]                                         `json:"external_azure_enabled"`
	ExternalAzureSecret                           nullable.Nullable[string]                                       `json:"external_azure_secret"`
	ExternalAzureUrl                              nullable.Nullable[string]                                       `json:"external_azure_url"`
	ExternalBitbucketClientId                     nullable.Nullable[string]                                       `json:"external_bitbucket_client_id"`
	ExternalBitbucketEnabled                      nullable.Nullable[bool]                                         `json:"external_bitbucket_enabled"`
	ExternalBitbucketSecret                       nullable.Nullable[string]                                       `json:"external_bitbucket_secret"`
	ExternalDiscordClientId                       nullable.Nullable[string]                                       `json:"external_discord_client_id"`
	ExternalDiscordEnabled                        nullable.Nullable[bool]                                         `json:"external_discord_enabled"`
	ExternalDiscordSecret                         nullable.Nullable[string]                                       `json:"external_discord_secret"`
	ExternalEmailEnabled                          nullable.Nullable[bool]                                         `json:"external_email_enabled"`
	ExternalFacebookClientId                      nullable.Nullable[string]                                       `json:"external_facebook_client_id"`
	ExternalFacebookEnabled                       nullable.Nullable[bool]                                         `json:"external_facebook_enabled"`
	ExternalFacebookSecret                        nullable.Nullable[string]                                       `json:"external_facebook_secret"`
	ExternalFigmaClientId                         nullable.Nullable[string]                                       `json:"external_figma_client_id"`
	ExternalFigmaEnabled                          nullable.Nullable[bool]                                         `json:"external_figma_enabled"`
	ExternalFigmaSecret                           nullable.Nullable[string]                                       `json:"external_figma_secret"`
	ExternalGithubClientId                        nullable.Nullable[string]                                       `json:"external_github_client_id"`
	ExternalGithubEnabled                         nullable.Nullable[bool]                                         `json:"external_github_enabled"`
	ExternalGithubSecret                          nullable.Nullable[string]                                       `json:"external_github_secret"`
	ExternalGitlabClientId                        nullable.Nullable[string]                                       `json:"external_gitlab_client_id"`
	ExternalGitlabEnabled                         nullable.Nullable[bool]                                         `json:"external_gitlab_enabled"`
	ExternalGitlabSecret                          nullable.Nullable[string]                                       `json:"external_gitlab_secret"`
	ExternalGitlabUrl                             nullable.Nullable[string]                                       `json:"external_gitlab_url"`
	ExternalGoogleAdditionalClientIds             nullable.Nullable[string]                                       `json:"external_google_additional_client_ids"`
	ExternalGoogleClientId                        nullable.Nullable[string]                                       `json:"external_google_client_id"`
	ExternalGoogleEnabled                         nullable.Nullable[bool]                                         `json:"external_google_enabled"`
	ExternalGoogleSecret                          nullable.Nullable[string]                                       `json:"external_google_secret"`
	ExternalGoogleSkipNonceCheck                  nullable.Nullable[bool]                                         `json:"external_google_skip_nonce_check"`
	ExternalKakaoClientId                         nullable.Nullable[string]                                       `json:"external_kakao_client_id"`
	ExternalKakaoEnabled                          nullable.Nullable[bool]                                         `json:"external_kakao_enabled"`
	ExternalKakaoSecret                           nullable.Nullable[string]                                       `json:"external_kakao_secret"`
	ExternalKeycloakClientId                      nullable.Nullable[string]                                       `json:"external_keycloak_client_id"`
	ExternalKeycloakEnabled                       nullable.Nullable[bool]                                         `json:"external_keycloak_enabled"`
	ExternalKeycloakSecret                        nullable.Nullable[string]                                       `json:"external_keycloak_secret"`
	ExternalKeycloakUrl                           nullable.Nullable[string]                                       `json:"external_keycloak_url"`
	ExternalLinkedinOidcClientId                  nullable.Nullable[string]                                       `json:"external_linkedin_oidc_client_id"`
	ExternalLinkedinOidcEnabled                   nullable.Nullable[bool]                                         `json:"external_linkedin_oidc_enabled"`
	ExternalLinkedinOidcSecret                    nullable.Nullable[string]                                       `json:"external_linkedin_oidc_secret"`
	ExternalNotionClientId                        nullable.Nullable[string]                                       `json:"external_notion_client_id"`
	ExternalNotionEnabled                         nullable.Nullable[bool]                                         `json:"external_notion_enabled"`
	ExternalNotionSecret                          nullable.Nullable[string]                                       `json:"external_notion_secret"`
	ExternalPhoneEnabled                          nullable.Nullable[bool]                                         `json:"external_phone_enabled"`
	ExternalSlackClientId                         nullable.Nullable[string]                                       `json:"external_slack_client_id"`
	ExternalSlackEnabled                          nullable.Nullable[bool]                                         `json:"external_slack_enabled"`
	ExternalSlackOidcClientId                     nullable.Nullable[string]                                       `json:"external_slack_oidc_client_id"`
	ExternalSlackOidcEnabled                      nullable.Nullable[bool]                                         `json:"external_slack_oidc_enabled"`
	ExternalSlackOidcSecret                       nullable.Nullable[string]                                       `json:"external_slack_oidc_secret"`
	ExternalSlackSecret                           nullable.Nullable[string]                                       `json:"external_slack_secret"`
	ExternalSpotifyClientId                       nullable.Nullable[string]                                       `json:"external_spotify_client_id"`
	ExternalSpotifyEnabled                        nullable.Nullable[bool]                                         `json:"external_spotify_enabled"`
	ExternalSpotifySecret                         nullable.Nullable[string]                                       `json:"external_spotify_secret"`
	ExternalTwitchClientId                        nullable.Nullable[string]                                       `json:"external_twitch_client_id"`
	ExternalTwitchEnabled                         nullable.Nullable[bool]                                         `json:"external_twitch_enabled"`
	ExternalTwitchSecret                          nullable.Nullable[string]                                       `json:"external_twitch_secret"`
	ExternalTwitterClientId                       nullable.Nullable[string]                                       `json:"external_twitter_client_id"`
	ExternalTwitterEnabled                        nullable.Nullable[bool]                                         `json:"external_twitter_enabled"`
	ExternalTwitterSecret                         nullable.Nullable[string]                                       `json:"external_twitter_secret"`
	ExternalWeb3SolanaEnabled                     nullable.Nullable[bool]                                         `json:"external_web3_solana_enabled"`
	ExternalWorkosClientId                        nullable.Nullable[string]                                       `json:"external_workos_client_id"`
	ExternalWorkosEnabled                         nullable.Nullable[bool]                                         `json:"external_workos_enabled"`
	ExternalWorkosSecret                          nullable.Nullable[string]                                       `json:"external_workos_secret"`
	ExternalWorkosUrl                             nullable.Nullable[string]                                       `json:"external_workos_url"`
	ExternalZoomClientId                          nullable.Nullable[string]                                       `json:"external_zoom_client_id"`
	ExternalZoomEnabled                           nullable.Nullable[bool]                                         `json:"external_zoom_enabled"`
	ExternalZoomSecret                            nullable.Nullable[string]                                       `json:"external_zoom_secret"`
	HookCustomAccessTokenEnabled                  nullable.Nullable[bool]                                         `json:"hook_custom_access_token_enabled"`
	HookCustomAccessTokenSecrets                  nullable.Nullable[string]                                       `json:"hook_custom_access_token_secrets"`
	HookCustomAccessTokenUri                      nullable.Nullable[string]                                       `json:"hook_custom_access_token_uri"`
	HookMfaVerificationAttemptEnabled             nullable.Nullable[bool]                                         `json:"hook_mfa_verification_attempt_enabled"`
	HookMfaVerificationAttemptSecrets             nullable.Nullable[string]                                       `json:"hook_mfa_verification_attempt_secrets"`
	HookMfaVerificationAttemptUri                 nullable.Nullable[string]                                       `json:"hook_mfa_verification_attempt_uri"`
	HookPasswordVerificationAttemptEnabled        nullable.Nullable[bool]                                         `json:"hook_password_verification_attempt_enabled"`
	HookPasswordVerificationAttemptSecrets        nullable.Nullable[string]                                       `json:"hook_password_verification_attempt_secrets"`
	HookPasswordVerificationAttemptUri            nullable.Nullable[string]                                       `json:"hook_password_verification_attempt_uri"`
	HookSendEmailEnabled                          nullable.Nullable[bool]                                         `json:"hook_send_email_enabled"`
	HookSendEmailSecrets                          nullable.Nullable[string]                                       `json:"hook_send_email_secrets"`
	HookSendEmailUri                              nullable.Nullable[string]                                       `json:"hook_send_email_uri"`
	HookSendSmsEnabled                            nullable.Nullable[bool]                                         `json:"hook_send_sms_enabled"`
	HookSendSmsSecrets                            nullable.Nullable[string]                                       `json:"hook_send_sms_secrets"`
	HookSendSmsUri                                nullable.Nullable[string]                                       `json:"hook_send_sms_uri"`
	JwtExp                                        nullable.Nullable[int]                                          `json:"jwt_exp"`
	MailerAllowUnverifiedEmailSignIns             nullable.Nullable[bool]                                         `json:"mailer_allow_unverified_email_sign_ins"`
	MailerAutoconfirm                             nullable.Nullable[bool]                                         `json:"mailer_autoconfirm"`
	MailerOtpExp                                  int                                                             `json:"mailer_otp_exp"`
	MailerOtpLength                               nullable.Nullable[int]                                          `json:"mailer_otp_length"`
	MailerSecureEmailChangeEnabled                nullable.Nullable[bool]                                         `json:"mailer_secure_email_change_enabled"`
	MailerSubjectsConfirmation                    nullable.Nullable[string]                                       `json:"mailer_subjects_confirmation"`
	MailerSubjectsEmailChange                     nullable.Nullable[string]                                       `json:"mailer_subjects_email_change"`
	MailerSubjectsInvite                          nullable.Nullable[string]                                       `json:"mailer_subjects_invite"`
	MailerSubjectsMagicLink                       nullable.Nullable[string]                                       `json:"mailer_subjects_magic_link"`
	MailerSubjectsReauthentication                nullable.Nullable[string]                                       `json:"mailer_subjects_reauthentication"`
	MailerSubjectsRecovery                        nullable.Nullable[string]                                       `json:"mailer_subjects_recovery"`
	MailerTemplatesConfirmationContent            nullable.Nullable[string]                                       `json:"mailer_templates_confirmation_content"`
	MailerTemplatesEmailChangeContent             nullable.Nullable[string]                                       `json:"mailer_templates_email_change_content"`
	MailerTemplatesInviteContent                  nullable.Nullable[string]                                       `json:"mailer_templates_invite_content"`
	MailerTemplatesMagicLinkContent               nullable.Nullable[string]                                       `json:"mailer_templates_magic_link_content"`
	MailerTemplatesReauthenticationContent        nullable.Nullable[string]                                       `json:"mailer_templates_reauthentication_content"`
	MailerTemplatesRecoveryContent                nullable.Nullable[string]                                       `json:"mailer_templates_recovery_content"`
	MfaMaxEnrolledFactors                         nullable.Nullable[int]                                          `json:"mfa_max_enrolled_factors"`
	MfaPhoneEnrollEnabled                         nullable.Nullable[bool]                                         `json:"mfa_phone_enroll_enabled"`
	MfaPhoneMaxFrequency                          nullable.Nullable[int]                                          `json:"mfa_phone_max_frequency"`
	MfaPhoneOtpLength                             int                                                             `json:"mfa_phone_otp_length"`
	MfaPhoneTemplate                              nullable.Nullable[string]                                       `json:"mfa_phone_template"`
	MfaPhoneVerifyEnabled                         nullable.Nullable[bool]                                         `json:"mfa_phone_verify_enabled"`
	MfaTotpEnrollEnabled                          nullable.Nullable[bool]                                         `json:"mfa_totp_enroll_enabled"`
	MfaTotpVerifyEnabled                          nullable.Nullable[bool]                                         `json:"mfa_totp_verify_enabled"`
	MfaWebAuthnEnrollEnabled                      nullable.Nullable[bool]                                         `json:"mfa_web_authn_enroll_enabled"`
	MfaWebAuthnVerifyEnabled                      nullable.Nullable[bool]                                         `json:"mfa_web_authn_verify_enabled"`
	PasswordHibpEnabled                           nullable.Nullable[bool]                                         `json:"password_hibp_enabled"`
	PasswordMinLength                             nullable.Nullable[int]                                          `json:"password_min_length"`
	PasswordRequiredCharacters                    nullable.Nullable[AuthConfigResponsePasswordRequiredCharacters] `json:"password_required_characters"`
	RateLimitAnonymousUsers                       nullable.Nullable[int]                                          `json:"rate_limit_anonymous_users"`
	RateLimitEmailSent                            nullable.Nullable[int]                                          `json:"rate_limit_email_sent"`
	RateLimitOtp                                  nullable.Nullable[int]                                          `json:"rate_limit_otp"`
	RateLimitSmsSent                              nullable.Nullable[int]                                          `json:"rate_limit_sms_sent"`
	RateLimitTokenRefresh                         nullable.Nullable[int]                                          `json:"rate_limit_token_refresh"`
	RateLimitVerify                               nullable.Nullable[int]                                          `json:"rate_limit_verify"`
	RateLimitWeb3                                 nullable.Nullable[int]                                          `json:"rate_limit_web3"`
	RefreshTokenRotationEnabled                   nullable.Nullable[bool]                                         `json:"refresh_token_rotation_enabled"`
	SamlAllowEncryptedAssertions                  nullable.Nullable[bool]                                         `json:"saml_allow_encrypted_assertions"`
	SamlEnabled                                   nullable.Nullable[bool]                                         `json:"saml_enabled"`
	SamlExternalUrl                               nullable.Nullable[string]                                       `json:"saml_external_url"`
	SecurityCaptchaEnabled                        nullable.Nullable[bool]                                         `json:"security_captcha_enabled"`
	SecurityCaptchaProvider                       nullable.Nullable[AuthConfigResponseSecurityCaptchaProvider]    `json:"security_captcha_provider"`
	SecurityCaptchaSecret                         nullable.Nullable[string]                                       `json:"security_captcha_secret"`
	SecurityManualLinkingEnabled                  nullable.Nullable[bool]                                         `json:"security_manual_linking_enabled"`
	SecurityRefreshTokenReuseInterval             nullable.Nullable[int]                                          `json:"security_refresh_token_reuse_interval"`
	SecurityUpdatePasswordRequireReauthentication nullable.Nullable[bool]                                         `json:"security_update_password_require_reauthentication"`
	SessionsInactivityTimeout                     nullable.Nullable[int]                                          `json:"sessions_inactivity_timeout"`
	SessionsSinglePerUser                         nullable.Nullable[bool]                                         `json:"sessions_single_per_user"`
	SessionsTags                                  nullable.Nullable[string]                                       `json:"sessions_tags"`
	SessionsTimebox                               nullable.Nullable[int]                                          `json:"sessions_timebox"`
	SiteUrl                                       nullable.Nullable[string]                                       `json:"site_url"`
	SmsAutoconfirm                                nullable.Nullable[bool]                                         `json:"sms_autoconfirm"`
	SmsMaxFrequency                               nullable.Nullable[int]                                          `json:"sms_max_frequency"`
	SmsMessagebirdAccessKey                       nullable.Nullable[string]                                       `json:"sms_messagebird_access_key"`
	SmsMessagebirdOriginator                      nullable.Nullable[string]                                       `json:"sms_messagebird_originator"`
	SmsOtpExp                                     nullable.Nullable[int]                                          `json:"sms_otp_exp"`
	SmsOtpLength                                  int                                                             `json:"sms_otp_length"`
	SmsProvider                                   nullable.Nullable[AuthConfigResponseSmsProvider]                `json:"sms_provider"`
	SmsTemplate                                   nullable.Nullable[string]                                       `json:"sms_template"`
	SmsTestOtp                                    nullable.Nullable[string]                                       `json:"sms_test_otp"`
	SmsTestOtpValidUntil                          nullable.Nullable[time.Time]                                    `json:"sms_test_otp_valid_until"`
	SmsTextlocalApiKey                            nullable.Nullable[string]                                       `json:"sms_textlocal_api_key"`
	SmsTextlocalSender                            nullable.Nullable[string]                                       `json:"sms_textlocal_sender"`
	SmsTwilioAccountSid                           nullable.Nullable[string]                                       `json:"sms_twilio_account_sid"`
	SmsTwilioAuthToken                            nullable.Nullable[string]                                       `json:"sms_twilio_auth_token"`
	SmsTwilioContentSid                           nullable.Nullable[string]                                       `json:"sms_twilio_content_sid"`
	SmsTwilioMessageServiceSid                    nullable.Nullable[string]                                       `json:"sms_twilio_message_service_sid"`
	SmsTwilioVerifyAccountSid                     nullable.Nullable[string]                                       `json:"sms_twilio_verify_account_sid"`
	SmsTwilioVerifyAuthToken                      nullable.Nullable[string]                                       `json:"sms_twilio_verify_auth_token"`
	SmsTwilioVerifyMessageServiceSid              nullable.Nullable[string]                                       `json:"sms_twilio_verify_message_service_sid"`
	SmsVonageApiKey                               nullable.Nullable[string]                                       `json:"sms_vonage_api_key"`
	SmsVonageApiSecret                            nullable.Nullable[string]                                       `json:"sms_vonage_api_secret"`
	SmsVonageFrom                                 nullable.Nullable[string]                                       `json:"sms_vonage_from"`
	SmtpAdminEmail                                nullable.Nullable[string]                                       `json:"smtp_admin_email"`
	SmtpHost                                      nullable.Nullable[string]                                       `json:"smtp_host"`
	SmtpMaxFrequency                              nullable.Nullable[int]                                          `json:"smtp_max_frequency"`
	SmtpPass                                      nullable.Nullable[string]                                       `json:"smtp_pass"`
	SmtpPort                                      nullable.Nullable[string]                                       `json:"smtp_port"`
	SmtpSenderName                                nullable.Nullable[string]                                       `json:"smtp_sender_name"`
	SmtpUser                                      nullable.Nullable[string]                                       `json:"smtp_user"`
	UriAllowList                                  nullable.Nullable[string]                                       `json:"uri_allow_list"`
}

// AuthConfigResponsePasswordRequiredCharacters defines model for AuthConfigResponse.PasswordRequiredCharacters.
type AuthConfigResponsePasswordRequiredCharacters string

// AuthConfigResponseSecurityCaptchaProvider defines model for AuthConfigResponse.SecurityCaptchaProvider.
type AuthConfigResponseSecurityCaptchaProvider string

// AuthConfigResponseSmsProvider defines model for AuthConfigResponse.SmsProvider.
type AuthConfigResponseSmsProvider string

// BranchActionBody defines model for BranchActionBody.
type BranchActionBody struct {
	MigrationVersion *string `json:"migration_version,omitempty"`
}

// BranchDeleteResponse defines model for BranchDeleteResponse.
type BranchDeleteResponse struct {
	Message BranchDeleteResponseMessage `json:"message"`
}

// BranchDeleteResponseMessage defines model for BranchDeleteResponse.Message.
type BranchDeleteResponseMessage string

// BranchDetailResponse defines model for BranchDetailResponse.
type BranchDetailResponse struct {
	DbHost          string                     `json:"db_host"`
	DbPass          *string                    `json:"db_pass,omitempty"`
	DbPort          int                        `json:"db_port"`
	DbUser          *string                    `json:"db_user,omitempty"`
	JwtSecret       *string                    `json:"jwt_secret,omitempty"`
	PostgresEngine  string                     `json:"postgres_engine"`
	PostgresVersion string                     `json:"postgres_version"`
	Ref             string                     `json:"ref"`
	ReleaseChannel  string                     `json:"release_channel"`
	Status          BranchDetailResponseStatus `json:"status"`
}

// BranchDetailResponseStatus defines model for BranchDetailResponse.Status.
type BranchDetailResponseStatus string

// BranchResponse defines model for BranchResponse.
type BranchResponse struct {
	CreatedAt string  `json:"created_at"`
	GitBranch *string `json:"git_branch,omitempty"`
	Id        string  `json:"id"`
	IsDefault bool    `json:"is_default"`

	// LatestCheckRunId This field is deprecated and will not be populated.
	// Deprecated:
	LatestCheckRunId *float32             `json:"latest_check_run_id,omitempty"`
	Name             string               `json:"name"`
	ParentProjectRef string               `json:"parent_project_ref"`
	Persistent       bool                 `json:"persistent"`
	PrNumber         *int32               `json:"pr_number,omitempty"`
	ProjectRef       string               `json:"project_ref"`
	Status           BranchResponseStatus `json:"status"`
	UpdatedAt        string               `json:"updated_at"`
}

// BranchResponseStatus defines model for BranchResponse.Status.
type BranchResponseStatus string

// BranchUpdateResponse defines model for BranchUpdateResponse.
type BranchUpdateResponse struct {
	Message       BranchUpdateResponseMessage `json:"message"`
	WorkflowRunId string                      `json:"workflow_run_id"`
}

// BranchUpdateResponseMessage defines model for BranchUpdateResponse.Message.
type BranchUpdateResponseMessage string

// BulkUpdateFunctionBody defines model for BulkUpdateFunctionBody.
type BulkUpdateFunctionBody = []struct {
	CreatedAt      *int64                       `json:"created_at,omitempty"`
	EntrypointPath *string                      `json:"entrypoint_path,omitempty"`
	Id             string                       `json:"id"`
	ImportMap      *bool                        `json:"import_map,omitempty"`
	ImportMapPath  *string                      `json:"import_map_path,omitempty"`
	Name           string                       `json:"name"`
	Slug           string                       `json:"slug"`
	Status         BulkUpdateFunctionBodyStatus `json:"status"`
	VerifyJwt      *bool                        `json:"verify_jwt,omitempty"`
	Version        int                          `json:"version"`
}

// BulkUpdateFunctionBodyStatus defines model for BulkUpdateFunctionBody.Status.
type BulkUpdateFunctionBodyStatus string

// BulkUpdateFunctionResponse defines model for BulkUpdateFunctionResponse.
type BulkUpdateFunctionResponse struct {
	Functions []struct {
		CreatedAt      int64                                     `json:"created_at"`
		EntrypointPath *string                                   `json:"entrypoint_path,omitempty"`
		Id             string                                    `json:"id"`
		ImportMap      *bool                                     `json:"import_map,omitempty"`
		ImportMapPath  *string                                   `json:"import_map_path,omitempty"`
		Name           string                                    `json:"name"`
		Slug           string                                    `json:"slug"`
		Status         BulkUpdateFunctionResponseFunctionsStatus `json:"status"`
		UpdatedAt      int64                                     `json:"updated_at"`
		VerifyJwt      *bool                                     `json:"verify_jwt,omitempty"`
		Version        int                                       `json:"version"`
	} `json:"functions"`
}

// BulkUpdateFunctionResponseFunctionsStatus defines model for BulkUpdateFunctionResponse.Functions.Status.
type BulkUpdateFunctionResponseFunctionsStatus string

// CreateApiKeyBody defines model for CreateApiKeyBody.
type CreateApiKeyBody struct {
	Description       nullable.Nullable[string] `json:"description,omitempty"`
	Name              string                    `json:"name"`
	SecretJwtTemplate nullable.Nullable[struct {
		Role string `json:"role"`
	}] `json:"secret_jwt_template,omitempty"`
	Type CreateApiKeyBodyType `json:"type"`
}

// CreateApiKeyBodyType defines model for CreateApiKeyBody.Type.
type CreateApiKeyBodyType string

// CreateBranchBody defines model for CreateBranchBody.
type CreateBranchBody struct {
	BranchName          string                               `json:"branch_name"`
	DesiredInstanceSize *CreateBranchBodyDesiredInstanceSize `json:"desired_instance_size,omitempty"`
	GitBranch           *string                              `json:"git_branch,omitempty"`
	Persistent          *bool                                `json:"persistent,omitempty"`

	// PostgresEngine Postgres engine version. If not provided, the latest version will be used.
	PostgresEngine *CreateBranchBodyPostgresEngine `json:"postgres_engine,omitempty"`
	Region         *string                         `json:"region,omitempty"`

	// ReleaseChannel Release channel. If not provided, GA will be used.
	ReleaseChannel *CreateBranchBodyReleaseChannel `json:"release_channel,omitempty"`
	Secrets        *map[string]string              `json:"secrets,omitempty"`
}

// CreateBranchBodyDesiredInstanceSize defines model for CreateBranchBody.DesiredInstanceSize.
type CreateBranchBodyDesiredInstanceSize string

// CreateBranchBodyPostgresEngine Postgres engine version. If not provided, the latest version will be used.
type CreateBranchBodyPostgresEngine string

// CreateBranchBodyReleaseChannel Release channel. If not provided, GA will be used.
type CreateBranchBodyReleaseChannel string

// CreateOrganizationV1 defines model for CreateOrganizationV1.
type CreateOrganizationV1 struct {
	Name string `json:"name"`
}

// CreateProjectClaimTokenResponse defines model for CreateProjectClaimTokenResponse.
type CreateProjectClaimTokenResponse struct {
	CreatedAt  string             `json:"created_at"`
	CreatedBy  openapi_types.UUID `json:"created_by"`
	ExpiresAt  string             `json:"expires_at"`
	Token      string             `json:"token"`
	TokenAlias string             `json:"token_alias"`
}

// CreateProviderBody defines model for CreateProviderBody.
type CreateProviderBody struct {
	AttributeMapping *struct {
		Keys map[string]struct {
			Array   *bool        `json:"array,omitempty"`
			Default *interface{} `json:"default,omitempty"`
			Name    *string      `json:"name,omitempty"`
			Names   *[]string    `json:"names,omitempty"`
		} `json:"keys"`
	} `json:"attribute_mapping,omitempty"`
	Domains     *[]string `json:"domains,omitempty"`
	MetadataUrl *string   `json:"metadata_url,omitempty"`
	MetadataXml *string   `json:"metadata_xml,omitempty"`

	// Type What type of provider will be created
	Type CreateProviderBodyType `json:"type"`
}

// CreateProviderBodyType What type of provider will be created
type CreateProviderBodyType string

// CreateProviderResponse defines model for CreateProviderResponse.
type CreateProviderResponse struct {
	CreatedAt *string `json:"created_at,omitempty"`
	Domains   *[]struct {
		CreatedAt *string `json:"created_at,omitempty"`
		Domain    *string `json:"domain,omitempty"`
		Id        string  `json:"id"`
		UpdatedAt *string `json:"updated_at,omitempty"`
	} `json:"domains,omitempty"`
	Id   string `json:"id"`
	Saml *struct {
		AttributeMapping *struct {
			Keys map[string]struct {
				Array   *bool        `json:"array,omitempty"`
				Default *interface{} `json:"default,omitempty"`
				Name    *string      `json:"name,omitempty"`
				Names   *[]string    `json:"names,omitempty"`
			} `json:"keys"`
		} `json:"attribute_mapping,omitempty"`
		EntityId    string  `json:"entity_id"`
		Id          string  `json:"id"`
		MetadataUrl *string `json:"metadata_url,omitempty"`
		MetadataXml *string `json:"metadata_xml,omitempty"`
	} `json:"saml,omitempty"`
	UpdatedAt *string `json:"updated_at,omitempty"`
}

// CreateSecretBody defines model for CreateSecretBody.
type CreateSecretBody = []struct {
	// Name Secret name must not start with the SUPABASE_ prefix.
	Name  string `json:"name"`
	Value string `json:"value"`
}

// CreateSigningKeyBody defines model for CreateSigningKeyBody.
type CreateSigningKeyBody struct {
	Algorithm CreateSigningKeyBodyAlgorithm `json:"algorithm"`
	Status    *CreateSigningKeyBodyStatus   `json:"status,omitempty"`
}

// CreateSigningKeyBodyAlgorithm defines model for CreateSigningKeyBody.Algorithm.
type CreateSigningKeyBodyAlgorithm string

// CreateSigningKeyBodyStatus defines model for CreateSigningKeyBody.Status.
type CreateSigningKeyBodyStatus string

// CreateThirdPartyAuthBody defines model for CreateThirdPartyAuthBody.
type CreateThirdPartyAuthBody struct {
	CustomJwks    *interface{} `json:"custom_jwks,omitempty"`
	JwksUrl       *string      `json:"jwks_url,omitempty"`
	OidcIssuerUrl *string      `json:"oidc_issuer_url,omitempty"`
}

// DatabaseUpgradeStatusResponse defines model for DatabaseUpgradeStatusResponse.
type DatabaseUpgradeStatusResponse struct {
	DatabaseUpgradeStatus nullable.Nullable[struct {
		Error          *DatabaseUpgradeStatusResponseDatabaseUpgradeStatusError    `json:"error,omitempty"`
		InitiatedAt    string                                                      `json:"initiated_at"`
		LatestStatusAt string                                                      `json:"latest_status_at"`
		Progress       *DatabaseUpgradeStatusResponseDatabaseUpgradeStatusProgress `json:"progress,omitempty"`
		Status         float32                                                     `json:"status"`
		TargetVersion  float32                                                     `json:"target_version"`
	}] `json:"databaseUpgradeStatus"`
}

// DatabaseUpgradeStatusResponseDatabaseUpgradeStatusError defines model for DatabaseUpgradeStatusResponse.DatabaseUpgradeStatus.Error.
type DatabaseUpgradeStatusResponseDatabaseUpgradeStatusError string

// DatabaseUpgradeStatusResponseDatabaseUpgradeStatusProgress defines model for DatabaseUpgradeStatusResponse.DatabaseUpgradeStatus.Progress.
type DatabaseUpgradeStatusResponseDatabaseUpgradeStatusProgress string

// DeleteProviderResponse defines model for DeleteProviderResponse.
type DeleteProviderResponse struct {
	CreatedAt *string `json:"created_at,omitempty"`
	Domains   *[]struct {
		CreatedAt *string `json:"created_at,omitempty"`
		Domain    *string `json:"domain,omitempty"`
		Id        string  `json:"id"`
		UpdatedAt *string `json:"updated_at,omitempty"`
	} `json:"domains,omitempty"`
	Id   string `json:"id"`
	Saml *struct {
		AttributeMapping *struct {
			Keys map[string]struct {
				Array   *bool        `json:"array,omitempty"`
				Default *interface{} `json:"default,omitempty"`
				Name    *string      `json:"name,omitempty"`
				Names   *[]string    `json:"names,omitempty"`
			} `json:"keys"`
		} `json:"attribute_mapping,omitempty"`
		EntityId    string  `json:"entity_id"`
		Id          string  `json:"id"`
		MetadataUrl *string `json:"metadata_url,omitempty"`
		MetadataXml *string `json:"metadata_xml,omitempty"`
	} `json:"saml,omitempty"`
	UpdatedAt *string `json:"updated_at,omitempty"`
}

// DeployFunctionResponse defines model for DeployFunctionResponse.
type DeployFunctionResponse struct {
	CreatedAt      *int64                       `json:"created_at,omitempty"`
	EntrypointPath *string                      `json:"entrypoint_path,omitempty"`
	Id             string                       `json:"id"`
	ImportMap      *bool                        `json:"import_map,omitempty"`
	ImportMapPath  *string                      `json:"import_map_path,omitempty"`
	Name           string                       `json:"name"`
	Slug           string                       `json:"slug"`
	Status         DeployFunctionResponseStatus `json:"status"`
	UpdatedAt      *int64                       `json:"updated_at,omitempty"`
	VerifyJwt      *bool                        `json:"verify_jwt,omitempty"`
	Version        int                          `json:"version"`
}

// DeployFunctionResponseStatus defines model for DeployFunctionResponse.Status.
type DeployFunctionResponseStatus string

// FunctionDeployBody defines model for FunctionDeployBody.
type FunctionDeployBody struct {
	File     *[]openapi_types.File `json:"file,omitempty"`
	Metadata struct {
		EntrypointPath string    `json:"entrypoint_path"`
		ImportMapPath  *string   `json:"import_map_path,omitempty"`
		Name           *string   `json:"name,omitempty"`
		StaticPatterns *[]string `json:"static_patterns,omitempty"`
		VerifyJwt      *bool     `json:"verify_jwt,omitempty"`
	} `json:"metadata"`
}

// FunctionResponse defines model for FunctionResponse.
type FunctionResponse struct {
	CreatedAt      int64                  `json:"created_at"`
	EntrypointPath *string                `json:"entrypoint_path,omitempty"`
	Id             string                 `json:"id"`
	ImportMap      *bool                  `json:"import_map,omitempty"`
	ImportMapPath  *string                `json:"import_map_path,omitempty"`
	Name           string                 `json:"name"`
	Slug           string                 `json:"slug"`
	Status         FunctionResponseStatus `json:"status"`
	UpdatedAt      int64                  `json:"updated_at"`
	VerifyJwt      *bool                  `json:"verify_jwt,omitempty"`
	Version        int                    `json:"version"`
}

// FunctionResponseStatus defines model for FunctionResponse.Status.
type FunctionResponseStatus string

// FunctionSlugResponse defines model for FunctionSlugResponse.
type FunctionSlugResponse struct {
	CreatedAt      int64                      `json:"created_at"`
	EntrypointPath *string                    `json:"entrypoint_path,omitempty"`
	Id             string                     `json:"id"`
	ImportMap      *bool                      `json:"import_map,omitempty"`
	ImportMapPath  *string                    `json:"import_map_path,omitempty"`
	Name           string                     `json:"name"`
	Slug           string                     `json:"slug"`
	Status         FunctionSlugResponseStatus `json:"status"`
	UpdatedAt      int64                      `json:"updated_at"`
	VerifyJwt      *bool                      `json:"verify_jwt,omitempty"`
	Version        int                        `json:"version"`
}

// FunctionSlugResponseStatus defines model for FunctionSlugResponse.Status.
type FunctionSlugResponseStatus string

// GetProjectAvailableRestoreVersionsResponse defines model for GetProjectAvailableRestoreVersionsResponse.
type GetProjectAvailableRestoreVersionsResponse struct {
	AvailableVersions []struct {
		PostgresEngine GetProjectAvailableRestoreVersionsResponseAvailableVersionsPostgresEngine `json:"postgres_engine"`
		ReleaseChannel GetProjectAvailableRestoreVersionsResponseAvailableVersionsReleaseChannel `json:"release_channel"`
		Version        string                                                                    `json:"version"`
	} `json:"available_versions"`
}

// GetProjectAvailableRestoreVersionsResponseAvailableVersionsPostgresEngine defines model for GetProjectAvailableRestoreVersionsResponse.AvailableVersions.PostgresEngine.
type GetProjectAvailableRestoreVersionsResponseAvailableVersionsPostgresEngine string

// GetProjectAvailableRestoreVersionsResponseAvailableVersionsReleaseChannel defines model for GetProjectAvailableRestoreVersionsResponse.AvailableVersions.ReleaseChannel.
type GetProjectAvailableRestoreVersionsResponseAvailableVersionsReleaseChannel string

// GetProjectDbMetadataResponse defines model for GetProjectDbMetadataResponse.
type GetProjectDbMetadataResponse struct {
	Databases []GetProjectDbMetadataResponse_Databases_Item `json:"databases"`
}

// GetProjectDbMetadataResponse_Databases_Schemas_Item defines model for GetProjectDbMetadataResponse.Databases.Schemas.Item.
type GetProjectDbMetadataResponse_Databases_Schemas_Item struct {
	Name                 string                 `json:"name"`
	AdditionalProperties map[string]interface{} `json:"-"`
}

// GetProjectDbMetadataResponse_Databases_Item defines model for GetProjectDbMetadataResponse.databases.Item.
type GetProjectDbMetadataResponse_Databases_Item struct {
	Name                 string                                                `json:"name"`
	Schemas              []GetProjectDbMetadataResponse_Databases_Schemas_Item `json:"schemas"`
	AdditionalProperties map[string]interface{}                                `json:"-"`
}

// GetProviderResponse defines model for GetProviderResponse.
type GetProviderResponse struct {
	CreatedAt *string `json:"created_at,omitempty"`
	Domains   *[]struct {
		CreatedAt *string `json:"created_at,omitempty"`
		Domain    *string `json:"domain,omitempty"`
		Id        string  `json:"id"`
		UpdatedAt *string `json:"updated_at,omitempty"`
	} `json:"domains,omitempty"`
	Id   string `json:"id"`
	Saml *struct {
		AttributeMapping *struct {
			Keys map[string]struct {
				Array   *bool        `json:"array,omitempty"`
				Default *interface{} `json:"default,omitempty"`
				Name    *string      `json:"name,omitempty"`
				Names   *[]string    `json:"names,omitempty"`
			} `json:"keys"`
		} `json:"attribute_mapping,omitempty"`
		EntityId    string  `json:"entity_id"`
		Id          string  `json:"id"`
		MetadataUrl *string `json:"metadata_url,omitempty"`
		MetadataXml *string `json:"metadata_xml,omitempty"`
	} `json:"saml,omitempty"`
	UpdatedAt *string `json:"updated_at,omitempty"`
}

// ListProjectAddonsResponse defines model for ListProjectAddonsResponse.
type ListProjectAddonsResponse struct {
	AvailableAddons []struct {
		Name     string                                       `json:"name"`
		Type     ListProjectAddonsResponseAvailableAddonsType `json:"type"`
		Variants []struct {
			Id ListProjectAddonsResponse_AvailableAddons_Variants_Id `json:"id"`

			// Meta Any JSON-serializable value
			Meta  *interface{} `json:"meta,omitempty"`
			Name  string       `json:"name"`
			Price struct {
				Amount      float32                                                       `json:"amount"`
				Description string                                                        `json:"description"`
				Interval    ListProjectAddonsResponseAvailableAddonsVariantsPriceInterval `json:"interval"`
				Type        ListProjectAddonsResponseAvailableAddonsVariantsPriceType     `json:"type"`
			} `json:"price"`
		} `json:"variants"`
	} `json:"available_addons"`
	SelectedAddons []struct {
		Type    ListProjectAddonsResponseSelectedAddonsType `json:"type"`
		Variant struct {
			Id ListProjectAddonsResponse_SelectedAddons_Variant_Id `json:"id"`

			// Meta Any JSON-serializable value
			Meta  *interface{} `json:"meta,omitempty"`
			Name  string       `json:"name"`
			Price struct {
				Amount      float32                                                     `json:"amount"`
				Description string                                                      `json:"description"`
				Interval    ListProjectAddonsResponseSelectedAddonsVariantPriceInterval `json:"interval"`
				Type        ListProjectAddonsResponseSelectedAddonsVariantPriceType     `json:"type"`
			} `json:"price"`
		} `json:"variant"`
	} `json:"selected_addons"`
}

// ListProjectAddonsResponseAvailableAddonsType defines model for ListProjectAddonsResponse.AvailableAddons.Type.
type ListProjectAddonsResponseAvailableAddonsType string

// ListProjectAddonsResponseAvailableAddonsVariantsId0 defines model for ListProjectAddonsResponse.AvailableAddons.Variants.Id.0.
type ListProjectAddonsResponseAvailableAddonsVariantsId0 string

// ListProjectAddonsResponseAvailableAddonsVariantsId1 defines model for ListProjectAddonsResponse.AvailableAddons.Variants.Id.1.
type ListProjectAddonsResponseAvailableAddonsVariantsId1 string

// ListProjectAddonsResponseAvailableAddonsVariantsId2 defines model for ListProjectAddonsResponse.AvailableAddons.Variants.Id.2.
type ListProjectAddonsResponseAvailableAddonsVariantsId2 string

// ListProjectAddonsResponseAvailableAddonsVariantsId3 defines model for ListProjectAddonsResponse.AvailableAddons.Variants.Id.3.
type ListProjectAddonsResponseAvailableAddonsVariantsId3 string

// ListProjectAddonsResponseAvailableAddonsVariantsId4 defines model for ListProjectAddonsResponse.AvailableAddons.Variants.Id.4.
type ListProjectAddonsResponseAvailableAddonsVariantsId4 string

// ListProjectAddonsResponseAvailableAddonsVariantsId5 defines model for ListProjectAddonsResponse.AvailableAddons.Variants.Id.5.
type ListProjectAddonsResponseAvailableAddonsVariantsId5 string

// ListProjectAddonsResponseAvailableAddonsVariantsId6 defines model for ListProjectAddonsResponse.AvailableAddons.Variants.Id.6.
type ListProjectAddonsResponseAvailableAddonsVariantsId6 string

// ListProjectAddonsResponse_AvailableAddons_Variants_Id defines model for ListProjectAddonsResponse.AvailableAddons.Variants.Id.
type ListProjectAddonsResponse_AvailableAddons_Variants_Id struct {
	union json.RawMessage
}

// ListProjectAddonsResponseAvailableAddonsVariantsPriceInterval defines model for ListProjectAddonsResponse.AvailableAddons.Variants.Price.Interval.
type ListProjectAddonsResponseAvailableAddonsVariantsPriceInterval string

// ListProjectAddonsResponseAvailableAddonsVariantsPriceType defines model for ListProjectAddonsResponse.AvailableAddons.Variants.Price.Type.
type ListProjectAddonsResponseAvailableAddonsVariantsPriceType string

// ListProjectAddonsResponseSelectedAddonsType defines model for ListProjectAddonsResponse.SelectedAddons.Type.
type ListProjectAddonsResponseSelectedAddonsType string

// ListProjectAddonsResponseSelectedAddonsVariantId0 defines model for ListProjectAddonsResponse.SelectedAddons.Variant.Id.0.
type ListProjectAddonsResponseSelectedAddonsVariantId0 string

// ListProjectAddonsResponseSelectedAddonsVariantId1 defines model for ListProjectAddonsResponse.SelectedAddons.Variant.Id.1.
type ListProjectAddonsResponseSelectedAddonsVariantId1 string

// ListProjectAddonsResponseSelectedAddonsVariantId2 defines model for ListProjectAddonsResponse.SelectedAddons.Variant.Id.2.
type ListProjectAddonsResponseSelectedAddonsVariantId2 string

// ListProjectAddonsResponseSelectedAddonsVariantId3 defines model for ListProjectAddonsResponse.SelectedAddons.Variant.Id.3.
type ListProjectAddonsResponseSelectedAddonsVariantId3 string

// ListProjectAddonsResponseSelectedAddonsVariantId4 defines model for ListProjectAddonsResponse.SelectedAddons.Variant.Id.4.
type ListProjectAddonsResponseSelectedAddonsVariantId4 string

// ListProjectAddonsResponseSelectedAddonsVariantId5 defines model for ListProjectAddonsResponse.SelectedAddons.Variant.Id.5.
type ListProjectAddonsResponseSelectedAddonsVariantId5 string

// ListProjectAddonsResponseSelectedAddonsVariantId6 defines model for ListProjectAddonsResponse.SelectedAddons.Variant.Id.6.
type ListProjectAddonsResponseSelectedAddonsVariantId6 string

// ListProjectAddonsResponse_SelectedAddons_Variant_Id defines model for ListProjectAddonsResponse.SelectedAddons.Variant.Id.
type ListProjectAddonsResponse_SelectedAddons_Variant_Id struct {
	union json.RawMessage
}

// ListProjectAddonsResponseSelectedAddonsVariantPriceInterval defines model for ListProjectAddonsResponse.SelectedAddons.Variant.Price.Interval.
type ListProjectAddonsResponseSelectedAddonsVariantPriceInterval string

// ListProjectAddonsResponseSelectedAddonsVariantPriceType defines model for ListProjectAddonsResponse.SelectedAddons.Variant.Price.Type.
type ListProjectAddonsResponseSelectedAddonsVariantPriceType string

// ListProvidersResponse defines model for ListProvidersResponse.
type ListProvidersResponse struct {
	Items []struct {
		CreatedAt *string `json:"created_at,omitempty"`
		Domains   *[]struct {
			CreatedAt *string `json:"created_at,omitempty"`
			Domain    *string `json:"domain,omitempty"`
			Id        string  `json:"id"`
			UpdatedAt *string `json:"updated_at,omitempty"`
		} `json:"domains,omitempty"`
		Id   string `json:"id"`
		Saml *struct {
			AttributeMapping *struct {
				Keys map[string]struct {
					Array   *bool        `json:"array,omitempty"`
					Default *interface{} `json:"default,omitempty"`
					Name    *string      `json:"name,omitempty"`
					Names   *[]string    `json:"names,omitempty"`
				} `json:"keys"`
			} `json:"attribute_mapping,omitempty"`
			EntityId    string  `json:"entity_id"`
			Id          string  `json:"id"`
			MetadataUrl *string `json:"metadata_url,omitempty"`
			MetadataXml *string `json:"metadata_xml,omitempty"`
		} `json:"saml,omitempty"`
		UpdatedAt *string `json:"updated_at,omitempty"`
	} `json:"items"`
}

// NetworkBanResponse defines model for NetworkBanResponse.
type NetworkBanResponse struct {
	BannedIpv4Addresses []string `json:"banned_ipv4_addresses"`
}

// NetworkBanResponseEnriched defines model for NetworkBanResponseEnriched.
type NetworkBanResponseEnriched struct {
	BannedIpv4Addresses []struct {
		BannedAddress string `json:"banned_address"`
		Identifier    string `json:"identifier"`
		Type          string `json:"type"`
	} `json:"banned_ipv4_addresses"`
}

// NetworkRestrictionsRequest defines model for NetworkRestrictionsRequest.
type NetworkRestrictionsRequest struct {
	DbAllowedCidrs   *[]string `json:"dbAllowedCidrs,omitempty"`
	DbAllowedCidrsV6 *[]string `json:"dbAllowedCidrsV6,omitempty"`
}

// NetworkRestrictionsResponse defines model for NetworkRestrictionsResponse.
type NetworkRestrictionsResponse struct {
	// Config At any given point in time, this is the config that the user has requested be applied to their project. The `status` field indicates if it has been applied to the project, or is pending. When an updated config is received, the applied config is moved to `old_config`.
	Config struct {
		DbAllowedCidrs   *[]string `json:"dbAllowedCidrs,omitempty"`
		DbAllowedCidrsV6 *[]string `json:"dbAllowedCidrsV6,omitempty"`
	} `json:"config"`
	Entitlement NetworkRestrictionsResponseEntitlement `json:"entitlement"`

	// OldConfig Populated when a new config has been received, but not registered as successfully applied to a project.
	OldConfig *struct {
		DbAllowedCidrs   *[]string `json:"dbAllowedCidrs,omitempty"`
		DbAllowedCidrsV6 *[]string `json:"dbAllowedCidrsV6,omitempty"`
	} `json:"old_config,omitempty"`
	Status NetworkRestrictionsResponseStatus `json:"status"`
}

// NetworkRestrictionsResponseEntitlement defines model for NetworkRestrictionsResponse.Entitlement.
type NetworkRestrictionsResponseEntitlement string

// NetworkRestrictionsResponseStatus defines model for NetworkRestrictionsResponse.Status.
type NetworkRestrictionsResponseStatus string

// OAuthRevokeTokenBody defines model for OAuthRevokeTokenBody.
type OAuthRevokeTokenBody struct {
	ClientId     openapi_types.UUID `json:"client_id"`
	ClientSecret string             `json:"client_secret"`
	RefreshToken string             `json:"refresh_token"`
}

// OAuthTokenBody defines model for OAuthTokenBody.
type OAuthTokenBody struct {
	ClientId     *openapi_types.UUID      `json:"client_id,omitempty"`
	ClientSecret *string                  `json:"client_secret,omitempty"`
	Code         *string                  `json:"code,omitempty"`
	CodeVerifier *string                  `json:"code_verifier,omitempty"`
	GrantType    *OAuthTokenBodyGrantType `json:"grant_type,omitempty"`
	RedirectUri  *string                  `json:"redirect_uri,omitempty"`
	RefreshToken *string                  `json:"refresh_token,omitempty"`
}

// OAuthTokenBodyGrantType defines model for OAuthTokenBody.GrantType.
type OAuthTokenBodyGrantType string

// OAuthTokenResponse defines model for OAuthTokenResponse.
type OAuthTokenResponse struct {
	AccessToken  string                      `json:"access_token"`
	ExpiresIn    int                         `json:"expires_in"`
	RefreshToken string                      `json:"refresh_token"`
	TokenType    OAuthTokenResponseTokenType `json:"token_type"`
}

// OAuthTokenResponseTokenType defines model for OAuthTokenResponse.TokenType.
type OAuthTokenResponseTokenType string

// OrganizationProjectClaimResponse defines model for OrganizationProjectClaimResponse.
type OrganizationProjectClaimResponse struct {
	CreatedAt string             `json:"created_at"`
	CreatedBy openapi_types.UUID `json:"created_by"`
	ExpiresAt string             `json:"expires_at"`
	Preview   struct {
		Errors []struct {
			Key     string `json:"key"`
			Message string `json:"message"`
		} `json:"errors"`
		Info []struct {
			Key     string `json:"key"`
			Message string `json:"message"`
		} `json:"info"`
		MembersExceedingFreeProjectLimit []struct {
			Limit float32 `json:"limit"`
			Name  string  `json:"name"`
		} `json:"members_exceeding_free_project_limit"`
		SourceSubscriptionPlan                OrganizationProjectClaimResponsePreviewSourceSubscriptionPlan                    `json:"source_subscription_plan"`
		TargetOrganizationEligible            nullable.Nullable[bool]                                                          `json:"target_organization_eligible"`
		TargetOrganizationHasFreeProjectSlots nullable.Nullable[bool]                                                          `json:"target_organization_has_free_project_slots"`
		TargetSubscriptionPlan                nullable.Nullable[OrganizationProjectClaimResponsePreviewTargetSubscriptionPlan] `json:"target_subscription_plan"`
		Valid                                 bool                                                                             `json:"valid"`
		Warnings                              []struct {
			Key     string `json:"key"`
			Message string `json:"message"`
		} `json:"warnings"`
	} `json:"preview"`
	Project struct {
		Name string `json:"name"`
		Ref  string `json:"ref"`
	} `json:"project"`
}

// OrganizationProjectClaimResponsePreviewSourceSubscriptionPlan defines model for OrganizationProjectClaimResponse.Preview.SourceSubscriptionPlan.
type OrganizationProjectClaimResponsePreviewSourceSubscriptionPlan string

// OrganizationProjectClaimResponsePreviewTargetSubscriptionPlan defines model for OrganizationProjectClaimResponse.Preview.TargetSubscriptionPlan.
type OrganizationProjectClaimResponsePreviewTargetSubscriptionPlan string

// OrganizationResponseV1 defines model for OrganizationResponseV1.
type OrganizationResponseV1 struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

// PgsodiumConfigResponse defines model for PgsodiumConfigResponse.
type PgsodiumConfigResponse struct {
	RootKey string `json:"root_key"`
}

// PostgresConfigResponse defines model for PostgresConfigResponse.
type PostgresConfigResponse struct {
	EffectiveCacheSize            *string                                       `json:"effective_cache_size,omitempty"`
	LogicalDecodingWorkMem        *string                                       `json:"logical_decoding_work_mem,omitempty"`
	MaintenanceWorkMem            *string                                       `json:"maintenance_work_mem,omitempty"`
	MaxConnections                *int                                          `json:"max_connections,omitempty"`
	MaxLocksPerTransaction        *int                                          `json:"max_locks_per_transaction,omitempty"`
	MaxParallelMaintenanceWorkers *int                                          `json:"max_parallel_maintenance_workers,omitempty"`
	MaxParallelWorkers            *int                                          `json:"max_parallel_workers,omitempty"`
	MaxParallelWorkersPerGather   *int                                          `json:"max_parallel_workers_per_gather,omitempty"`
	MaxReplicationSlots           *int                                          `json:"max_replication_slots,omitempty"`
	MaxSlotWalKeepSize            *string                                       `json:"max_slot_wal_keep_size,omitempty"`
	MaxStandbyArchiveDelay        *string                                       `json:"max_standby_archive_delay,omitempty"`
	MaxStandbyStreamingDelay      *string                                       `json:"max_standby_streaming_delay,omitempty"`
	MaxWalSenders                 *int                                          `json:"max_wal_senders,omitempty"`
	MaxWalSize                    *string                                       `json:"max_wal_size,omitempty"`
	MaxWorkerProcesses            *int                                          `json:"max_worker_processes,omitempty"`
	SessionReplicationRole        *PostgresConfigResponseSessionReplicationRole `json:"session_replication_role,omitempty"`
	SharedBuffers                 *string                                       `json:"shared_buffers,omitempty"`
	StatementTimeout              *string                                       `json:"statement_timeout,omitempty"`
	TrackActivityQuerySize        *string                                       `json:"track_activity_query_size,omitempty"`
	TrackCommitTimestamp          *bool                                         `json:"track_commit_timestamp,omitempty"`
	WalKeepSize                   *string                                       `json:"wal_keep_size,omitempty"`
	WalSenderTimeout              *string                                       `json:"wal_sender_timeout,omitempty"`
	WorkMem                       *string                                       `json:"work_mem,omitempty"`
}

// PostgresConfigResponseSessionReplicationRole defines model for PostgresConfigResponse.SessionReplicationRole.
type PostgresConfigResponseSessionReplicationRole string

// PostgrestConfigWithJWTSecretResponse defines model for PostgrestConfigWithJWTSecretResponse.
type PostgrestConfigWithJWTSecretResponse struct {
	DbExtraSearchPath string `json:"db_extra_search_path"`

	// DbPool If `null`, the value is automatically configured based on compute size.
	DbPool    nullable.Nullable[int] `json:"db_pool"`
	DbSchema  string                 `json:"db_schema"`
	JwtSecret *string                `json:"jwt_secret,omitempty"`
	MaxRows   int                    `json:"max_rows"`
}

// ProjectClaimTokenResponse defines model for ProjectClaimTokenResponse.
type ProjectClaimTokenResponse struct {
	CreatedAt  string             `json:"created_at"`
	CreatedBy  openapi_types.UUID `json:"created_by"`
	ExpiresAt  string             `json:"expires_at"`
	TokenAlias string             `json:"token_alias"`
}

// ProjectUpgradeEligibilityResponse defines model for ProjectUpgradeEligibilityResponse.
type ProjectUpgradeEligibilityResponse struct {
	CurrentAppVersion               string                                                           `json:"current_app_version"`
	CurrentAppVersionReleaseChannel ProjectUpgradeEligibilityResponseCurrentAppVersionReleaseChannel `json:"current_app_version_release_channel"`
	DurationEstimateHours           float32                                                          `json:"duration_estimate_hours"`
	Eligible                        bool                                                             `json:"eligible"`
	ExtensionDependentObjects       []string                                                         `json:"extension_dependent_objects"`
	LatestAppVersion                string                                                           `json:"latest_app_version"`
	LegacyAuthCustomRoles           []string                                                         `json:"legacy_auth_custom_roles"`
	PotentialBreakingChanges        []string                                                         `json:"potential_breaking_changes"`
	TargetUpgradeVersions           []struct {
		AppVersion      string                                                                `json:"app_version"`
		PostgresVersion ProjectUpgradeEligibilityResponseTargetUpgradeVersionsPostgresVersion `json:"postgres_version"`
		ReleaseChannel  ProjectUpgradeEligibilityResponseTargetUpgradeVersionsReleaseChannel  `json:"release_channel"`
	} `json:"target_upgrade_versions"`
}

// ProjectUpgradeEligibilityResponseCurrentAppVersionReleaseChannel defines model for ProjectUpgradeEligibilityResponse.CurrentAppVersionReleaseChannel.
type ProjectUpgradeEligibilityResponseCurrentAppVersionReleaseChannel string

// ProjectUpgradeEligibilityResponseTargetUpgradeVersionsPostgresVersion defines model for ProjectUpgradeEligibilityResponse.TargetUpgradeVersions.PostgresVersion.
type ProjectUpgradeEligibilityResponseTargetUpgradeVersionsPostgresVersion string

// ProjectUpgradeEligibilityResponseTargetUpgradeVersionsReleaseChannel defines model for ProjectUpgradeEligibilityResponse.TargetUpgradeVersions.ReleaseChannel.
type ProjectUpgradeEligibilityResponseTargetUpgradeVersionsReleaseChannel string

// ProjectUpgradeInitiateResponse defines model for ProjectUpgradeInitiateResponse.
type ProjectUpgradeInitiateResponse struct {
	TrackingId string `json:"tracking_id"`
}

// ReadOnlyStatusResponse defines model for ReadOnlyStatusResponse.
type ReadOnlyStatusResponse struct {
	Enabled             bool   `json:"enabled"`
	OverrideActiveUntil string `json:"override_active_until"`
	OverrideEnabled     bool   `json:"override_enabled"`
}

// RemoveNetworkBanRequest defines model for RemoveNetworkBanRequest.
type RemoveNetworkBanRequest struct {
	Identifier    *string  `json:"identifier,omitempty"`
	Ipv4Addresses []string `json:"ipv4_addresses"`
}

// RemoveReadReplicaBody defines model for RemoveReadReplicaBody.
type RemoveReadReplicaBody struct {
	DatabaseIdentifier string `json:"database_identifier"`
}

// SecretResponse defines model for SecretResponse.
type SecretResponse struct {
	Name      string  `json:"name"`
	UpdatedAt *string `json:"updated_at,omitempty"`
	Value     string  `json:"value"`
}

// SetUpReadReplicaBody defines model for SetUpReadReplicaBody.
type SetUpReadReplicaBody struct {
	// ReadReplicaRegion Region you want your read replica to reside in
	ReadReplicaRegion SetUpReadReplicaBodyReadReplicaRegion `json:"read_replica_region"`
}

// SetUpReadReplicaBodyReadReplicaRegion Region you want your read replica to reside in
type SetUpReadReplicaBodyReadReplicaRegion string

// SigningKeyResponse defines model for SigningKeyResponse.
type SigningKeyResponse struct {
	Algorithm SigningKeyResponseAlgorithm    `json:"algorithm"`
	CreatedAt time.Time                      `json:"created_at"`
	Id        openapi_types.UUID             `json:"id"`
	PublicJwk nullable.Nullable[interface{}] `json:"public_jwk,omitempty"`
	Status    SigningKeyResponseStatus       `json:"status"`
	UpdatedAt time.Time                      `json:"updated_at"`
}

// SigningKeyResponseAlgorithm defines model for SigningKeyResponse.Algorithm.
type SigningKeyResponseAlgorithm string

// SigningKeyResponseStatus defines model for SigningKeyResponse.Status.
type SigningKeyResponseStatus string

// SigningKeysResponse defines model for SigningKeysResponse.
type SigningKeysResponse struct {
	Keys []struct {
		Algorithm SigningKeysResponseKeysAlgorithm `json:"algorithm"`
		CreatedAt time.Time                        `json:"created_at"`
		Id        openapi_types.UUID               `json:"id"`
		PublicJwk nullable.Nullable[interface{}]   `json:"public_jwk,omitempty"`
		Status    SigningKeysResponseKeysStatus    `json:"status"`
		UpdatedAt time.Time                        `json:"updated_at"`
	} `json:"keys"`
}

// SigningKeysResponseKeysAlgorithm defines model for SigningKeysResponse.Keys.Algorithm.
type SigningKeysResponseKeysAlgorithm string

// SigningKeysResponseKeysStatus defines model for SigningKeysResponse.Keys.Status.
type SigningKeysResponseKeysStatus string

// SnippetList defines model for SnippetList.
type SnippetList struct {
	Cursor *string `json:"cursor,omitempty"`
	Data   []struct {
		Description nullable.Nullable[string] `json:"description"`
		Id          string                    `json:"id"`
		InsertedAt  string                    `json:"inserted_at"`
		Name        string                    `json:"name"`
		Owner       struct {
			Id       float32 `json:"id"`
			Username string  `json:"username"`
		} `json:"owner"`
		Project struct {
			Id   float32 `json:"id"`
			Name string  `json:"name"`
		} `json:"project"`
		Type      SnippetListDataType `json:"type"`
		UpdatedAt string              `json:"updated_at"`
		UpdatedBy struct {
			Id       float32 `json:"id"`
			Username string  `json:"username"`
		} `json:"updated_by"`
		Visibility SnippetListDataVisibility `json:"visibility"`
	} `json:"data"`
}

// SnippetListDataType defines model for SnippetList.Data.Type.
type SnippetListDataType string

// SnippetListDataVisibility defines model for SnippetList.Data.Visibility.
type SnippetListDataVisibility string

// SnippetResponse defines model for SnippetResponse.
type SnippetResponse struct {
	Content struct {
		Favorite      bool   `json:"favorite"`
		SchemaVersion string `json:"schema_version"`
		Sql           string `json:"sql"`
	} `json:"content"`
	Description nullable.Nullable[string] `json:"description"`
	Id          string                    `json:"id"`
	InsertedAt  string                    `json:"inserted_at"`
	Name        string                    `json:"name"`
	Owner       struct {
		Id       float32 `json:"id"`
		Username string  `json:"username"`
	} `json:"owner"`
	Project struct {
		Id   float32 `json:"id"`
		Name string  `json:"name"`
	} `json:"project"`
	Type      SnippetResponseType `json:"type"`
	UpdatedAt string              `json:"updated_at"`
	UpdatedBy struct {
		Id       float32 `json:"id"`
		Username string  `json:"username"`
	} `json:"updated_by"`
	Visibility SnippetResponseVisibility `json:"visibility"`
}

// SnippetResponseType defines model for SnippetResponse.Type.
type SnippetResponseType string

// SnippetResponseVisibility defines model for SnippetResponse.Visibility.
type SnippetResponseVisibility string

// SslEnforcementRequest defines model for SslEnforcementRequest.
type SslEnforcementRequest struct {
	RequestedConfig struct {
		Database bool `json:"database"`
	} `json:"requestedConfig"`
}

// SslEnforcementResponse defines model for SslEnforcementResponse.
type SslEnforcementResponse struct {
	AppliedSuccessfully bool `json:"appliedSuccessfully"`
	CurrentConfig       struct {
		Database bool `json:"database"`
	} `json:"currentConfig"`
}

// StorageConfigResponse defines model for StorageConfigResponse.
type StorageConfigResponse struct {
	Features struct {
		ImageTransformation struct {
			Enabled bool `json:"enabled"`
		} `json:"imageTransformation"`
		S3Protocol struct {
			Enabled bool `json:"enabled"`
		} `json:"s3Protocol"`
	} `json:"features"`
	FileSizeLimit int64 `json:"fileSizeLimit"`
}

// StreamableFile defines model for StreamableFile.
type StreamableFile = map[string]interface{}

// SubdomainAvailabilityResponse defines model for SubdomainAvailabilityResponse.
type SubdomainAvailabilityResponse struct {
	Available bool `json:"available"`
}

// SupavisorConfigResponse defines model for SupavisorConfigResponse.
type SupavisorConfigResponse struct {
	ConnectionString string                              `json:"connection_string"`
	DatabaseType     SupavisorConfigResponseDatabaseType `json:"database_type"`
	DbHost           string                              `json:"db_host"`
	DbName           string                              `json:"db_name"`
	DbPort           int                                 `json:"db_port"`
	DbUser           string                              `json:"db_user"`
	DefaultPoolSize  nullable.Nullable[int]              `json:"default_pool_size"`
	Identifier       string                              `json:"identifier"`
	IsUsingScramAuth bool                                `json:"is_using_scram_auth"`
	MaxClientConn    nullable.Nullable[int]              `json:"max_client_conn"`
	PoolMode         SupavisorConfigResponsePoolMode     `json:"pool_mode"`
}

// SupavisorConfigResponseDatabaseType defines model for SupavisorConfigResponse.DatabaseType.
type SupavisorConfigResponseDatabaseType string

// SupavisorConfigResponsePoolMode defines model for SupavisorConfigResponse.PoolMode.
type SupavisorConfigResponsePoolMode string

// ThirdPartyAuth defines model for ThirdPartyAuth.
type ThirdPartyAuth struct {
	CustomJwks    nullable.Nullable[interface{}] `json:"custom_jwks,omitempty"`
	Id            openapi_types.UUID             `json:"id"`
	InsertedAt    string                         `json:"inserted_at"`
	JwksUrl       nullable.Nullable[string]      `json:"jwks_url,omitempty"`
	OidcIssuerUrl nullable.Nullable[string]      `json:"oidc_issuer_url,omitempty"`
	ResolvedAt    nullable.Nullable[string]      `json:"resolved_at,omitempty"`
	ResolvedJwks  nullable.Nullable[interface{}] `json:"resolved_jwks,omitempty"`
	Type          string                         `json:"type"`
	UpdatedAt     string                         `json:"updated_at"`
}

// TypescriptResponse defines model for TypescriptResponse.
type TypescriptResponse struct {
	Types string `json:"types"`
}

// UpdateApiKeyBody defines model for UpdateApiKeyBody.
type UpdateApiKeyBody struct {
	Description       nullable.Nullable[string] `json:"description,omitempty"`
	Name              *string                   `json:"name,omitempty"`
	SecretJwtTemplate nullable.Nullable[struct {
		Role string `json:"role"`
	}] `json:"secret_jwt_template,omitempty"`
}

// UpdateAuthConfigBody defines model for UpdateAuthConfigBody.
type UpdateAuthConfigBody struct {
	ApiMaxRequestDuration                         nullable.Nullable[int]                                            `json:"api_max_request_duration,omitempty"`
	DbMaxPoolSize                                 nullable.Nullable[int]                                            `json:"db_max_pool_size,omitempty"`
	DisableSignup                                 nullable.Nullable[bool]                                           `json:"disable_signup,omitempty"`
	ExternalAnonymousUsersEnabled                 nullable.Nullable[bool]                                           `json:"external_anonymous_users_enabled,omitempty"`
	ExternalAppleAdditionalClientIds              nullable.Nullable[string]                                         `json:"external_apple_additional_client_ids,omitempty"`
	ExternalAppleClientId                         nullable.Nullable[string]                                         `json:"external_apple_client_id,omitempty"`
	ExternalAppleEnabled                          nullable.Nullable[bool]                                           `json:"external_apple_enabled,omitempty"`
	ExternalAppleSecret                           nullable.Nullable[string]                                         `json:"external_apple_secret,omitempty"`
	ExternalAzureClientId                         nullable.Nullable[string]                                         `json:"external_azure_client_id,omitempty"`
	ExternalAzureEnabled                          nullable.Nullable[bool]                                           `json:"external_azure_enabled,omitempty"`
	ExternalAzureSecret                           nullable.Nullable[string]                                         `json:"external_azure_secret,omitempty"`
	ExternalAzureUrl                              nullable.Nullable[string]                                         `json:"external_azure_url,omitempty"`
	ExternalBitbucketClientId                     nullable.Nullable[string]                                         `json:"external_bitbucket_client_id,omitempty"`
	ExternalBitbucketEnabled                      nullable.Nullable[bool]                                           `json:"external_bitbucket_enabled,omitempty"`
	ExternalBitbucketSecret                       nullable.Nullable[string]                                         `json:"external_bitbucket_secret,omitempty"`
	ExternalDiscordClientId                       nullable.Nullable[string]                                         `json:"external_discord_client_id,omitempty"`
	ExternalDiscordEnabled                        nullable.Nullable[bool]                                           `json:"external_discord_enabled,omitempty"`
	ExternalDiscordSecret                         nullable.Nullable[string]                                         `json:"external_discord_secret,omitempty"`
	ExternalEmailEnabled                          nullable.Nullable[bool]                                           `json:"external_email_enabled,omitempty"`
	ExternalFacebookClientId                      nullable.Nullable[string]                                         `json:"external_facebook_client_id,omitempty"`
	ExternalFacebookEnabled                       nullable.Nullable[bool]                                           `json:"external_facebook_enabled,omitempty"`
	ExternalFacebookSecret                        nullable.Nullable[string]                                         `json:"external_facebook_secret,omitempty"`
	ExternalFigmaClientId                         nullable.Nullable[string]                                         `json:"external_figma_client_id,omitempty"`
	ExternalFigmaEnabled                          nullable.Nullable[bool]                                           `json:"external_figma_enabled,omitempty"`
	ExternalFigmaSecret                           nullable.Nullable[string]                                         `json:"external_figma_secret,omitempty"`
	ExternalGithubClientId                        nullable.Nullable[string]                                         `json:"external_github_client_id,omitempty"`
	ExternalGithubEnabled                         nullable.Nullable[bool]                                           `json:"external_github_enabled,omitempty"`
	ExternalGithubSecret                          nullable.Nullable[string]                                         `json:"external_github_secret,omitempty"`
	ExternalGitlabClientId                        nullable.Nullable[string]                                         `json:"external_gitlab_client_id,omitempty"`
	ExternalGitlabEnabled                         nullable.Nullable[bool]                                           `json:"external_gitlab_enabled,omitempty"`
	ExternalGitlabSecret                          nullable.Nullable[string]                                         `json:"external_gitlab_secret,omitempty"`
	ExternalGitlabUrl                             nullable.Nullable[string]                                         `json:"external_gitlab_url,omitempty"`
	ExternalGoogleAdditionalClientIds             nullable.Nullable[string]                                         `json:"external_google_additional_client_ids,omitempty"`
	ExternalGoogleClientId                        nullable.Nullable[string]                                         `json:"external_google_client_id,omitempty"`
	ExternalGoogleEnabled                         nullable.Nullable[bool]                                           `json:"external_google_enabled,omitempty"`
	ExternalGoogleSecret                          nullable.Nullable[string]                                         `json:"external_google_secret,omitempty"`
	ExternalGoogleSkipNonceCheck                  nullable.Nullable[bool]                                           `json:"external_google_skip_nonce_check,omitempty"`
	ExternalKakaoClientId                         nullable.Nullable[string]                                         `json:"external_kakao_client_id,omitempty"`
	ExternalKakaoEnabled                          nullable.Nullable[bool]                                           `json:"external_kakao_enabled,omitempty"`
	ExternalKakaoSecret                           nullable.Nullable[string]                                         `json:"external_kakao_secret,omitempty"`
	ExternalKeycloakClientId                      nullable.Nullable[string]                                         `json:"external_keycloak_client_id,omitempty"`
	ExternalKeycloakEnabled                       nullable.Nullable[bool]                                           `json:"external_keycloak_enabled,omitempty"`
	ExternalKeycloakSecret                        nullable.Nullable[string]                                         `json:"external_keycloak_secret,omitempty"`
	ExternalKeycloakUrl                           nullable.Nullable[string]                                         `json:"external_keycloak_url,omitempty"`
	ExternalLinkedinOidcClientId                  nullable.Nullable[string]                                         `json:"external_linkedin_oidc_client_id,omitempty"`
	ExternalLinkedinOidcEnabled                   nullable.Nullable[bool]                                           `json:"external_linkedin_oidc_enabled,omitempty"`
	ExternalLinkedinOidcSecret                    nullable.Nullable[string]                                         `json:"external_linkedin_oidc_secret,omitempty"`
	ExternalNotionClientId                        nullable.Nullable[string]                                         `json:"external_notion_client_id,omitempty"`
	ExternalNotionEnabled                         nullable.Nullable[bool]                                           `json:"external_notion_enabled,omitempty"`
	ExternalNotionSecret                          nullable.Nullable[string]                                         `json:"external_notion_secret,omitempty"`
	ExternalPhoneEnabled                          nullable.Nullable[bool]                                           `json:"external_phone_enabled,omitempty"`
	ExternalSlackClientId                         nullable.Nullable[string]                                         `json:"external_slack_client_id,omitempty"`
	ExternalSlackEnabled                          nullable.Nullable[bool]                                           `json:"external_slack_enabled,omitempty"`
	ExternalSlackOidcClientId                     nullable.Nullable[string]                                         `json:"external_slack_oidc_client_id,omitempty"`
	ExternalSlackOidcEnabled                      nullable.Nullable[bool]                                           `json:"external_slack_oidc_enabled,omitempty"`
	ExternalSlackOidcSecret                       nullable.Nullable[string]                                         `json:"external_slack_oidc_secret,omitempty"`
	ExternalSlackSecret                           nullable.Nullable[string]                                         `json:"external_slack_secret,omitempty"`
	ExternalSpotifyClientId                       nullable.Nullable[string]                                         `json:"external_spotify_client_id,omitempty"`
	ExternalSpotifyEnabled                        nullable.Nullable[bool]                                           `json:"external_spotify_enabled,omitempty"`
	ExternalSpotifySecret                         nullable.Nullable[string]                                         `json:"external_spotify_secret,omitempty"`
	ExternalTwitchClientId                        nullable.Nullable[string]                                         `json:"external_twitch_client_id,omitempty"`
	ExternalTwitchEnabled                         nullable.Nullable[bool]                                           `json:"external_twitch_enabled,omitempty"`
	ExternalTwitchSecret                          nullable.Nullable[string]                                         `json:"external_twitch_secret,omitempty"`
	ExternalTwitterClientId                       nullable.Nullable[string]                                         `json:"external_twitter_client_id,omitempty"`
	ExternalTwitterEnabled                        nullable.Nullable[bool]                                           `json:"external_twitter_enabled,omitempty"`
	ExternalTwitterSecret                         nullable.Nullable[string]                                         `json:"external_twitter_secret,omitempty"`
	ExternalWeb3SolanaEnabled                     nullable.Nullable[bool]                                           `json:"external_web3_solana_enabled,omitempty"`
	ExternalWorkosClientId                        nullable.Nullable[string]                                         `json:"external_workos_client_id,omitempty"`
	ExternalWorkosEnabled                         nullable.Nullable[bool]                                           `json:"external_workos_enabled,omitempty"`
	ExternalWorkosSecret                          nullable.Nullable[string]                                         `json:"external_workos_secret,omitempty"`
	ExternalWorkosUrl                             nullable.Nullable[string]                                         `json:"external_workos_url,omitempty"`
	ExternalZoomClientId                          nullable.Nullable[string]                                         `json:"external_zoom_client_id,omitempty"`
	ExternalZoomEnabled                           nullable.Nullable[bool]                                           `json:"external_zoom_enabled,omitempty"`
	ExternalZoomSecret                            nullable.Nullable[string]                                         `json:"external_zoom_secret,omitempty"`
	HookCustomAccessTokenEnabled                  nullable.Nullable[bool]                                           `json:"hook_custom_access_token_enabled,omitempty"`
	HookCustomAccessTokenSecrets                  nullable.Nullable[string]                                         `json:"hook_custom_access_token_secrets,omitempty"`
	HookCustomAccessTokenUri                      nullable.Nullable[string]                                         `json:"hook_custom_access_token_uri,omitempty"`
	HookMfaVerificationAttemptEnabled             nullable.Nullable[bool]                                           `json:"hook_mfa_verification_attempt_enabled,omitempty"`
	HookMfaVerificationAttemptSecrets             nullable.Nullable[string]                                         `json:"hook_mfa_verification_attempt_secrets,omitempty"`
	HookMfaVerificationAttemptUri                 nullable.Nullable[string]                                         `json:"hook_mfa_verification_attempt_uri,omitempty"`
	HookPasswordVerificationAttemptEnabled        nullable.Nullable[bool]                                           `json:"hook_password_verification_attempt_enabled,omitempty"`
	HookPasswordVerificationAttemptSecrets        nullable.Nullable[string]                                         `json:"hook_password_verification_attempt_secrets,omitempty"`
	HookPasswordVerificationAttemptUri            nullable.Nullable[string]                                         `json:"hook_password_verification_attempt_uri,omitempty"`
	HookSendEmailEnabled                          nullable.Nullable[bool]                                           `json:"hook_send_email_enabled,omitempty"`
	HookSendEmailSecrets                          nullable.Nullable[string]                                         `json:"hook_send_email_secrets,omitempty"`
	HookSendEmailUri                              nullable.Nullable[string]                                         `json:"hook_send_email_uri,omitempty"`
	HookSendSmsEnabled                            nullable.Nullable[bool]                                           `json:"hook_send_sms_enabled,omitempty"`
	HookSendSmsSecrets                            nullable.Nullable[string]                                         `json:"hook_send_sms_secrets,omitempty"`
	HookSendSmsUri                                nullable.Nullable[string]                                         `json:"hook_send_sms_uri,omitempty"`
	JwtExp                                        nullable.Nullable[int]                                            `json:"jwt_exp,omitempty"`
	MailerAllowUnverifiedEmailSignIns             nullable.Nullable[bool]                                           `json:"mailer_allow_unverified_email_sign_ins,omitempty"`
	MailerAutoconfirm                             nullable.Nullable[bool]                                           `json:"mailer_autoconfirm,omitempty"`
	MailerOtpExp                                  *int                                                              `json:"mailer_otp_exp,omitempty"`
	MailerOtpLength                               nullable.Nullable[int]                                            `json:"mailer_otp_length,omitempty"`
	MailerSecureEmailChangeEnabled                nullable.Nullable[bool]                                           `json:"mailer_secure_email_change_enabled,omitempty"`
	MailerSubjectsConfirmation                    nullable.Nullable[string]                                         `json:"mailer_subjects_confirmation,omitempty"`
	MailerSubjectsEmailChange                     nullable.Nullable[string]                                         `json:"mailer_subjects_email_change,omitempty"`
	MailerSubjectsInvite                          nullable.Nullable[string]                                         `json:"mailer_subjects_invite,omitempty"`
	MailerSubjectsMagicLink                       nullable.Nullable[string]                                         `json:"mailer_subjects_magic_link,omitempty"`
	MailerSubjectsReauthentication                nullable.Nullable[string]                                         `json:"mailer_subjects_reauthentication,omitempty"`
	MailerSubjectsRecovery                        nullable.Nullable[string]                                         `json:"mailer_subjects_recovery,omitempty"`
	MailerTemplatesConfirmationContent            nullable.Nullable[string]                                         `json:"mailer_templates_confirmation_content,omitempty"`
	MailerTemplatesEmailChangeContent             nullable.Nullable[string]                                         `json:"mailer_templates_email_change_content,omitempty"`
	MailerTemplatesInviteContent                  nullable.Nullable[string]                                         `json:"mailer_templates_invite_content,omitempty"`
	MailerTemplatesMagicLinkContent               nullable.Nullable[string]                                         `json:"mailer_templates_magic_link_content,omitempty"`
	MailerTemplatesReauthenticationContent        nullable.Nullable[string]                                         `json:"mailer_templates_reauthentication_content,omitempty"`
	MailerTemplatesRecoveryContent                nullable.Nullable[string]                                         `json:"mailer_templates_recovery_content,omitempty"`
	MfaMaxEnrolledFactors                         nullable.Nullable[int]                                            `json:"mfa_max_enrolled_factors,omitempty"`
	MfaPhoneEnrollEnabled                         nullable.Nullable[bool]                                           `json:"mfa_phone_enroll_enabled,omitempty"`
	MfaPhoneMaxFrequency                          nullable.Nullable[int]                                            `json:"mfa_phone_max_frequency,omitempty"`
	MfaPhoneOtpLength                             nullable.Nullable[int]                                            `json:"mfa_phone_otp_length,omitempty"`
	MfaPhoneTemplate                              nullable.Nullable[string]                                         `json:"mfa_phone_template,omitempty"`
	MfaPhoneVerifyEnabled                         nullable.Nullable[bool]                                           `json:"mfa_phone_verify_enabled,omitempty"`
	MfaTotpEnrollEnabled                          nullable.Nullable[bool]                                           `json:"mfa_totp_enroll_enabled,omitempty"`
	MfaTotpVerifyEnabled                          nullable.Nullable[bool]                                           `json:"mfa_totp_verify_enabled,omitempty"`
	MfaWebAuthnEnrollEnabled                      nullable.Nullable[bool]                                           `json:"mfa_web_authn_enroll_enabled,omitempty"`
	MfaWebAuthnVerifyEnabled                      nullable.Nullable[bool]                                           `json:"mfa_web_authn_verify_enabled,omitempty"`
	PasswordHibpEnabled                           nullable.Nullable[bool]                                           `json:"password_hibp_enabled,omitempty"`
	PasswordMinLength                             nullable.Nullable[int]                                            `json:"password_min_length,omitempty"`
	PasswordRequiredCharacters                    nullable.Nullable[UpdateAuthConfigBodyPasswordRequiredCharacters] `json:"password_required_characters,omitempty"`
	RateLimitAnonymousUsers                       nullable.Nullable[int]                                            `json:"rate_limit_anonymous_users,omitempty"`
	RateLimitEmailSent                            nullable.Nullable[int]                                            `json:"rate_limit_email_sent,omitempty"`
	RateLimitOtp                                  nullable.Nullable[int]                                            `json:"rate_limit_otp,omitempty"`
	RateLimitSmsSent                              nullable.Nullable[int]                                            `json:"rate_limit_sms_sent,omitempty"`
	RateLimitTokenRefresh                         nullable.Nullable[int]                                            `json:"rate_limit_token_refresh,omitempty"`
	RateLimitVerify                               nullable.Nullable[int]                                            `json:"rate_limit_verify,omitempty"`
	RateLimitWeb3                                 nullable.Nullable[int]                                            `json:"rate_limit_web3,omitempty"`
	RefreshTokenRotationEnabled                   nullable.Nullable[bool]                                           `json:"refresh_token_rotation_enabled,omitempty"`
	SamlEnabled                                   nullable.Nullable[bool]                                           `json:"saml_enabled,omitempty"`
	SamlExternalUrl                               nullable.Nullable[string]                                         `json:"saml_external_url,omitempty"`
	SecurityCaptchaEnabled                        nullable.Nullable[bool]                                           `json:"security_captcha_enabled,omitempty"`
	SecurityCaptchaProvider                       nullable.Nullable[UpdateAuthConfigBodySecurityCaptchaProvider]    `json:"security_captcha_provider,omitempty"`
	SecurityCaptchaSecret                         nullable.Nullable[string]                                         `json:"security_captcha_secret,omitempty"`
	SecurityManualLinkingEnabled                  nullable.Nullable[bool]                                           `json:"security_manual_linking_enabled,omitempty"`
	SecurityRefreshTokenReuseInterval             nullable.Nullable[int]                                            `json:"security_refresh_token_reuse_interval,omitempty"`
	SecurityUpdatePasswordRequireReauthentication nullable.Nullable[bool]                                           `json:"security_update_password_require_reauthentication,omitempty"`
	SessionsInactivityTimeout                     nullable.Nullable[int]                                            `json:"sessions_inactivity_timeout,omitempty"`
	SessionsSinglePerUser                         nullable.Nullable[bool]                                           `json:"sessions_single_per_user,omitempty"`
	SessionsTags                                  nullable.Nullable[string]                                         `json:"sessions_tags,omitempty"`
	SessionsTimebox                               nullable.Nullable[int]                                            `json:"sessions_timebox,omitempty"`
	SiteUrl                                       nullable.Nullable[string]                                         `json:"site_url,omitempty"`
	SmsAutoconfirm                                nullable.Nullable[bool]                                           `json:"sms_autoconfirm,omitempty"`
	SmsMaxFrequency                               nullable.Nullable[int]                                            `json:"sms_max_frequency,omitempty"`
	SmsMessagebirdAccessKey                       nullable.Nullable[string]                                         `json:"sms_messagebird_access_key,omitempty"`
	SmsMessagebirdOriginator                      nullable.Nullable[string]                                         `json:"sms_messagebird_originator,omitempty"`
	SmsOtpExp                                     nullable.Nullable[int]                                            `json:"sms_otp_exp,omitempty"`
	SmsOtpLength                                  *int                                                              `json:"sms_otp_length,omitempty"`
	SmsProvider                                   nullable.Nullable[UpdateAuthConfigBodySmsProvider]                `json:"sms_provider,omitempty"`
	SmsTemplate                                   nullable.Nullable[string]                                         `json:"sms_template,omitempty"`
	SmsTestOtp                                    nullable.Nullable[string]                                         `json:"sms_test_otp,omitempty"`
	SmsTestOtpValidUntil                          nullable.Nullable[time.Time]                                      `json:"sms_test_otp_valid_until,omitempty"`
	SmsTextlocalApiKey                            nullable.Nullable[string]                                         `json:"sms_textlocal_api_key,omitempty"`
	SmsTextlocalSender                            nullable.Nullable[string]                                         `json:"sms_textlocal_sender,omitempty"`
	SmsTwilioAccountSid                           nullable.Nullable[string]                                         `json:"sms_twilio_account_sid,omitempty"`
	SmsTwilioAuthToken                            nullable.Nullable[string]                                         `json:"sms_twilio_auth_token,omitempty"`
	SmsTwilioContentSid                           nullable.Nullable[string]                                         `json:"sms_twilio_content_sid,omitempty"`
	SmsTwilioMessageServiceSid                    nullable.Nullable[string]                                         `json:"sms_twilio_message_service_sid,omitempty"`
	SmsTwilioVerifyAccountSid                     nullable.Nullable[string]                                         `json:"sms_twilio_verify_account_sid,omitempty"`
	SmsTwilioVerifyAuthToken                      nullable.Nullable[string]                                         `json:"sms_twilio_verify_auth_token,omitempty"`
	SmsTwilioVerifyMessageServiceSid              nullable.Nullable[string]                                         `json:"sms_twilio_verify_message_service_sid,omitempty"`
	SmsVonageApiKey                               nullable.Nullable[string]                                         `json:"sms_vonage_api_key,omitempty"`
	SmsVonageApiSecret                            nullable.Nullable[string]                                         `json:"sms_vonage_api_secret,omitempty"`
	SmsVonageFrom                                 nullable.Nullable[string]                                         `json:"sms_vonage_from,omitempty"`
	SmtpAdminEmail                                nullable.Nullable[string]                                         `json:"smtp_admin_email,omitempty"`
	SmtpHost                                      nullable.Nullable[string]                                         `json:"smtp_host,omitempty"`
	SmtpMaxFrequency                              nullable.Nullable[int]                                            `json:"smtp_max_frequency,omitempty"`
	SmtpPass                                      nullable.Nullable[string]                                         `json:"smtp_pass,omitempty"`
	SmtpPort                                      nullable.Nullable[string]                                         `json:"smtp_port,omitempty"`
	SmtpSenderName                                nullable.Nullable[string]                                         `json:"smtp_sender_name,omitempty"`
	SmtpUser                                      nullable.Nullable[string]                                         `json:"smtp_user,omitempty"`
	UriAllowList                                  nullable.Nullable[string]                                         `json:"uri_allow_list,omitempty"`
}

// UpdateAuthConfigBodyPasswordRequiredCharacters defines model for UpdateAuthConfigBody.PasswordRequiredCharacters.
type UpdateAuthConfigBodyPasswordRequiredCharacters string

// UpdateAuthConfigBodySecurityCaptchaProvider defines model for UpdateAuthConfigBody.SecurityCaptchaProvider.
type UpdateAuthConfigBodySecurityCaptchaProvider string

// UpdateAuthConfigBodySmsProvider defines model for UpdateAuthConfigBody.SmsProvider.
type UpdateAuthConfigBodySmsProvider string

// UpdateBranchBody defines model for UpdateBranchBody.
type UpdateBranchBody struct {
	BranchName *string `json:"branch_name,omitempty"`
	GitBranch  *string `json:"git_branch,omitempty"`
	Persistent *bool   `json:"persistent,omitempty"`

	// ResetOnPush This field is deprecated and will be ignored. Use v1-reset-a-branch endpoint directly instead.
	// Deprecated:
	ResetOnPush *bool                   `json:"reset_on_push,omitempty"`
	Status      *UpdateBranchBodyStatus `json:"status,omitempty"`
}

// UpdateBranchBodyStatus defines model for UpdateBranchBody.Status.
type UpdateBranchBodyStatus string

// UpdateCustomHostnameBody defines model for UpdateCustomHostnameBody.
type UpdateCustomHostnameBody struct {
	CustomHostname string `json:"custom_hostname"`
}

// UpdateCustomHostnameResponse defines model for UpdateCustomHostnameResponse.
type UpdateCustomHostnameResponse struct {
	CustomHostname string `json:"custom_hostname"`
	Data           struct {
		Errors   []interface{} `json:"errors"`
		Messages []interface{} `json:"messages"`
		Result   struct {
			CustomOriginServer    string `json:"custom_origin_server"`
			Hostname              string `json:"hostname"`
			Id                    string `json:"id"`
			OwnershipVerification struct {
				Name  string `json:"name"`
				Type  string `json:"type"`
				Value string `json:"value"`
			} `json:"ownership_verification"`
			Ssl struct {
				Status           string `json:"status"`
				ValidationErrors *[]struct {
					Message string `json:"message"`
				} `json:"validation_errors,omitempty"`
				ValidationRecords []struct {
					TxtName  string `json:"txt_name"`
					TxtValue string `json:"txt_value"`
				} `json:"validation_records"`
			} `json:"ssl"`
			Status             string    `json:"status"`
			VerificationErrors *[]string `json:"verification_errors,omitempty"`
		} `json:"result"`
		Success bool `json:"success"`
	} `json:"data"`
	Status UpdateCustomHostnameResponseStatus `json:"status"`
}

// UpdateCustomHostnameResponseStatus defines model for UpdateCustomHostnameResponse.Status.
type UpdateCustomHostnameResponseStatus string

// UpdatePgsodiumConfigBody defines model for UpdatePgsodiumConfigBody.
type UpdatePgsodiumConfigBody struct {
	RootKey string `json:"root_key"`
}

// UpdatePostgresConfigBody defines model for UpdatePostgresConfigBody.
type UpdatePostgresConfigBody struct {
	EffectiveCacheSize            *string                                         `json:"effective_cache_size,omitempty"`
	LogicalDecodingWorkMem        *string                                         `json:"logical_decoding_work_mem,omitempty"`
	MaintenanceWorkMem            *string                                         `json:"maintenance_work_mem,omitempty"`
	MaxConnections                *int                                            `json:"max_connections,omitempty"`
	MaxLocksPerTransaction        *int                                            `json:"max_locks_per_transaction,omitempty"`
	MaxParallelMaintenanceWorkers *int                                            `json:"max_parallel_maintenance_workers,omitempty"`
	MaxParallelWorkers            *int                                            `json:"max_parallel_workers,omitempty"`
	MaxParallelWorkersPerGather   *int                                            `json:"max_parallel_workers_per_gather,omitempty"`
	MaxReplicationSlots           *int                                            `json:"max_replication_slots,omitempty"`
	MaxSlotWalKeepSize            *string                                         `json:"max_slot_wal_keep_size,omitempty"`
	MaxStandbyArchiveDelay        *string                                         `json:"max_standby_archive_delay,omitempty"`
	MaxStandbyStreamingDelay      *string                                         `json:"max_standby_streaming_delay,omitempty"`
	MaxWalSenders                 *int                                            `json:"max_wal_senders,omitempty"`
	MaxWalSize                    *string                                         `json:"max_wal_size,omitempty"`
	MaxWorkerProcesses            *int                                            `json:"max_worker_processes,omitempty"`
	RestartDatabase               *bool                                           `json:"restart_database,omitempty"`
	SessionReplicationRole        *UpdatePostgresConfigBodySessionReplicationRole `json:"session_replication_role,omitempty"`
	SharedBuffers                 *string                                         `json:"shared_buffers,omitempty"`
	StatementTimeout              *string                                         `json:"statement_timeout,omitempty"`
	TrackActivityQuerySize        *string                                         `json:"track_activity_query_size,omitempty"`
	TrackCommitTimestamp          *bool                                           `json:"track_commit_timestamp,omitempty"`
	WalKeepSize                   *string                                         `json:"wal_keep_size,omitempty"`
	WalSenderTimeout              *string                                         `json:"wal_sender_timeout,omitempty"`
	WorkMem                       *string                                         `json:"work_mem,omitempty"`
}

// UpdatePostgresConfigBodySessionReplicationRole defines model for UpdatePostgresConfigBody.SessionReplicationRole.
type UpdatePostgresConfigBodySessionReplicationRole string

// UpdateProviderBody defines model for UpdateProviderBody.
type UpdateProviderBody struct {
	AttributeMapping *struct {
		Keys map[string]struct {
			Array   *bool        `json:"array,omitempty"`
			Default *interface{} `json:"default,omitempty"`
			Name    *string      `json:"name,omitempty"`
			Names   *[]string    `json:"names,omitempty"`
		} `json:"keys"`
	} `json:"attribute_mapping,omitempty"`
	Domains     *[]string `json:"domains,omitempty"`
	MetadataUrl *string   `json:"metadata_url,omitempty"`
	MetadataXml *string   `json:"metadata_xml,omitempty"`
}

// UpdateProviderResponse defines model for UpdateProviderResponse.
type UpdateProviderResponse struct {
	CreatedAt *string `json:"created_at,omitempty"`
	Domains   *[]struct {
		CreatedAt *string `json:"created_at,omitempty"`
		Domain    *string `json:"domain,omitempty"`
		Id        string  `json:"id"`
		UpdatedAt *string `json:"updated_at,omitempty"`
	} `json:"domains,omitempty"`
	Id   string `json:"id"`
	Saml *struct {
		AttributeMapping *struct {
			Keys map[string]struct {
				Array   *bool        `json:"array,omitempty"`
				Default *interface{} `json:"default,omitempty"`
				Name    *string      `json:"name,omitempty"`
				Names   *[]string    `json:"names,omitempty"`
			} `json:"keys"`
		} `json:"attribute_mapping,omitempty"`
		EntityId    string  `json:"entity_id"`
		Id          string  `json:"id"`
		MetadataUrl *string `json:"metadata_url,omitempty"`
		MetadataXml *string `json:"metadata_xml,omitempty"`
	} `json:"saml,omitempty"`
	UpdatedAt *string `json:"updated_at,omitempty"`
}

// UpdateSigningKeyBody defines model for UpdateSigningKeyBody.
type UpdateSigningKeyBody struct {
	Status UpdateSigningKeyBodyStatus `json:"status"`
}

// UpdateSigningKeyBodyStatus defines model for UpdateSigningKeyBody.Status.
type UpdateSigningKeyBodyStatus string

// UpdateStorageConfigBody defines model for UpdateStorageConfigBody.
type UpdateStorageConfigBody struct {
	Features *struct {
		ImageTransformation struct {
			Enabled bool `json:"enabled"`
		} `json:"imageTransformation"`
		S3Protocol struct {
			Enabled bool `json:"enabled"`
		} `json:"s3Protocol"`
	} `json:"features,omitempty"`
	FileSizeLimit *int64 `json:"fileSizeLimit,omitempty"`
}

// UpdateSupavisorConfigBody defines model for UpdateSupavisorConfigBody.
type UpdateSupavisorConfigBody struct {
	DefaultPoolSize nullable.Nullable[int] `json:"default_pool_size,omitempty"`

	// PoolMode Dedicated pooler mode for the project
	PoolMode *UpdateSupavisorConfigBodyPoolMode `json:"pool_mode,omitempty"`
}

// UpdateSupavisorConfigBodyPoolMode Dedicated pooler mode for the project
type UpdateSupavisorConfigBodyPoolMode string

// UpdateSupavisorConfigResponse defines model for UpdateSupavisorConfigResponse.
type UpdateSupavisorConfigResponse struct {
	DefaultPoolSize nullable.Nullable[int] `json:"default_pool_size"`
	PoolMode        string                 `json:"pool_mode"`
}

// UpgradeDatabaseBody defines model for UpgradeDatabaseBody.
type UpgradeDatabaseBody struct {
	ReleaseChannel *UpgradeDatabaseBodyReleaseChannel `json:"release_channel,omitempty"`
	TargetVersion  string                             `json:"target_version"`
}

// UpgradeDatabaseBodyReleaseChannel defines model for UpgradeDatabaseBody.ReleaseChannel.
type UpgradeDatabaseBodyReleaseChannel string

// V1BackupsResponse defines model for V1BackupsResponse.
type V1BackupsResponse struct {
	Backups []struct {
		InsertedAt       string                         `json:"inserted_at"`
		IsPhysicalBackup bool                           `json:"is_physical_backup"`
		Status           V1BackupsResponseBackupsStatus `json:"status"`
	} `json:"backups"`
	PhysicalBackupData struct {
		EarliestPhysicalBackupDateUnix *int `json:"earliest_physical_backup_date_unix,omitempty"`
		LatestPhysicalBackupDateUnix   *int `json:"latest_physical_backup_date_unix,omitempty"`
	} `json:"physical_backup_data"`
	PitrEnabled bool   `json:"pitr_enabled"`
	Region      string `json:"region"`
	WalgEnabled bool   `json:"walg_enabled"`
}

// V1BackupsResponseBackupsStatus defines model for V1BackupsResponse.Backups.Status.
type V1BackupsResponseBackupsStatus string

// V1CreateFunctionBody defines model for V1CreateFunctionBody.
type V1CreateFunctionBody struct {
	Body      string `json:"body"`
	Name      string `json:"name"`
	Slug      string `json:"slug"`
	VerifyJwt *bool  `json:"verify_jwt,omitempty"`
}

// V1CreateMigrationBody defines model for V1CreateMigrationBody.
type V1CreateMigrationBody struct {
	Name  *string `json:"name,omitempty"`
	Query string  `json:"query"`
}

// V1CreateProjectBody defines model for V1CreateProjectBody.
type V1CreateProjectBody struct {
	// DbPass Database password
	DbPass              string                                  `json:"db_pass"`
	DesiredInstanceSize *V1CreateProjectBodyDesiredInstanceSize `json:"desired_instance_size,omitempty"`

	// KpsEnabled This field is deprecated and is ignored in this request
	// Deprecated:
	KpsEnabled *bool `json:"kps_enabled,omitempty"`

	// Name Name of your project
	Name string `json:"name"`

	// OrganizationId Slug of your organization
	OrganizationId string `json:"organization_id"`

	// Plan Subscription Plan is now set on organization level and is ignored in this request
	// Deprecated:
	Plan *V1CreateProjectBodyPlan `json:"plan,omitempty"`

	// Region Region you want your server to reside in
	Region V1CreateProjectBodyRegion `json:"region"`

	// TemplateUrl Template URL used to create the project from the CLI.
	TemplateUrl *string `json:"template_url,omitempty"`
}

// V1CreateProjectBodyDesiredInstanceSize defines model for V1CreateProjectBody.DesiredInstanceSize.
type V1CreateProjectBodyDesiredInstanceSize string

// V1CreateProjectBodyPlan Subscription Plan is now set on organization level and is ignored in this request
type V1CreateProjectBodyPlan string

// V1CreateProjectBodyRegion Region you want your server to reside in
type V1CreateProjectBodyRegion string

// V1ListMigrationsResponse defines model for V1ListMigrationsResponse.
type V1ListMigrationsResponse = []struct {
	Name    *string `json:"name,omitempty"`
	Version string  `json:"version"`
}

// V1OrganizationMemberResponse defines model for V1OrganizationMemberResponse.
type V1OrganizationMemberResponse struct {
	Email      *string `json:"email,omitempty"`
	MfaEnabled bool    `json:"mfa_enabled"`
	RoleName   string  `json:"role_name"`
	UserId     string  `json:"user_id"`
	UserName   string  `json:"user_name"`
}

// V1OrganizationSlugResponse defines model for V1OrganizationSlugResponse.
type V1OrganizationSlugResponse struct {
	AllowedReleaseChannels []V1OrganizationSlugResponseAllowedReleaseChannels `json:"allowed_release_channels"`
	Id                     string                                             `json:"id"`
	Name                   string                                             `json:"name"`
	OptInTags              []V1OrganizationSlugResponseOptInTags              `json:"opt_in_tags"`
	Plan                   *V1OrganizationSlugResponsePlan                    `json:"plan,omitempty"`
}

// V1OrganizationSlugResponseAllowedReleaseChannels defines model for V1OrganizationSlugResponse.AllowedReleaseChannels.
type V1OrganizationSlugResponseAllowedReleaseChannels string

// V1OrganizationSlugResponseOptInTags defines model for V1OrganizationSlugResponse.OptInTags.
type V1OrganizationSlugResponseOptInTags string

// V1OrganizationSlugResponsePlan defines model for V1OrganizationSlugResponse.Plan.
type V1OrganizationSlugResponsePlan string

// V1PgbouncerConfigResponse defines model for V1PgbouncerConfigResponse.
type V1PgbouncerConfigResponse struct {
	ConnectionString        *string                            `json:"connection_string,omitempty"`
	DefaultPoolSize         *float32                           `json:"default_pool_size,omitempty"`
	IgnoreStartupParameters *string                            `json:"ignore_startup_parameters,omitempty"`
	MaxClientConn           *float32                           `json:"max_client_conn,omitempty"`
	PoolMode                *V1PgbouncerConfigResponsePoolMode `json:"pool_mode,omitempty"`
}

// V1PgbouncerConfigResponsePoolMode defines model for V1PgbouncerConfigResponse.PoolMode.
type V1PgbouncerConfigResponsePoolMode string

// V1PostgrestConfigResponse defines model for V1PostgrestConfigResponse.
type V1PostgrestConfigResponse struct {
	DbExtraSearchPath string `json:"db_extra_search_path"`

	// DbPool If `null`, the value is automatically configured based on compute size.
	DbPool   nullable.Nullable[int] `json:"db_pool"`
	DbSchema string                 `json:"db_schema"`
	MaxRows  int                    `json:"max_rows"`
}

// V1ProjectAdvisorsResponse defines model for V1ProjectAdvisorsResponse.
type V1ProjectAdvisorsResponse struct {
	Lints []struct {
		CacheKey    string                                     `json:"cache_key"`
		Categories  []V1ProjectAdvisorsResponseLintsCategories `json:"categories"`
		Description string                                     `json:"description"`
		Detail      string                                     `json:"detail"`
		Facing      V1ProjectAdvisorsResponseLintsFacing       `json:"facing"`
		Level       V1ProjectAdvisorsResponseLintsLevel        `json:"level"`
		Metadata    *struct {
			Entity      *string                                     `json:"entity,omitempty"`
			FkeyColumns *[]float32                                  `json:"fkey_columns,omitempty"`
			FkeyName    *string                                     `json:"fkey_name,omitempty"`
			Name        *string                                     `json:"name,omitempty"`
			Schema      *string                                     `json:"schema,omitempty"`
			Type        *V1ProjectAdvisorsResponseLintsMetadataType `json:"type,omitempty"`
		} `json:"metadata,omitempty"`
		Name        V1ProjectAdvisorsResponseLintsName `json:"name"`
		Remediation string                             `json:"remediation"`
		Title       string                             `json:"title"`
	} `json:"lints"`
}

// V1ProjectAdvisorsResponseLintsCategories defines model for V1ProjectAdvisorsResponse.Lints.Categories.
type V1ProjectAdvisorsResponseLintsCategories string

// V1ProjectAdvisorsResponseLintsFacing defines model for V1ProjectAdvisorsResponse.Lints.Facing.
type V1ProjectAdvisorsResponseLintsFacing string

// V1ProjectAdvisorsResponseLintsLevel defines model for V1ProjectAdvisorsResponse.Lints.Level.
type V1ProjectAdvisorsResponseLintsLevel string

// V1ProjectAdvisorsResponseLintsMetadataType defines model for V1ProjectAdvisorsResponse.Lints.Metadata.Type.
type V1ProjectAdvisorsResponseLintsMetadataType string

// V1ProjectAdvisorsResponseLintsName defines model for V1ProjectAdvisorsResponse.Lints.Name.
type V1ProjectAdvisorsResponseLintsName string

// V1ProjectRefResponse defines model for V1ProjectRefResponse.
type V1ProjectRefResponse struct {
	Id   int    `json:"id"`
	Name string `json:"name"`
	Ref  string `json:"ref"`
}

// V1ProjectResponse defines model for V1ProjectResponse.
type V1ProjectResponse struct {
	// CreatedAt Creation timestamp
	CreatedAt string `json:"created_at"`

	// Id Id of your project
	Id string `json:"id"`

	// Name Name of your project
	Name string `json:"name"`

	// OrganizationId Slug of your organization
	OrganizationId string `json:"organization_id"`

	// Region Region of your project
	Region string                  `json:"region"`
	Status V1ProjectResponseStatus `json:"status"`
}

// V1ProjectResponseStatus defines model for V1ProjectResponse.Status.
type V1ProjectResponseStatus string

// V1ProjectWithDatabaseResponse defines model for V1ProjectWithDatabaseResponse.
type V1ProjectWithDatabaseResponse struct {
	// CreatedAt Creation timestamp
	CreatedAt string `json:"created_at"`
	Database  struct {
		// Host Database host
		Host string `json:"host"`

		// PostgresEngine Database engine
		PostgresEngine string `json:"postgres_engine"`

		// ReleaseChannel Release channel
		ReleaseChannel string `json:"release_channel"`

		// Version Database version
		Version string `json:"version"`
	} `json:"database"`

	// Id Id of your project
	Id string `json:"id"`

	// Name Name of your project
	Name string `json:"name"`

	// OrganizationId Slug of your organization
	OrganizationId string `json:"organization_id"`

	// Region Region of your project
	Region string                              `json:"region"`
	Status V1ProjectWithDatabaseResponseStatus `json:"status"`
}

// V1ProjectWithDatabaseResponseStatus defines model for V1ProjectWithDatabaseResponse.Status.
type V1ProjectWithDatabaseResponseStatus string

// V1RestorePitrBody defines model for V1RestorePitrBody.
type V1RestorePitrBody struct {
	RecoveryTimeTargetUnix int64 `json:"recovery_time_target_unix"`
}

// V1RunQueryBody defines model for V1RunQueryBody.
type V1RunQueryBody struct {
	Query    string `json:"query"`
	ReadOnly *bool  `json:"read_only,omitempty"`
}

// V1ServiceHealthResponse defines model for V1ServiceHealthResponse.
type V1ServiceHealthResponse struct {
	Error   *string                       `json:"error,omitempty"`
	Healthy bool                          `json:"healthy"`
	Info    *V1ServiceHealthResponse_Info `json:"info,omitempty"`
	Name    V1ServiceHealthResponseName   `json:"name"`
	Status  V1ServiceHealthResponseStatus `json:"status"`
}

// V1ServiceHealthResponseInfo0 defines model for .
type V1ServiceHealthResponseInfo0 struct {
	Description string                           `json:"description"`
	Name        V1ServiceHealthResponseInfo0Name `json:"name"`
	Version     string                           `json:"version"`
}

// V1ServiceHealthResponseInfo0Name defines model for V1ServiceHealthResponse.Info.0.Name.
type V1ServiceHealthResponseInfo0Name string

// V1ServiceHealthResponseInfo1 defines model for .
type V1ServiceHealthResponseInfo1 struct {
	ConnectedCluster int  `json:"connected_cluster"`
	DbConnected      bool `json:"db_connected"`
	Healthy          bool `json:"healthy"`
}

// V1ServiceHealthResponse_Info defines model for V1ServiceHealthResponse.Info.
type V1ServiceHealthResponse_Info struct {
	union json.RawMessage
}

// V1ServiceHealthResponseName defines model for V1ServiceHealthResponse.Name.
type V1ServiceHealthResponseName string

// V1ServiceHealthResponseStatus defines model for V1ServiceHealthResponse.Status.
type V1ServiceHealthResponseStatus string

// V1StorageBucketResponse defines model for V1StorageBucketResponse.
type V1StorageBucketResponse struct {
	CreatedAt string `json:"created_at"`
	Id        string `json:"id"`
	Name      string `json:"name"`
	Owner     string `json:"owner"`
	Public    bool   `json:"public"`
	UpdatedAt string `json:"updated_at"`
}

// V1UpdateFunctionBody defines model for V1UpdateFunctionBody.
type V1UpdateFunctionBody struct {
	Body      *string `json:"body,omitempty"`
	Name      *string `json:"name,omitempty"`
	VerifyJwt *bool   `json:"verify_jwt,omitempty"`
}

// V1UpdatePostgrestConfigBody defines model for V1UpdatePostgrestConfigBody.
type V1UpdatePostgrestConfigBody struct {
	DbExtraSearchPath *string `json:"db_extra_search_path,omitempty"`
	DbPool            *int    `json:"db_pool,omitempty"`
	DbSchema          *string `json:"db_schema,omitempty"`
	MaxRows           *int    `json:"max_rows,omitempty"`
}

// VanitySubdomainBody defines model for VanitySubdomainBody.
type VanitySubdomainBody struct {
	VanitySubdomain string `json:"vanity_subdomain"`
}

// VanitySubdomainConfigResponse defines model for VanitySubdomainConfigResponse.
type VanitySubdomainConfigResponse struct {
	CustomDomain *string                             `json:"custom_domain,omitempty"`
	Status       VanitySubdomainConfigResponseStatus `json:"status"`
}

// VanitySubdomainConfigResponseStatus defines model for VanitySubdomainConfigResponse.Status.
type VanitySubdomainConfigResponseStatus string

// V1DiffABranchParams defines parameters for V1DiffABranch.
type V1DiffABranchParams struct {
	IncludedSchemas *string `form:"included_schemas,omitempty" json:"included_schemas,omitempty"`
}

// V1AuthorizeUserParams defines parameters for V1AuthorizeUser.
type V1AuthorizeUserParams struct {
	ClientId            openapi_types.UUID                        `form:"client_id" json:"client_id"`
	ResponseType        V1AuthorizeUserParamsResponseType         `form:"response_type" json:"response_type"`
	RedirectUri         string                                    `form:"redirect_uri" json:"redirect_uri"`
	Scope               *string                                   `form:"scope,omitempty" json:"scope,omitempty"`
	State               *string                                   `form:"state,omitempty" json:"state,omitempty"`
	ResponseMode        *string                                   `form:"response_mode,omitempty" json:"response_mode,omitempty"`
	CodeChallenge       *string                                   `form:"code_challenge,omitempty" json:"code_challenge,omitempty"`
	CodeChallengeMethod *V1AuthorizeUserParamsCodeChallengeMethod `form:"code_challenge_method,omitempty" json:"code_challenge_method,omitempty"`
}

// V1AuthorizeUserParamsResponseType defines parameters for V1AuthorizeUser.
type V1AuthorizeUserParamsResponseType string

// V1AuthorizeUserParamsCodeChallengeMethod defines parameters for V1AuthorizeUser.
type V1AuthorizeUserParamsCodeChallengeMethod string

// GetLogsParams defines parameters for GetLogs.
type GetLogsParams struct {
	Sql               *string    `form:"sql,omitempty" json:"sql,omitempty"`
	IsoTimestampStart *time.Time `form:"iso_timestamp_start,omitempty" json:"iso_timestamp_start,omitempty"`
	IsoTimestampEnd   *time.Time `form:"iso_timestamp_end,omitempty" json:"iso_timestamp_end,omitempty"`
}

// GetApiCountsParams defines parameters for GetApiCounts.
type GetApiCountsParams struct {
	Interval *GetApiCountsParamsInterval `form:"interval,omitempty" json:"interval,omitempty"`
}

// GetApiCountsParamsInterval defines parameters for GetApiCounts.
type GetApiCountsParamsInterval string

// V1GetProjectApiKeysParams defines parameters for V1GetProjectApiKeys.
type V1GetProjectApiKeysParams struct {
	// Reveal Boolean string, true or false
	Reveal *bool `form:"reveal,omitempty" json:"reveal,omitempty"`
}

// CreateApiKeyParams defines parameters for CreateApiKey.
type CreateApiKeyParams struct {
	// Reveal Boolean string, true or false
	Reveal *bool `form:"reveal,omitempty" json:"reveal,omitempty"`
}

// DeleteApiKeyParams defines parameters for DeleteApiKey.
type DeleteApiKeyParams struct {
	// Reveal Boolean string, true or false
	Reveal *bool `form:"reveal,omitempty" json:"reveal,omitempty"`
}

// GetApiKeyParams defines parameters for GetApiKey.
type GetApiKeyParams struct {
	// Reveal Boolean string, true or false
	Reveal *bool `form:"reveal,omitempty" json:"reveal,omitempty"`
}

// UpdateApiKeyParams defines parameters for UpdateApiKey.
type UpdateApiKeyParams struct {
	// Reveal Boolean string, true or false
	Reveal *bool `form:"reveal,omitempty" json:"reveal,omitempty"`
}

// V1ApplyAMigrationParams defines parameters for V1ApplyAMigration.
type V1ApplyAMigrationParams struct {
	// IdempotencyKey A unique key to ensure the same migration is tracked only once.
	IdempotencyKey *string `json:"Idempotency-Key,omitempty"`
}

// V1CreateAFunctionParams defines parameters for V1CreateAFunction.
type V1CreateAFunctionParams struct {
	Slug *string `form:"slug,omitempty" json:"slug,omitempty"`
	Name *string `form:"name,omitempty" json:"name,omitempty"`

	// VerifyJwt Boolean string, true or false
	VerifyJwt *bool `form:"verify_jwt,omitempty" json:"verify_jwt,omitempty"`

	// ImportMap Boolean string, true or false
	ImportMap      *bool   `form:"import_map,omitempty" json:"import_map,omitempty"`
	EntrypointPath *string `form:"entrypoint_path,omitempty" json:"entrypoint_path,omitempty"`
	ImportMapPath  *string `form:"import_map_path,omitempty" json:"import_map_path,omitempty"`
}

// V1DeployAFunctionParams defines parameters for V1DeployAFunction.
type V1DeployAFunctionParams struct {
	Slug *string `form:"slug,omitempty" json:"slug,omitempty"`

	// BundleOnly Boolean string, true or false
	BundleOnly *bool `form:"bundleOnly,omitempty" json:"bundleOnly,omitempty"`
}

// V1UpdateAFunctionParams defines parameters for V1UpdateAFunction.
type V1UpdateAFunctionParams struct {
	Slug *string `form:"slug,omitempty" json:"slug,omitempty"`
	Name *string `form:"name,omitempty" json:"name,omitempty"`

	// VerifyJwt Boolean string, true or false
	VerifyJwt *bool `form:"verify_jwt,omitempty" json:"verify_jwt,omitempty"`

	// ImportMap Boolean string, true or false
	ImportMap      *bool   `form:"import_map,omitempty" json:"import_map,omitempty"`
	EntrypointPath *string `form:"entrypoint_path,omitempty" json:"entrypoint_path,omitempty"`
	ImportMapPath  *string `form:"import_map_path,omitempty" json:"import_map_path,omitempty"`
}

// V1GetServicesHealthParams defines parameters for V1GetServicesHealth.
type V1GetServicesHealthParams struct {
	Services  []V1GetServicesHealthParamsServices `form:"services" json:"services"`
	TimeoutMs *int                                `form:"timeout_ms,omitempty" json:"timeout_ms,omitempty"`
}

// V1GetServicesHealthParamsServices defines parameters for V1GetServicesHealth.
type V1GetServicesHealthParamsServices string

// V1BulkDeleteSecretsJSONBody defines parameters for V1BulkDeleteSecrets.
type V1BulkDeleteSecretsJSONBody = []string

// V1GenerateTypescriptTypesParams defines parameters for V1GenerateTypescriptTypes.
type V1GenerateTypescriptTypesParams struct {
	IncludedSchemas *string `form:"included_schemas,omitempty" json:"included_schemas,omitempty"`
}

// V1GetPostgresUpgradeStatusParams defines parameters for V1GetPostgresUpgradeStatus.
type V1GetPostgresUpgradeStatusParams struct {
	TrackingId *string `form:"tracking_id,omitempty" json:"tracking_id,omitempty"`
}

// V1ListAllSnippetsParams defines parameters for V1ListAllSnippets.
type V1ListAllSnippetsParams struct {
	// ProjectRef Project ref
	ProjectRef *string                           `form:"project_ref,omitempty" json:"project_ref,omitempty"`
	Cursor     *string                           `form:"cursor,omitempty" json:"cursor,omitempty"`
	Limit      *string                           `form:"limit,omitempty" json:"limit,omitempty"`
	SortBy     *V1ListAllSnippetsParamsSortBy    `form:"sort_by,omitempty" json:"sort_by,omitempty"`
	SortOrder  *V1ListAllSnippetsParamsSortOrder `form:"sort_order,omitempty" json:"sort_order,omitempty"`
}

// V1ListAllSnippetsParamsSortBy defines parameters for V1ListAllSnippets.
type V1ListAllSnippetsParamsSortBy string

// V1ListAllSnippetsParamsSortOrder defines parameters for V1ListAllSnippets.
type V1ListAllSnippetsParamsSortOrder string

// V1UpdateABranchConfigJSONRequestBody defines body for V1UpdateABranchConfig for application/json ContentType.
type V1UpdateABranchConfigJSONRequestBody = UpdateBranchBody

// V1MergeABranchJSONRequestBody defines body for V1MergeABranch for application/json ContentType.
type V1MergeABranchJSONRequestBody = BranchActionBody

// V1PushABranchJSONRequestBody defines body for V1PushABranch for application/json ContentType.
type V1PushABranchJSONRequestBody = BranchActionBody

// V1ResetABranchJSONRequestBody defines body for V1ResetABranch for application/json ContentType.
type V1ResetABranchJSONRequestBody = BranchActionBody

// V1RevokeTokenJSONRequestBody defines body for V1RevokeToken for application/json ContentType.
type V1RevokeTokenJSONRequestBody = OAuthRevokeTokenBody

// V1ExchangeOauthTokenFormdataRequestBody defines body for V1ExchangeOauthToken for application/x-www-form-urlencoded ContentType.
type V1ExchangeOauthTokenFormdataRequestBody = OAuthTokenBody

// V1CreateAnOrganizationJSONRequestBody defines body for V1CreateAnOrganization for application/json ContentType.
type V1CreateAnOrganizationJSONRequestBody = CreateOrganizationV1

// V1CreateAProjectJSONRequestBody defines body for V1CreateAProject for application/json ContentType.
type V1CreateAProjectJSONRequestBody = V1CreateProjectBody

// CreateApiKeyJSONRequestBody defines body for CreateApiKey for application/json ContentType.
type CreateApiKeyJSONRequestBody = CreateApiKeyBody

// UpdateApiKeyJSONRequestBody defines body for UpdateApiKey for application/json ContentType.
type UpdateApiKeyJSONRequestBody = UpdateApiKeyBody

// V1ApplyProjectAddonJSONRequestBody defines body for V1ApplyProjectAddon for application/json ContentType.
type V1ApplyProjectAddonJSONRequestBody = ApplyProjectAddonBody

// V1CreateABranchJSONRequestBody defines body for V1CreateABranch for application/json ContentType.
type V1CreateABranchJSONRequestBody = CreateBranchBody

// V1UpdateAuthServiceConfigJSONRequestBody defines body for V1UpdateAuthServiceConfig for application/json ContentType.
type V1UpdateAuthServiceConfigJSONRequestBody = UpdateAuthConfigBody

// CreateSigningKeyForProjectJSONRequestBody defines body for CreateSigningKeyForProject for application/json ContentType.
type CreateSigningKeyForProjectJSONRequestBody = CreateSigningKeyBody

// PatchSigningKeyJSONRequestBody defines body for PatchSigningKey for application/json ContentType.
type PatchSigningKeyJSONRequestBody = UpdateSigningKeyBody

// V1CreateASsoProviderJSONRequestBody defines body for V1CreateASsoProvider for application/json ContentType.
type V1CreateASsoProviderJSONRequestBody = CreateProviderBody

// V1UpdateASsoProviderJSONRequestBody defines body for V1UpdateASsoProvider for application/json ContentType.
type V1UpdateASsoProviderJSONRequestBody = UpdateProviderBody

// CreateTPAForProjectJSONRequestBody defines body for CreateTPAForProject for application/json ContentType.
type CreateTPAForProjectJSONRequestBody = CreateThirdPartyAuthBody

// V1UpdatePoolerConfigJSONRequestBody defines body for V1UpdatePoolerConfig for application/json ContentType.
type V1UpdatePoolerConfigJSONRequestBody = UpdateSupavisorConfigBody

// V1UpdatePostgresConfigJSONRequestBody defines body for V1UpdatePostgresConfig for application/json ContentType.
type V1UpdatePostgresConfigJSONRequestBody = UpdatePostgresConfigBody

// V1UpdateStorageConfigJSONRequestBody defines body for V1UpdateStorageConfig for application/json ContentType.
type V1UpdateStorageConfigJSONRequestBody = UpdateStorageConfigBody

// V1UpdateHostnameConfigJSONRequestBody defines body for V1UpdateHostnameConfig for application/json ContentType.
type V1UpdateHostnameConfigJSONRequestBody = UpdateCustomHostnameBody

// V1RestorePitrBackupJSONRequestBody defines body for V1RestorePitrBackup for application/json ContentType.
type V1RestorePitrBackupJSONRequestBody = V1RestorePitrBody

// V1ApplyAMigrationJSONRequestBody defines body for V1ApplyAMigration for application/json ContentType.
type V1ApplyAMigrationJSONRequestBody = V1CreateMigrationBody

// V1RunAQueryJSONRequestBody defines body for V1RunAQuery for application/json ContentType.
type V1RunAQueryJSONRequestBody = V1RunQueryBody

// V1CreateAFunctionJSONRequestBody defines body for V1CreateAFunction for application/json ContentType.
type V1CreateAFunctionJSONRequestBody = V1CreateFunctionBody

// V1BulkUpdateFunctionsJSONRequestBody defines body for V1BulkUpdateFunctions for application/json ContentType.
type V1BulkUpdateFunctionsJSONRequestBody = BulkUpdateFunctionBody

// V1DeployAFunctionMultipartRequestBody defines body for V1DeployAFunction for multipart/form-data ContentType.
type V1DeployAFunctionMultipartRequestBody = FunctionDeployBody

// V1UpdateAFunctionJSONRequestBody defines body for V1UpdateAFunction for application/json ContentType.
type V1UpdateAFunctionJSONRequestBody = V1UpdateFunctionBody

// V1DeleteNetworkBansJSONRequestBody defines body for V1DeleteNetworkBans for application/json ContentType.
type V1DeleteNetworkBansJSONRequestBody = RemoveNetworkBanRequest

// V1UpdateNetworkRestrictionsJSONRequestBody defines body for V1UpdateNetworkRestrictions for application/json ContentType.
type V1UpdateNetworkRestrictionsJSONRequestBody = NetworkRestrictionsRequest

// V1UpdatePgsodiumConfigJSONRequestBody defines body for V1UpdatePgsodiumConfig for application/json ContentType.
type V1UpdatePgsodiumConfigJSONRequestBody = UpdatePgsodiumConfigBody

// V1UpdatePostgrestServiceConfigJSONRequestBody defines body for V1UpdatePostgrestServiceConfig for application/json ContentType.
type V1UpdatePostgrestServiceConfigJSONRequestBody = V1UpdatePostgrestConfigBody

// V1RemoveAReadReplicaJSONRequestBody defines body for V1RemoveAReadReplica for application/json ContentType.
type V1RemoveAReadReplicaJSONRequestBody = RemoveReadReplicaBody

// V1SetupAReadReplicaJSONRequestBody defines body for V1SetupAReadReplica for application/json ContentType.
type V1SetupAReadReplicaJSONRequestBody = SetUpReadReplicaBody

// V1BulkDeleteSecretsJSONRequestBody defines body for V1BulkDeleteSecrets for application/json ContentType.
type V1BulkDeleteSecretsJSONRequestBody = V1BulkDeleteSecretsJSONBody

// V1BulkCreateSecretsJSONRequestBody defines body for V1BulkCreateSecrets for application/json ContentType.
type V1BulkCreateSecretsJSONRequestBody = CreateSecretBody

// V1UpdateSslEnforcementConfigJSONRequestBody defines body for V1UpdateSslEnforcementConfig for application/json ContentType.
type V1UpdateSslEnforcementConfigJSONRequestBody = SslEnforcementRequest

// V1UpgradePostgresVersionJSONRequestBody defines body for V1UpgradePostgresVersion for application/json ContentType.
type V1UpgradePostgresVersionJSONRequestBody = UpgradeDatabaseBody

// V1ActivateVanitySubdomainConfigJSONRequestBody defines body for V1ActivateVanitySubdomainConfig for application/json ContentType.
type V1ActivateVanitySubdomainConfigJSONRequestBody = VanitySubdomainBody

// V1CheckVanitySubdomainAvailabilityJSONRequestBody defines body for V1CheckVanitySubdomainAvailability for application/json ContentType.
type V1CheckVanitySubdomainAvailabilityJSONRequestBody = VanitySubdomainBody

// Getter for additional properties for GetProjectDbMetadataResponse_Databases_Schemas_Item. Returns the specified
// element and whether it was found
func (a GetProjectDbMetadataResponse_Databases_Schemas_Item) Get(fieldName string) (value interface{}, found bool) {
	if a.AdditionalProperties != nil {
		value, found = a.AdditionalProperties[fieldName]
	}
	return
}

// Setter for additional properties for GetProjectDbMetadataResponse_Databases_Schemas_Item
func (a *GetProjectDbMetadataResponse_Databases_Schemas_Item) Set(fieldName string, value interface{}) {
	if a.AdditionalProperties == nil {
		a.AdditionalProperties = make(map[string]interface{})
	}
	a.AdditionalProperties[fieldName] = value
}

// Override default JSON handling for GetProjectDbMetadataResponse_Databases_Schemas_Item to handle AdditionalProperties
func (a *GetProjectDbMetadataResponse_Databases_Schemas_Item) UnmarshalJSON(b []byte) error {
	object := make(map[string]json.RawMessage)
	err := json.Unmarshal(b, &object)
	if err != nil {
		return err
	}

	if raw, found := object["name"]; found {
		err = json.Unmarshal(raw, &a.Name)
		if err != nil {
			return fmt.Errorf("error reading 'name': %w", err)
		}
		delete(object, "name")
	}

	if len(object) != 0 {
		a.AdditionalProperties = make(map[string]interface{})
		for fieldName, fieldBuf := range object {
			var fieldVal interface{}
			err := json.Unmarshal(fieldBuf, &fieldVal)
			if err != nil {
				return fmt.Errorf("error unmarshaling field %s: %w", fieldName, err)
			}
			a.AdditionalProperties[fieldName] = fieldVal
		}
	}
	return nil
}

// Override default JSON handling for GetProjectDbMetadataResponse_Databases_Schemas_Item to handle AdditionalProperties
func (a GetProjectDbMetadataResponse_Databases_Schemas_Item) MarshalJSON() ([]byte, error) {
	var err error
	object := make(map[string]json.RawMessage)

	object["name"], err = json.Marshal(a.Name)
	if err != nil {
		return nil, fmt.Errorf("error marshaling 'name': %w", err)
	}

	for fieldName, field := range a.AdditionalProperties {
		object[fieldName], err = json.Marshal(field)
		if err != nil {
			return nil, fmt.Errorf("error marshaling '%s': %w", fieldName, err)
		}
	}
	return json.Marshal(object)
}

// Getter for additional properties for GetProjectDbMetadataResponse_Databases_Item. Returns the specified
// element and whether it was found
func (a GetProjectDbMetadataResponse_Databases_Item) Get(fieldName string) (value interface{}, found bool) {
	if a.AdditionalProperties != nil {
		value, found = a.AdditionalProperties[fieldName]
	}
	return
}

// Setter for additional properties for GetProjectDbMetadataResponse_Databases_Item
func (a *GetProjectDbMetadataResponse_Databases_Item) Set(fieldName string, value interface{}) {
	if a.AdditionalProperties == nil {
		a.AdditionalProperties = make(map[string]interface{})
	}
	a.AdditionalProperties[fieldName] = value
}

// Override default JSON handling for GetProjectDbMetadataResponse_Databases_Item to handle AdditionalProperties
func (a *GetProjectDbMetadataResponse_Databases_Item) UnmarshalJSON(b []byte) error {
	object := make(map[string]json.RawMessage)
	err := json.Unmarshal(b, &object)
	if err != nil {
		return err
	}

	if raw, found := object["name"]; found {
		err = json.Unmarshal(raw, &a.Name)
		if err != nil {
			return fmt.Errorf("error reading 'name': %w", err)
		}
		delete(object, "name")
	}

	if raw, found := object["schemas"]; found {
		err = json.Unmarshal(raw, &a.Schemas)
		if err != nil {
			return fmt.Errorf("error reading 'schemas': %w", err)
		}
		delete(object, "schemas")
	}

	if len(object) != 0 {
		a.AdditionalProperties = make(map[string]interface{})
		for fieldName, fieldBuf := range object {
			var fieldVal interface{}
			err := json.Unmarshal(fieldBuf, &fieldVal)
			if err != nil {
				return fmt.Errorf("error unmarshaling field %s: %w", fieldName, err)
			}
			a.AdditionalProperties[fieldName] = fieldVal
		}
	}
	return nil
}

// Override default JSON handling for GetProjectDbMetadataResponse_Databases_Item to handle AdditionalProperties
func (a GetProjectDbMetadataResponse_Databases_Item) MarshalJSON() ([]byte, error) {
	var err error
	object := make(map[string]json.RawMessage)

	object["name"], err = json.Marshal(a.Name)
	if err != nil {
		return nil, fmt.Errorf("error marshaling 'name': %w", err)
	}

	object["schemas"], err = json.Marshal(a.Schemas)
	if err != nil {
		return nil, fmt.Errorf("error marshaling 'schemas': %w", err)
	}

	for fieldName, field := range a.AdditionalProperties {
		object[fieldName], err = json.Marshal(field)
		if err != nil {
			return nil, fmt.Errorf("error marshaling '%s': %w", fieldName, err)
		}
	}
	return json.Marshal(object)
}

// AsAnalyticsResponseError0 returns the union data inside the AnalyticsResponse_Error as a AnalyticsResponseError0
func (t AnalyticsResponse_Error) AsAnalyticsResponseError0() (AnalyticsResponseError0, error) {
	var body AnalyticsResponseError0
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromAnalyticsResponseError0 overwrites any union data inside the AnalyticsResponse_Error as the provided AnalyticsResponseError0
func (t *AnalyticsResponse_Error) FromAnalyticsResponseError0(v AnalyticsResponseError0) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeAnalyticsResponseError0 performs a merge with any union data inside the AnalyticsResponse_Error, using the provided AnalyticsResponseError0
func (t *AnalyticsResponse_Error) MergeAnalyticsResponseError0(v AnalyticsResponseError0) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsAnalyticsResponseError1 returns the union data inside the AnalyticsResponse_Error as a AnalyticsResponseError1
func (t AnalyticsResponse_Error) AsAnalyticsResponseError1() (AnalyticsResponseError1, error) {
	var body AnalyticsResponseError1
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromAnalyticsResponseError1 overwrites any union data inside the AnalyticsResponse_Error as the provided AnalyticsResponseError1
func (t *AnalyticsResponse_Error) FromAnalyticsResponseError1(v AnalyticsResponseError1) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeAnalyticsResponseError1 performs a merge with any union data inside the AnalyticsResponse_Error, using the provided AnalyticsResponseError1
func (t *AnalyticsResponse_Error) MergeAnalyticsResponseError1(v AnalyticsResponseError1) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t AnalyticsResponse_Error) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *AnalyticsResponse_Error) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}

// AsApplyProjectAddonBodyAddonVariant0 returns the union data inside the ApplyProjectAddonBody_AddonVariant as a ApplyProjectAddonBodyAddonVariant0
func (t ApplyProjectAddonBody_AddonVariant) AsApplyProjectAddonBodyAddonVariant0() (ApplyProjectAddonBodyAddonVariant0, error) {
	var body ApplyProjectAddonBodyAddonVariant0
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromApplyProjectAddonBodyAddonVariant0 overwrites any union data inside the ApplyProjectAddonBody_AddonVariant as the provided ApplyProjectAddonBodyAddonVariant0
func (t *ApplyProjectAddonBody_AddonVariant) FromApplyProjectAddonBodyAddonVariant0(v ApplyProjectAddonBodyAddonVariant0) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeApplyProjectAddonBodyAddonVariant0 performs a merge with any union data inside the ApplyProjectAddonBody_AddonVariant, using the provided ApplyProjectAddonBodyAddonVariant0
func (t *ApplyProjectAddonBody_AddonVariant) MergeApplyProjectAddonBodyAddonVariant0(v ApplyProjectAddonBodyAddonVariant0) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsApplyProjectAddonBodyAddonVariant1 returns the union data inside the ApplyProjectAddonBody_AddonVariant as a ApplyProjectAddonBodyAddonVariant1
func (t ApplyProjectAddonBody_AddonVariant) AsApplyProjectAddonBodyAddonVariant1() (ApplyProjectAddonBodyAddonVariant1, error) {
	var body ApplyProjectAddonBodyAddonVariant1
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromApplyProjectAddonBodyAddonVariant1 overwrites any union data inside the ApplyProjectAddonBody_AddonVariant as the provided ApplyProjectAddonBodyAddonVariant1
func (t *ApplyProjectAddonBody_AddonVariant) FromApplyProjectAddonBodyAddonVariant1(v ApplyProjectAddonBodyAddonVariant1) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeApplyProjectAddonBodyAddonVariant1 performs a merge with any union data inside the ApplyProjectAddonBody_AddonVariant, using the provided ApplyProjectAddonBodyAddonVariant1
func (t *ApplyProjectAddonBody_AddonVariant) MergeApplyProjectAddonBodyAddonVariant1(v ApplyProjectAddonBodyAddonVariant1) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsApplyProjectAddonBodyAddonVariant2 returns the union data inside the ApplyProjectAddonBody_AddonVariant as a ApplyProjectAddonBodyAddonVariant2
func (t ApplyProjectAddonBody_AddonVariant) AsApplyProjectAddonBodyAddonVariant2() (ApplyProjectAddonBodyAddonVariant2, error) {
	var body ApplyProjectAddonBodyAddonVariant2
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromApplyProjectAddonBodyAddonVariant2 overwrites any union data inside the ApplyProjectAddonBody_AddonVariant as the provided ApplyProjectAddonBodyAddonVariant2
func (t *ApplyProjectAddonBody_AddonVariant) FromApplyProjectAddonBodyAddonVariant2(v ApplyProjectAddonBodyAddonVariant2) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeApplyProjectAddonBodyAddonVariant2 performs a merge with any union data inside the ApplyProjectAddonBody_AddonVariant, using the provided ApplyProjectAddonBodyAddonVariant2
func (t *ApplyProjectAddonBody_AddonVariant) MergeApplyProjectAddonBodyAddonVariant2(v ApplyProjectAddonBodyAddonVariant2) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsApplyProjectAddonBodyAddonVariant3 returns the union data inside the ApplyProjectAddonBody_AddonVariant as a ApplyProjectAddonBodyAddonVariant3
func (t ApplyProjectAddonBody_AddonVariant) AsApplyProjectAddonBodyAddonVariant3() (ApplyProjectAddonBodyAddonVariant3, error) {
	var body ApplyProjectAddonBodyAddonVariant3
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromApplyProjectAddonBodyAddonVariant3 overwrites any union data inside the ApplyProjectAddonBody_AddonVariant as the provided ApplyProjectAddonBodyAddonVariant3
func (t *ApplyProjectAddonBody_AddonVariant) FromApplyProjectAddonBodyAddonVariant3(v ApplyProjectAddonBodyAddonVariant3) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeApplyProjectAddonBodyAddonVariant3 performs a merge with any union data inside the ApplyProjectAddonBody_AddonVariant, using the provided ApplyProjectAddonBodyAddonVariant3
func (t *ApplyProjectAddonBody_AddonVariant) MergeApplyProjectAddonBodyAddonVariant3(v ApplyProjectAddonBodyAddonVariant3) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t ApplyProjectAddonBody_AddonVariant) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *ApplyProjectAddonBody_AddonVariant) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}

// AsListProjectAddonsResponseAvailableAddonsVariantsId0 returns the union data inside the ListProjectAddonsResponse_AvailableAddons_Variants_Id as a ListProjectAddonsResponseAvailableAddonsVariantsId0
func (t ListProjectAddonsResponse_AvailableAddons_Variants_Id) AsListProjectAddonsResponseAvailableAddonsVariantsId0() (ListProjectAddonsResponseAvailableAddonsVariantsId0, error) {
	var body ListProjectAddonsResponseAvailableAddonsVariantsId0
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromListProjectAddonsResponseAvailableAddonsVariantsId0 overwrites any union data inside the ListProjectAddonsResponse_AvailableAddons_Variants_Id as the provided ListProjectAddonsResponseAvailableAddonsVariantsId0
func (t *ListProjectAddonsResponse_AvailableAddons_Variants_Id) FromListProjectAddonsResponseAvailableAddonsVariantsId0(v ListProjectAddonsResponseAvailableAddonsVariantsId0) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeListProjectAddonsResponseAvailableAddonsVariantsId0 performs a merge with any union data inside the ListProjectAddonsResponse_AvailableAddons_Variants_Id, using the provided ListProjectAddonsResponseAvailableAddonsVariantsId0
func (t *ListProjectAddonsResponse_AvailableAddons_Variants_Id) MergeListProjectAddonsResponseAvailableAddonsVariantsId0(v ListProjectAddonsResponseAvailableAddonsVariantsId0) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsListProjectAddonsResponseAvailableAddonsVariantsId1 returns the union data inside the ListProjectAddonsResponse_AvailableAddons_Variants_Id as a ListProjectAddonsResponseAvailableAddonsVariantsId1
func (t ListProjectAddonsResponse_AvailableAddons_Variants_Id) AsListProjectAddonsResponseAvailableAddonsVariantsId1() (ListProjectAddonsResponseAvailableAddonsVariantsId1, error) {
	var body ListProjectAddonsResponseAvailableAddonsVariantsId1
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromListProjectAddonsResponseAvailableAddonsVariantsId1 overwrites any union data inside the ListProjectAddonsResponse_AvailableAddons_Variants_Id as the provided ListProjectAddonsResponseAvailableAddonsVariantsId1
func (t *ListProjectAddonsResponse_AvailableAddons_Variants_Id) FromListProjectAddonsResponseAvailableAddonsVariantsId1(v ListProjectAddonsResponseAvailableAddonsVariantsId1) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeListProjectAddonsResponseAvailableAddonsVariantsId1 performs a merge with any union data inside the ListProjectAddonsResponse_AvailableAddons_Variants_Id, using the provided ListProjectAddonsResponseAvailableAddonsVariantsId1
func (t *ListProjectAddonsResponse_AvailableAddons_Variants_Id) MergeListProjectAddonsResponseAvailableAddonsVariantsId1(v ListProjectAddonsResponseAvailableAddonsVariantsId1) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsListProjectAddonsResponseAvailableAddonsVariantsId2 returns the union data inside the ListProjectAddonsResponse_AvailableAddons_Variants_Id as a ListProjectAddonsResponseAvailableAddonsVariantsId2
func (t ListProjectAddonsResponse_AvailableAddons_Variants_Id) AsListProjectAddonsResponseAvailableAddonsVariantsId2() (ListProjectAddonsResponseAvailableAddonsVariantsId2, error) {
	var body ListProjectAddonsResponseAvailableAddonsVariantsId2
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromListProjectAddonsResponseAvailableAddonsVariantsId2 overwrites any union data inside the ListProjectAddonsResponse_AvailableAddons_Variants_Id as the provided ListProjectAddonsResponseAvailableAddonsVariantsId2
func (t *ListProjectAddonsResponse_AvailableAddons_Variants_Id) FromListProjectAddonsResponseAvailableAddonsVariantsId2(v ListProjectAddonsResponseAvailableAddonsVariantsId2) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeListProjectAddonsResponseAvailableAddonsVariantsId2 performs a merge with any union data inside the ListProjectAddonsResponse_AvailableAddons_Variants_Id, using the provided ListProjectAddonsResponseAvailableAddonsVariantsId2
func (t *ListProjectAddonsResponse_AvailableAddons_Variants_Id) MergeListProjectAddonsResponseAvailableAddonsVariantsId2(v ListProjectAddonsResponseAvailableAddonsVariantsId2) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsListProjectAddonsResponseAvailableAddonsVariantsId3 returns the union data inside the ListProjectAddonsResponse_AvailableAddons_Variants_Id as a ListProjectAddonsResponseAvailableAddonsVariantsId3
func (t ListProjectAddonsResponse_AvailableAddons_Variants_Id) AsListProjectAddonsResponseAvailableAddonsVariantsId3() (ListProjectAddonsResponseAvailableAddonsVariantsId3, error) {
	var body ListProjectAddonsResponseAvailableAddonsVariantsId3
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromListProjectAddonsResponseAvailableAddonsVariantsId3 overwrites any union data inside the ListProjectAddonsResponse_AvailableAddons_Variants_Id as the provided ListProjectAddonsResponseAvailableAddonsVariantsId3
func (t *ListProjectAddonsResponse_AvailableAddons_Variants_Id) FromListProjectAddonsResponseAvailableAddonsVariantsId3(v ListProjectAddonsResponseAvailableAddonsVariantsId3) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeListProjectAddonsResponseAvailableAddonsVariantsId3 performs a merge with any union data inside the ListProjectAddonsResponse_AvailableAddons_Variants_Id, using the provided ListProjectAddonsResponseAvailableAddonsVariantsId3
func (t *ListProjectAddonsResponse_AvailableAddons_Variants_Id) MergeListProjectAddonsResponseAvailableAddonsVariantsId3(v ListProjectAddonsResponseAvailableAddonsVariantsId3) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsListProjectAddonsResponseAvailableAddonsVariantsId4 returns the union data inside the ListProjectAddonsResponse_AvailableAddons_Variants_Id as a ListProjectAddonsResponseAvailableAddonsVariantsId4
func (t ListProjectAddonsResponse_AvailableAddons_Variants_Id) AsListProjectAddonsResponseAvailableAddonsVariantsId4() (ListProjectAddonsResponseAvailableAddonsVariantsId4, error) {
	var body ListProjectAddonsResponseAvailableAddonsVariantsId4
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromListProjectAddonsResponseAvailableAddonsVariantsId4 overwrites any union data inside the ListProjectAddonsResponse_AvailableAddons_Variants_Id as the provided ListProjectAddonsResponseAvailableAddonsVariantsId4
func (t *ListProjectAddonsResponse_AvailableAddons_Variants_Id) FromListProjectAddonsResponseAvailableAddonsVariantsId4(v ListProjectAddonsResponseAvailableAddonsVariantsId4) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeListProjectAddonsResponseAvailableAddonsVariantsId4 performs a merge with any union data inside the ListProjectAddonsResponse_AvailableAddons_Variants_Id, using the provided ListProjectAddonsResponseAvailableAddonsVariantsId4
func (t *ListProjectAddonsResponse_AvailableAddons_Variants_Id) MergeListProjectAddonsResponseAvailableAddonsVariantsId4(v ListProjectAddonsResponseAvailableAddonsVariantsId4) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsListProjectAddonsResponseAvailableAddonsVariantsId5 returns the union data inside the ListProjectAddonsResponse_AvailableAddons_Variants_Id as a ListProjectAddonsResponseAvailableAddonsVariantsId5
func (t ListProjectAddonsResponse_AvailableAddons_Variants_Id) AsListProjectAddonsResponseAvailableAddonsVariantsId5() (ListProjectAddonsResponseAvailableAddonsVariantsId5, error) {
	var body ListProjectAddonsResponseAvailableAddonsVariantsId5
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromListProjectAddonsResponseAvailableAddonsVariantsId5 overwrites any union data inside the ListProjectAddonsResponse_AvailableAddons_Variants_Id as the provided ListProjectAddonsResponseAvailableAddonsVariantsId5
func (t *ListProjectAddonsResponse_AvailableAddons_Variants_Id) FromListProjectAddonsResponseAvailableAddonsVariantsId5(v ListProjectAddonsResponseAvailableAddonsVariantsId5) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeListProjectAddonsResponseAvailableAddonsVariantsId5 performs a merge with any union data inside the ListProjectAddonsResponse_AvailableAddons_Variants_Id, using the provided ListProjectAddonsResponseAvailableAddonsVariantsId5
func (t *ListProjectAddonsResponse_AvailableAddons_Variants_Id) MergeListProjectAddonsResponseAvailableAddonsVariantsId5(v ListProjectAddonsResponseAvailableAddonsVariantsId5) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsListProjectAddonsResponseAvailableAddonsVariantsId6 returns the union data inside the ListProjectAddonsResponse_AvailableAddons_Variants_Id as a ListProjectAddonsResponseAvailableAddonsVariantsId6
func (t ListProjectAddonsResponse_AvailableAddons_Variants_Id) AsListProjectAddonsResponseAvailableAddonsVariantsId6() (ListProjectAddonsResponseAvailableAddonsVariantsId6, error) {
	var body ListProjectAddonsResponseAvailableAddonsVariantsId6
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromListProjectAddonsResponseAvailableAddonsVariantsId6 overwrites any union data inside the ListProjectAddonsResponse_AvailableAddons_Variants_Id as the provided ListProjectAddonsResponseAvailableAddonsVariantsId6
func (t *ListProjectAddonsResponse_AvailableAddons_Variants_Id) FromListProjectAddonsResponseAvailableAddonsVariantsId6(v ListProjectAddonsResponseAvailableAddonsVariantsId6) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeListProjectAddonsResponseAvailableAddonsVariantsId6 performs a merge with any union data inside the ListProjectAddonsResponse_AvailableAddons_Variants_Id, using the provided ListProjectAddonsResponseAvailableAddonsVariantsId6
func (t *ListProjectAddonsResponse_AvailableAddons_Variants_Id) MergeListProjectAddonsResponseAvailableAddonsVariantsId6(v ListProjectAddonsResponseAvailableAddonsVariantsId6) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t ListProjectAddonsResponse_AvailableAddons_Variants_Id) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *ListProjectAddonsResponse_AvailableAddons_Variants_Id) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}

// AsListProjectAddonsResponseSelectedAddonsVariantId0 returns the union data inside the ListProjectAddonsResponse_SelectedAddons_Variant_Id as a ListProjectAddonsResponseSelectedAddonsVariantId0
func (t ListProjectAddonsResponse_SelectedAddons_Variant_Id) AsListProjectAddonsResponseSelectedAddonsVariantId0() (ListProjectAddonsResponseSelectedAddonsVariantId0, error) {
	var body ListProjectAddonsResponseSelectedAddonsVariantId0
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromListProjectAddonsResponseSelectedAddonsVariantId0 overwrites any union data inside the ListProjectAddonsResponse_SelectedAddons_Variant_Id as the provided ListProjectAddonsResponseSelectedAddonsVariantId0
func (t *ListProjectAddonsResponse_SelectedAddons_Variant_Id) FromListProjectAddonsResponseSelectedAddonsVariantId0(v ListProjectAddonsResponseSelectedAddonsVariantId0) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeListProjectAddonsResponseSelectedAddonsVariantId0 performs a merge with any union data inside the ListProjectAddonsResponse_SelectedAddons_Variant_Id, using the provided ListProjectAddonsResponseSelectedAddonsVariantId0
func (t *ListProjectAddonsResponse_SelectedAddons_Variant_Id) MergeListProjectAddonsResponseSelectedAddonsVariantId0(v ListProjectAddonsResponseSelectedAddonsVariantId0) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsListProjectAddonsResponseSelectedAddonsVariantId1 returns the union data inside the ListProjectAddonsResponse_SelectedAddons_Variant_Id as a ListProjectAddonsResponseSelectedAddonsVariantId1
func (t ListProjectAddonsResponse_SelectedAddons_Variant_Id) AsListProjectAddonsResponseSelectedAddonsVariantId1() (ListProjectAddonsResponseSelectedAddonsVariantId1, error) {
	var body ListProjectAddonsResponseSelectedAddonsVariantId1
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromListProjectAddonsResponseSelectedAddonsVariantId1 overwrites any union data inside the ListProjectAddonsResponse_SelectedAddons_Variant_Id as the provided ListProjectAddonsResponseSelectedAddonsVariantId1
func (t *ListProjectAddonsResponse_SelectedAddons_Variant_Id) FromListProjectAddonsResponseSelectedAddonsVariantId1(v ListProjectAddonsResponseSelectedAddonsVariantId1) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeListProjectAddonsResponseSelectedAddonsVariantId1 performs a merge with any union data inside the ListProjectAddonsResponse_SelectedAddons_Variant_Id, using the provided ListProjectAddonsResponseSelectedAddonsVariantId1
func (t *ListProjectAddonsResponse_SelectedAddons_Variant_Id) MergeListProjectAddonsResponseSelectedAddonsVariantId1(v ListProjectAddonsResponseSelectedAddonsVariantId1) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsListProjectAddonsResponseSelectedAddonsVariantId2 returns the union data inside the ListProjectAddonsResponse_SelectedAddons_Variant_Id as a ListProjectAddonsResponseSelectedAddonsVariantId2
func (t ListProjectAddonsResponse_SelectedAddons_Variant_Id) AsListProjectAddonsResponseSelectedAddonsVariantId2() (ListProjectAddonsResponseSelectedAddonsVariantId2, error) {
	var body ListProjectAddonsResponseSelectedAddonsVariantId2
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromListProjectAddonsResponseSelectedAddonsVariantId2 overwrites any union data inside the ListProjectAddonsResponse_SelectedAddons_Variant_Id as the provided ListProjectAddonsResponseSelectedAddonsVariantId2
func (t *ListProjectAddonsResponse_SelectedAddons_Variant_Id) FromListProjectAddonsResponseSelectedAddonsVariantId2(v ListProjectAddonsResponseSelectedAddonsVariantId2) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeListProjectAddonsResponseSelectedAddonsVariantId2 performs a merge with any union data inside the ListProjectAddonsResponse_SelectedAddons_Variant_Id, using the provided ListProjectAddonsResponseSelectedAddonsVariantId2
func (t *ListProjectAddonsResponse_SelectedAddons_Variant_Id) MergeListProjectAddonsResponseSelectedAddonsVariantId2(v ListProjectAddonsResponseSelectedAddonsVariantId2) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsListProjectAddonsResponseSelectedAddonsVariantId3 returns the union data inside the ListProjectAddonsResponse_SelectedAddons_Variant_Id as a ListProjectAddonsResponseSelectedAddonsVariantId3
func (t ListProjectAddonsResponse_SelectedAddons_Variant_Id) AsListProjectAddonsResponseSelectedAddonsVariantId3() (ListProjectAddonsResponseSelectedAddonsVariantId3, error) {
	var body ListProjectAddonsResponseSelectedAddonsVariantId3
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromListProjectAddonsResponseSelectedAddonsVariantId3 overwrites any union data inside the ListProjectAddonsResponse_SelectedAddons_Variant_Id as the provided ListProjectAddonsResponseSelectedAddonsVariantId3
func (t *ListProjectAddonsResponse_SelectedAddons_Variant_Id) FromListProjectAddonsResponseSelectedAddonsVariantId3(v ListProjectAddonsResponseSelectedAddonsVariantId3) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeListProjectAddonsResponseSelectedAddonsVariantId3 performs a merge with any union data inside the ListProjectAddonsResponse_SelectedAddons_Variant_Id, using the provided ListProjectAddonsResponseSelectedAddonsVariantId3
func (t *ListProjectAddonsResponse_SelectedAddons_Variant_Id) MergeListProjectAddonsResponseSelectedAddonsVariantId3(v ListProjectAddonsResponseSelectedAddonsVariantId3) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsListProjectAddonsResponseSelectedAddonsVariantId4 returns the union data inside the ListProjectAddonsResponse_SelectedAddons_Variant_Id as a ListProjectAddonsResponseSelectedAddonsVariantId4
func (t ListProjectAddonsResponse_SelectedAddons_Variant_Id) AsListProjectAddonsResponseSelectedAddonsVariantId4() (ListProjectAddonsResponseSelectedAddonsVariantId4, error) {
	var body ListProjectAddonsResponseSelectedAddonsVariantId4
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromListProjectAddonsResponseSelectedAddonsVariantId4 overwrites any union data inside the ListProjectAddonsResponse_SelectedAddons_Variant_Id as the provided ListProjectAddonsResponseSelectedAddonsVariantId4
func (t *ListProjectAddonsResponse_SelectedAddons_Variant_Id) FromListProjectAddonsResponseSelectedAddonsVariantId4(v ListProjectAddonsResponseSelectedAddonsVariantId4) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeListProjectAddonsResponseSelectedAddonsVariantId4 performs a merge with any union data inside the ListProjectAddonsResponse_SelectedAddons_Variant_Id, using the provided ListProjectAddonsResponseSelectedAddonsVariantId4
func (t *ListProjectAddonsResponse_SelectedAddons_Variant_Id) MergeListProjectAddonsResponseSelectedAddonsVariantId4(v ListProjectAddonsResponseSelectedAddonsVariantId4) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsListProjectAddonsResponseSelectedAddonsVariantId5 returns the union data inside the ListProjectAddonsResponse_SelectedAddons_Variant_Id as a ListProjectAddonsResponseSelectedAddonsVariantId5
func (t ListProjectAddonsResponse_SelectedAddons_Variant_Id) AsListProjectAddonsResponseSelectedAddonsVariantId5() (ListProjectAddonsResponseSelectedAddonsVariantId5, error) {
	var body ListProjectAddonsResponseSelectedAddonsVariantId5
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromListProjectAddonsResponseSelectedAddonsVariantId5 overwrites any union data inside the ListProjectAddonsResponse_SelectedAddons_Variant_Id as the provided ListProjectAddonsResponseSelectedAddonsVariantId5
func (t *ListProjectAddonsResponse_SelectedAddons_Variant_Id) FromListProjectAddonsResponseSelectedAddonsVariantId5(v ListProjectAddonsResponseSelectedAddonsVariantId5) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeListProjectAddonsResponseSelectedAddonsVariantId5 performs a merge with any union data inside the ListProjectAddonsResponse_SelectedAddons_Variant_Id, using the provided ListProjectAddonsResponseSelectedAddonsVariantId5
func (t *ListProjectAddonsResponse_SelectedAddons_Variant_Id) MergeListProjectAddonsResponseSelectedAddonsVariantId5(v ListProjectAddonsResponseSelectedAddonsVariantId5) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsListProjectAddonsResponseSelectedAddonsVariantId6 returns the union data inside the ListProjectAddonsResponse_SelectedAddons_Variant_Id as a ListProjectAddonsResponseSelectedAddonsVariantId6
func (t ListProjectAddonsResponse_SelectedAddons_Variant_Id) AsListProjectAddonsResponseSelectedAddonsVariantId6() (ListProjectAddonsResponseSelectedAddonsVariantId6, error) {
	var body ListProjectAddonsResponseSelectedAddonsVariantId6
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromListProjectAddonsResponseSelectedAddonsVariantId6 overwrites any union data inside the ListProjectAddonsResponse_SelectedAddons_Variant_Id as the provided ListProjectAddonsResponseSelectedAddonsVariantId6
func (t *ListProjectAddonsResponse_SelectedAddons_Variant_Id) FromListProjectAddonsResponseSelectedAddonsVariantId6(v ListProjectAddonsResponseSelectedAddonsVariantId6) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeListProjectAddonsResponseSelectedAddonsVariantId6 performs a merge with any union data inside the ListProjectAddonsResponse_SelectedAddons_Variant_Id, using the provided ListProjectAddonsResponseSelectedAddonsVariantId6
func (t *ListProjectAddonsResponse_SelectedAddons_Variant_Id) MergeListProjectAddonsResponseSelectedAddonsVariantId6(v ListProjectAddonsResponseSelectedAddonsVariantId6) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t ListProjectAddonsResponse_SelectedAddons_Variant_Id) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *ListProjectAddonsResponse_SelectedAddons_Variant_Id) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}

// AsV1ServiceHealthResponseInfo0 returns the union data inside the V1ServiceHealthResponse_Info as a V1ServiceHealthResponseInfo0
func (t V1ServiceHealthResponse_Info) AsV1ServiceHealthResponseInfo0() (V1ServiceHealthResponseInfo0, error) {
	var body V1ServiceHealthResponseInfo0
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromV1ServiceHealthResponseInfo0 overwrites any union data inside the V1ServiceHealthResponse_Info as the provided V1ServiceHealthResponseInfo0
func (t *V1ServiceHealthResponse_Info) FromV1ServiceHealthResponseInfo0(v V1ServiceHealthResponseInfo0) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeV1ServiceHealthResponseInfo0 performs a merge with any union data inside the V1ServiceHealthResponse_Info, using the provided V1ServiceHealthResponseInfo0
func (t *V1ServiceHealthResponse_Info) MergeV1ServiceHealthResponseInfo0(v V1ServiceHealthResponseInfo0) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsV1ServiceHealthResponseInfo1 returns the union data inside the V1ServiceHealthResponse_Info as a V1ServiceHealthResponseInfo1
func (t V1ServiceHealthResponse_Info) AsV1ServiceHealthResponseInfo1() (V1ServiceHealthResponseInfo1, error) {
	var body V1ServiceHealthResponseInfo1
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromV1ServiceHealthResponseInfo1 overwrites any union data inside the V1ServiceHealthResponse_Info as the provided V1ServiceHealthResponseInfo1
func (t *V1ServiceHealthResponse_Info) FromV1ServiceHealthResponseInfo1(v V1ServiceHealthResponseInfo1) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeV1ServiceHealthResponseInfo1 performs a merge with any union data inside the V1ServiceHealthResponse_Info, using the provided V1ServiceHealthResponseInfo1
func (t *V1ServiceHealthResponse_Info) MergeV1ServiceHealthResponseInfo1(v V1ServiceHealthResponseInfo1) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t V1ServiceHealthResponse_Info) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *V1ServiceHealthResponse_Info) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}
