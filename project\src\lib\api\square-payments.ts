import { supabase } from '../supabase/client';
import type { ProcessPaymentParams, PaymentResult } from '../../types/forms';

export async function processSquarePayment(params: ProcessPaymentParams): Promise<PaymentResult> {
  try {
    // Call Supabase Edge Function to process payment
    const { data, error } = await supabase.functions.invoke('process-square-payment', {
      body: {
        sourceId: params.sourceId,
        amount: params.amount,
        currency: params.currency || 'USD',
        buyerVerificationToken: params.buyerVerificationToken,
        orderId: params.orderId,
        customerEmail: params.customerEmail,
        customerName: params.customerName,
        locationId: params.locationId,
        idempotencyKey: params.idempotencyKey || crypto.randomUUID(),
      },
    });

    if (error) {
      return {
        success: false,
        error: error.message || 'Failed to process payment',
      };
    }

    return {
      success: true,
      paymentId: data.paymentId,
      orderId: data.orderId,
      status: data.status,
    };
  } catch (error: unknown) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}

export async function createSquarePaymentLink(
  amount: number,
  description: string,
  orderId?: string,
  customerEmail?: string
): Promise<unknown> {
  try {
    const { data, error } = await supabase.functions.invoke('create-payment-link', {
      body: {
        amount,
        description,
        orderId,
        customerEmail,
        currency: 'USD',
      },
    });

    if (error) {
      throw error;
    }

    return data;
  } catch (error: unknown) {
    throw new Error(error instanceof Error ? error.message : 'Failed to create payment link');
  }
}

export async function getPaymentStatus(paymentId: string): Promise<unknown> {
  try {
    const { data, error } = await supabase.functions.invoke('check-payment-status', {
      body: { paymentId },
    });

    if (error) {
      throw error;
    }

    return data;
  } catch (error: unknown) {
    throw new Error(error instanceof Error ? error.message : 'Failed to check payment status');
  }
}

export async function refundPayment(paymentId: string, amount?: number, reason?: string): Promise<unknown> {
  try {
    const { data, error } = await supabase.functions.invoke('refund-payment', {
      body: {
        paymentId,
        amount,
        reason,
      },
    });

    if (error) {
      throw error;
    }

    return data;
  } catch (error: unknown) {
    throw new Error(error instanceof Error ? error.message : 'Failed to process refund');
  }
}