import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Debug logging removed for production

// Check if environment variables are properly configured
export const isSupabaseConfigured = Boolean(
  supabaseUrl?.startsWith('https://') && 
  supabaseAnonKey?.length > 0
);

if (!isSupabaseConfigured) {
  console.error('Supabase configuration is missing or invalid. Please check your environment variables.');
}

// Create and export the Supabase client
export const supabase = isSupabaseConfigured 
  ? createClient(supabaseUrl, supabaseAnonKey)
  : null;

// Helper function to check if client is available
export function getSupabaseClient() {
  if (!supabase) {
    throw new Error(
      'Supabase client is not configured. Please check your environment variables and ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set correctly.'
    );
  }
  return supabase;
}

// Helper function to check network connectivity with Supabase
export async function checkSupabaseConnection() {
  if (!isSupabaseConfigured) {
    throw new Error('Supabase is not configured. Please check your environment variables.');
  }
  
  try {
    // Use a simple query to test the connection instead of direct fetch
    const { error } = await getSupabaseClient()
      .from('profiles')
      .select('id')
      .limit(1);
      
    if (error) {
      throw new Error('Could not connect to Supabase: ' + error.message);
    }
    
    return true;
  } catch (_error) {
    throw new Error('Failed to connect to Supabase. Please check your internet connection and configuration.');
  }
}