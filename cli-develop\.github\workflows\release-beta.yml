name: Release (Beta)

on:
  push:
    branches:
      - develop
  workflow_dispatch:

permissions:
  contents: write

jobs:
  release:
    name: semantic-release
    runs-on: ubuntu-latest
    permissions:
      contents: write
    outputs:
      new-release-published: ${{ steps.semantic-release.outputs.new_release_published }}
      new-release-version: ${{ steps.semantic-release.outputs.new_release_version }}
      new-release-channel: ${{ steps.semantic-release.outputs.new_release_channel }}
    steps:
      - uses: actions/checkout@v4
      - id: semantic-release
        uses: cycjimmy/semantic-release-action@v4
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  goreleaser:
    name: Go<PERSON><PERSON>aser
    needs:
      - release
    if: needs.release.outputs.new-release-published == 'true'
    permissions:
      contents: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          cache: true

      - uses: goreleaser/goreleaser-action@v6
        with:
          distribution: goreleaser
          version: ~> v2
          args: release --clean
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SENTRY_DSN: ${{ secrets.SENTRY_DSN }}

      - run: gh release edit v${{ needs.release.outputs.new-release-version }} --draft=false --prerelease
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  commit:
    name: Publish Brew and Scoop
    needs:
      - release
      - goreleaser
    if: needs.release.outputs.new-release-published == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          cache: true
      - run: go run tools/publish/main.go --beta "${{ needs.release.outputs.new-release-version }}"
        env:
          GITHUB_TOKEN: ${{ secrets.GH_PAT }}

  publish:
    name: Publish NPM
    needs:
      - release
      - goreleaser
    if: needs.release.outputs.new-release-published == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: "16.x"
          registry-url: "https://registry.npmjs.org"
      - run: npm --git-tag-version=false version ${{ needs.release.outputs.new-release-version }}
      - run: npm publish --tag ${{ needs.release.outputs.new-release-channel }}
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
