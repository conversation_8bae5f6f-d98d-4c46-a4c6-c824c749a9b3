# Square API Configuration
SQUARE_ACCESS_TOKEN=YOUR_SQUARE_ACCESS_TOKEN
SQUARE_LOCATION_ID=YOUR_SQUARE_LOCATION_ID
SQUARE_ENVIRONMENT=sandbox

# Supabase Configuration
SUPABASE_URL=https://auyztjlijlbyopxrnxqz.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF1eXp0amxpamxieW9weHJueHF6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzYwNDAxMDksImV4cCI6MjA1MTYxNjEwOX0._0PvItjCjDHHxwOZD5vAPfp2l2J99v8VBEHK7UDcIF8Q
SUPABASE_SERVICE_KEY=YOUR_SUPABASE_SERVICE_KEY_FOR_BACKEND_OPERATIONS

# Server Configuration
PORT=3001
CLIENT_URL=http://localhost:5173
SUPPORT_EMAIL=<EMAIL>

# For production, update CLIENT_URL to your actual domain
# CLIENT_URL=https://your-domain.com 