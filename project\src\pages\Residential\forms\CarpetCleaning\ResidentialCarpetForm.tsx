import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { AlertCircle } from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { ProgressBar } from '../../../../components/forms/ProgressBar';
import { ServiceTypeSelector } from './components/ServiceTypeSelector';
import { PropertyDetails } from './components/PropertyDetails';
import { CarpetDetails } from './components/CarpetDetails';
import { StainTreatment } from './components/StainTreatment';
import { ServiceScheduling } from './components/ServiceScheduling';
import { ContactInfo } from './components/ContactInfo';
import { FormLayout } from './components/FormLayout';
import { SlideTransition } from '../../../../components/animations/SlideTransition';
import { useFormValidation } from '../../../../lib/hooks/useFormValidation';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { submitBookingForm } from '../../../../lib/api/bookingForms';
import { saveFormData } from '../../../../lib/utils/formStorage';
import { steps, initialFormData } from './types';
import type { FormData } from './types';
import { PaymentModal } from '../../../../components/payment/PaymentModal';

interface ResidentialCarpetFormProps {
  onBack: () => void;
}

export function ResidentialCarpetForm({ onBack }: ResidentialCarpetFormProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, loading, saveFormData } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<FormData>(initialFormData);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [estimatedPrice, setEstimatedPrice] = useState(0);
  const { validateStep } = useFormValidation();

  // Check for skip parameter to bypass first step
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const skipFirstStep = params.get('skip') === '1';
    
    // If skip parameter is present and we're on the first step, move to the second step
    if (skipFirstStep && currentStep === 0) {
      // Set a default service type based on the URL path
      const pathSegments = location.pathname.split('/');
      const serviceType = pathSegments[pathSegments.length - 1] || 'carpet';
      
      // Update form data with the service type
      setFormData(prev => ({
        ...prev,
        serviceType
      }));
      
      // Move to the second step
      setCurrentStep(1);
    }
  }, [location, currentStep]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      navigate('/auth/login', { 
        state: { from: location.pathname },
        replace: true 
      });
    }
  }, [user, loading, navigate, location]);

  // Calculate estimated price based on form data
  useEffect(() => {
    let basePrice = 0;
    
    // Base price calculation based on square footage
    if (formData.propertyDetails.squareFootage) {
      // $0.50 per square foot for carpet cleaning
      basePrice = formData.propertyDetails.squareFootage * 0.50;
    }
    
    // Minimum price based on rooms
    const roomsPrice = (formData.propertyDetails.bedrooms || 0) * 90;
    
    basePrice = Math.max(basePrice, roomsPrice, 180); // Minimum $180 for carpet cleaning
    
    // Adjustments based on carpet condition
    if (formData.carpetDetails.condition === 'poor') {
      basePrice *= 1.2; // 20% more for poor condition
    }
    
    // Adjustments based on traffic level
    if (formData.carpetDetails.trafficLevel === 'heavy') {
      basePrice *= 1.1; // 10% more for heavy traffic
    } else if (formData.carpetDetails.trafficLevel === 'extreme') {
      basePrice *= 1.15; // 15% more for extreme traffic
    }
    
    // Adjustments for stain treatment
    if (formData.stainTreatment.hasStains && formData.stainTreatment.stainTypes.length > 0) {
      basePrice += formData.stainTreatment.stainTypes.length * 15; // $15 per stain type
    }
    
    // Round to nearest dollar
    setEstimatedPrice(Math.round(basePrice));
  }, [formData]);

  const handleNext = async () => {
    const validation = validateStep(currentStep, formData);
    
    if (!validation.isValid) {
      setValidationError(validation.errors.join('\n'));
      return;
    }

    setValidationError(null);

    if (currentStep === steps.length - 1) {
      await handleSubmit();
    } else {
      setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
    }
  };

  const handlePrev = () => {
    if (currentStep === 0) {
      onBack();
    } else {
      setCurrentStep((prev) => Math.max(prev - 1, 0));
    }
  };

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      setValidationError(null);

      if (!user) {
        // Save form data and redirect to login/signup
        saveFormData(formData, 'residential-carpet');
        navigate('/auth/login', { 
          state: { from: location.pathname }
        });
        return;
      }

      // Show payment modal instead of submitting directly
      setShowPaymentModal(true);
    } catch (err) {
      console.error('Error submitting form:', err);
      setValidationError(
        err instanceof Error ? err.message : 'Failed to submit request'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePaymentComplete = async () => {
    try {
      setIsSubmitting(true);
      
      await submitBookingForm({
        ...formData,
        paymentStatus: 'paid',
        amount: estimatedPrice
      }, 'residential-carpet', user);
      
      navigate('/thank-you', { state: { formData, paymentStatus: 'paid' } });
    } catch (err) {
      console.error('Error submitting form:', err);
      setValidationError(
        err instanceof Error ? err.message : 'Failed to submit request'
      );
    } finally {
      setIsSubmitting(false);
      setShowPaymentModal(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <ServiceTypeSelector
            selected={formData.serviceType}
            onChange={(type) => setFormData({ ...formData, serviceType: type })}
          />
        );
      case 1:
        return (
          <PropertyDetails
            details={formData.propertyDetails}
            onChange={(details) => setFormData({ ...formData, propertyDetails: details })}
          />
        );
      case 2:
        return (
          <CarpetDetails
            details={formData.carpetDetails}
            onChange={(details) => setFormData({ ...formData, carpetDetails: details })}
          />
        );
      case 3:
        return (
          <StainTreatment
            treatment={formData.stainTreatment}
            onChange={(treatment) => setFormData({ ...formData, stainTreatment: treatment })}
          />
        );
      case 4:
        return (
          <ServiceScheduling
            schedule={formData.schedule}
            onChange={(schedule) => setFormData({ ...formData, schedule: schedule })}
          />
        );
      case 5:
        return (
          <ContactInfo
            contact={formData.contact}
            onChange={(contact) => setFormData({ ...formData, contact: contact })}
          />
        );
      default:
        return null;
    }
  };

  return (
    <FormLayout formData={formData} onBack={onBack}>
      {/* Progress Bar */}
      <ProgressBar steps={steps} currentStep={currentStep} color="#93C572" />

      {/* Validation Error */}
      {validationError && (
        <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start">
            <AlertCircle className="w-5 h-5 text-red-600 mr-2 flex-shrink-0 mt-0.5" />
            <div className="text-red-600 whitespace-pre-line">
              {validationError}
            </div>
          </div>
        </div>
      )}

      {/* Price Estimate */}
      {currentStep > 1 && (
        <div className="mb-6 p-4 bg-brand-50 border border-brand-200 rounded-lg">
          <div className="flex justify-between items-center">
            <span className="text-brand-800 font-medium">Estimated Price:</span>
            <span className="text-xl font-bold text-brand-800">${estimatedPrice}</span>
          </div>
          <p className="text-sm text-brand-600 mt-1">
            Final price may vary based on inspection and additional services
          </p>
        </div>
      )}

      {/* Form Content */}
      <div className="mt-6 mb-6">
        <SlideTransition>
          <div className="transition-all duration-300 ease-in-out">
            {renderStep()}
          </div>
        </SlideTransition>
      </div>

      {/* Form Actions */}
      <div className="flex items-center justify-between pt-6 border-t border-gray-200">
        <Button
          variant="outline"
          onClick={handlePrev}
          disabled={isSubmitting}
          size="lg"
          className="min-w-[120px]"
        >
          {currentStep === 0 ? 'Cancel' : 'Back'}
        </Button>
        
        <Button
          onClick={handleNext}
          disabled={isSubmitting}
          size="lg"
          className="min-w-[120px]"
        >
          {currentStep === steps.length - 1 ? 'Proceed to Payment' : 'Continue'}
        </Button>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        amount={estimatedPrice}
        description={`Carpet Cleaning Service - ${formData.carpetDetails.material || 'Standard'}`}
        customerEmail={formData.contact.email}
        onPaymentComplete={handlePaymentComplete}
      />
    </FormLayout>
  );
}