_format_version: "1.1"
services:
  - name: auth-v1-open
    _comment: "GoTrue: /auth/v1/verify* -> http://auth:9999/verify*"
    url: http://{{ .GotrueId }}:9999/verify
    routes:
      - name: auth-v1-open
        strip_path: true
        paths:
          - /auth/v1/verify
    plugins:
      - name: cors
  - name: auth-v1-open-callback
    _comment: "GoTrue: /auth/v1/callback* -> http://auth:9999/callback*"
    url: http://{{ .GotrueId }}:9999/callback
    routes:
      - name: auth-v1-open-callback
        strip_path: true
        paths:
          - /auth/v1/callback
    plugins:
      - name: cors
  - name: auth-v1-open-authorize
    _comment: "GoTrue: /auth/v1/authorize* -> http://auth:9999/authorize*"
    url: http://{{ .GotrueId }}:9999/authorize
    routes:
      - name: auth-v1-open-authorize
        strip_path: true
        paths:
          - /auth/v1/authorize
    plugins:
      - name: cors
  - name: auth-v1
    _comment: "GoTrue: /auth/v1/* -> http://auth:9999/*"
    url: http://{{ .GotrueId }}:9999/
    routes:
      - name: auth-v1-all
        strip_path: true
        paths:
          - /auth/v1/
    plugins:
      - name: cors
  - name: rest-v1
    _comment: "PostgREST: /rest/v1/* -> http://rest:3000/*"
    url: http://{{ .RestId }}:3000/
    routes:
      - name: rest-v1-all
        strip_path: true
        paths:
          - /rest/v1/
    plugins:
      - name: cors
  - name: rest-admin-v1
    _comment: "PostgREST: /rest-admin/v1/* -> http://rest:3001/*"
    url: http://{{ .RestId }}:3001/
    routes:
      - name: rest-admin-v1-all
        strip_path: true
        paths:
          - /rest-admin/v1/
    plugins:
      - name: cors
  - name: graphql-v1
    _comment: "PostgREST: /graphql/v1 -> http://rest:3000/rpc/graphql"
    url: http://{{ .RestId }}:3000/rpc/graphql
    routes:
      - name: graphql-v1-all
        strip_path: true
        paths:
          - /graphql/v1
    plugins:
      - name: cors
      - name: request-transformer
        config:
          add:
            headers:
              - "Content-Profile: graphql_public"
  - name: realtime-v1-ws
    _comment: "Realtime: /realtime/v1/* -> ws://realtime:4000/socket/websocket"
    url: http://{{ .RealtimeId }}:4000/socket
    protocol: ws
    routes:
      - name: realtime-v1-ws
        strip_path: true
        paths:
          - /realtime/v1/
    plugins:
      - name: cors
  - name: realtime-v1-longpoll
    _comment: "Realtime: /realtime/v1/* -> ws://realtime:4000/socket/longpoll"
    url: http://{{ .RealtimeId }}:4000/socket
    protocol: http
    routes:
      - name: realtime-v1-longpoll
        strip_path: true
        paths:
          - /realtime/v1/
    plugins:
      - name: cors
  - name: realtime-v1-rest
    _comment: "Realtime: /realtime/v1/* -> http://realtime:4000/api/*"
    url: http://{{ .RealtimeId }}:4000/api
    protocol: http
    routes:
      - name: realtime-v1-rest
        strip_path: true
        paths:
          - /realtime/v1/api
    plugins:
      - name: cors

  - name: storage-v1
    _comment: "Storage: /storage/v1/* -> http://storage-api:5000/*"
    url: http://{{ .StorageId }}:5000/
    routes:
      - name: storage-v1-all
        strip_path: true
        paths:
          - /storage/v1/
    plugins:
      - name: cors
  - name: pg-meta
    _comment: "pg-meta: /pg/* -> http://pg-meta:8080/*"
    url: http://{{ .PgmetaId }}:8080/
    routes:
      - name: pg-meta-all
        strip_path: true
        paths:
          - /pg/
  - name: functions-v1
    _comment: "Functions: /functions/v1/* -> http://edge-runtime:8081/*"
    url: http://{{ .EdgeRuntimeId }}:8081/
    # Set request idle timeout to 150s to match hosted project
    # Ref: https://supabase.com/docs/guides/functions/limits
    read_timeout: 150000
    routes:
      - name: functions-v1-all
        strip_path: true
        paths:
          - /functions/v1/
  - name: analytics-v1
    _comment: "Analytics: /analytics/v1/* -> http://logflare:4000/*"
    url: http://{{ .LogflareId }}:4000/
    routes:
      - name: analytics-v1-all
        strip_path: true
        paths:
          - /analytics/v1/
  - name: pooler-v2-ws
    _comment: "Pooler: /pooler/v2/* -> ws://pooler:4000/v2/*"
    url: http://{{ .PoolerId }}:4000/v2
    protocol: ws
    routes:
      - name: pooler-v2-ws
        strip_path: true
        paths:
          - /pooler/v2/
    plugins:
      - name: cors
