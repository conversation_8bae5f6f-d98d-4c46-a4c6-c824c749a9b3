# Supabase OpenAPI Specification

This directory contains the OpenAPI specification for Supabase Management APIs.

It is used to automatically generate the Go [client](pkg/api/client.gen.go) and [types](pkg/api/types.gen.go).

## Updating the specification

The specification yaml is generated from our NestJS middleware. The latest release is viewable as [Swagger UI](https://api.supabase.com/api/v1).

To make a new release:

1. Update `beta.yaml` with the latest version from local development

```bash
curl -o api/beta.yaml http://127.0.0.1:8080/api/v1-yaml
```

2. Regenerate the Go client and API types

```bash
go generate
```

3. [Optional] Manually add [properties](https://swagger.io/docs/specification/basic-structure/) not generated by NestJS
