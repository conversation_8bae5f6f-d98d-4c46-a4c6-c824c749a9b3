// Package api provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package api

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

// RequestEditorFn  is the function signature for the RequestEditor callback function
type RequestEditorFn func(ctx context.Context, req *http.Request) error

// Do<PERSON> performs HTTP requests.
//
// The standard http.Client implements this interface.
type HttpRequestDoer interface {
	Do(req *http.Request) (*http.Response, error)
}

// Client which conforms to the OpenAPI3 specification for this service.
type Client struct {
	// The endpoint of the server conforming to this interface, with scheme,
	// https://api.deepmap.com for example. This can contain a path relative
	// to the server, such as https://api.deepmap.com/dev-test, and all the
	// paths in the swagger spec will be appended to the server.
	Server string

	// Doer for performing requests, typically a *http.Client with any
	// customized settings, such as certificate chains.
	Client HttpRequestDoer

	// A list of callbacks for modifying requests which are generated before sending over
	// the network.
	RequestEditors []RequestEditorFn
}

// ClientOption allows setting custom parameters during construction
type ClientOption func(*Client) error

// Creates a new Client, with reasonable defaults
func NewClient(server string, opts ...ClientOption) (*Client, error) {
	// create a client with sane default values
	client := Client{
		Server: server,
	}
	// mutate client and add all optional params
	for _, o := range opts {
		if err := o(&client); err != nil {
			return nil, err
		}
	}
	// ensure the server URL always has a trailing slash
	if !strings.HasSuffix(client.Server, "/") {
		client.Server += "/"
	}
	// create httpClient, if not already present
	if client.Client == nil {
		client.Client = &http.Client{}
	}
	return &client, nil
}

// WithHTTPClient allows overriding the default Doer, which is
// automatically created using http.Client. This is useful for tests.
func WithHTTPClient(doer HttpRequestDoer) ClientOption {
	return func(c *Client) error {
		c.Client = doer
		return nil
	}
}

// WithRequestEditorFn allows setting up a callback function, which will be
// called right before sending the request. This can be used to mutate the request.
func WithRequestEditorFn(fn RequestEditorFn) ClientOption {
	return func(c *Client) error {
		c.RequestEditors = append(c.RequestEditors, fn)
		return nil
	}
}

// The interface specification for the client above.
type ClientInterface interface {
	// V1DeleteABranch request
	V1DeleteABranch(ctx context.Context, branchId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetABranchConfig request
	V1GetABranchConfig(ctx context.Context, branchId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1UpdateABranchConfigWithBody request with any body
	V1UpdateABranchConfigWithBody(ctx context.Context, branchId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1UpdateABranchConfig(ctx context.Context, branchId openapi_types.UUID, body V1UpdateABranchConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1DiffABranch request
	V1DiffABranch(ctx context.Context, branchId openapi_types.UUID, params *V1DiffABranchParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1MergeABranchWithBody request with any body
	V1MergeABranchWithBody(ctx context.Context, branchId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1MergeABranch(ctx context.Context, branchId openapi_types.UUID, body V1MergeABranchJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1PushABranchWithBody request with any body
	V1PushABranchWithBody(ctx context.Context, branchId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1PushABranch(ctx context.Context, branchId openapi_types.UUID, body V1PushABranchJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ResetABranchWithBody request with any body
	V1ResetABranchWithBody(ctx context.Context, branchId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1ResetABranch(ctx context.Context, branchId openapi_types.UUID, body V1ResetABranchJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1AuthorizeUser request
	V1AuthorizeUser(ctx context.Context, params *V1AuthorizeUserParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1RevokeTokenWithBody request with any body
	V1RevokeTokenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1RevokeToken(ctx context.Context, body V1RevokeTokenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ExchangeOauthTokenWithBody request with any body
	V1ExchangeOauthTokenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1ExchangeOauthTokenWithFormdataBody(ctx context.Context, body V1ExchangeOauthTokenFormdataRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ListAllOrganizations request
	V1ListAllOrganizations(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1CreateAnOrganizationWithBody request with any body
	V1CreateAnOrganizationWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1CreateAnOrganization(ctx context.Context, body V1CreateAnOrganizationJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetAnOrganization request
	V1GetAnOrganization(ctx context.Context, slug string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ListOrganizationMembers request
	V1ListOrganizationMembers(ctx context.Context, slug string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetOrganizationProjectClaim request
	V1GetOrganizationProjectClaim(ctx context.Context, slug string, token string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ClaimProjectForOrganization request
	V1ClaimProjectForOrganization(ctx context.Context, slug string, token string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ListAllProjects request
	V1ListAllProjects(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1CreateAProjectWithBody request with any body
	V1CreateAProjectWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1CreateAProject(ctx context.Context, body V1CreateAProjectJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1DeleteAProject request
	V1DeleteAProject(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetProject request
	V1GetProject(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetPerformanceAdvisors request
	GetPerformanceAdvisors(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetSecurityAdvisors request
	GetSecurityAdvisors(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetLogs request
	GetLogs(ctx context.Context, ref string, params *GetLogsParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApiCounts request
	GetApiCounts(ctx context.Context, ref string, params *GetApiCountsParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApiRequestsCount request
	GetApiRequestsCount(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetProjectApiKeys request
	V1GetProjectApiKeys(ctx context.Context, ref string, params *V1GetProjectApiKeysParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// CreateApiKeyWithBody request with any body
	CreateApiKeyWithBody(ctx context.Context, ref string, params *CreateApiKeyParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	CreateApiKey(ctx context.Context, ref string, params *CreateApiKeyParams, body CreateApiKeyJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteApiKey request
	DeleteApiKey(ctx context.Context, ref string, id openapi_types.UUID, params *DeleteApiKeyParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApiKey request
	GetApiKey(ctx context.Context, ref string, id openapi_types.UUID, params *GetApiKeyParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// UpdateApiKeyWithBody request with any body
	UpdateApiKeyWithBody(ctx context.Context, ref string, id openapi_types.UUID, params *UpdateApiKeyParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	UpdateApiKey(ctx context.Context, ref string, id openapi_types.UUID, params *UpdateApiKeyParams, body UpdateApiKeyJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ListProjectAddons request
	V1ListProjectAddons(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ApplyProjectAddonWithBody request with any body
	V1ApplyProjectAddonWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1ApplyProjectAddon(ctx context.Context, ref string, body V1ApplyProjectAddonJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1RemoveProjectAddon request
	V1RemoveProjectAddon(ctx context.Context, ref string, addonVariant interface{}, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1DisablePreviewBranching request
	V1DisablePreviewBranching(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ListAllBranches request
	V1ListAllBranches(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1CreateABranchWithBody request with any body
	V1CreateABranchWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1CreateABranch(ctx context.Context, ref string, body V1CreateABranchJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1DeleteProjectClaimToken request
	V1DeleteProjectClaimToken(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetProjectClaimToken request
	V1GetProjectClaimToken(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1CreateProjectClaimToken request
	V1CreateProjectClaimToken(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetAuthServiceConfig request
	V1GetAuthServiceConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1UpdateAuthServiceConfigWithBody request with any body
	V1UpdateAuthServiceConfigWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1UpdateAuthServiceConfig(ctx context.Context, ref string, body V1UpdateAuthServiceConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// ListSigningKeysForProject request
	ListSigningKeysForProject(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// CreateSigningKeyForProjectWithBody request with any body
	CreateSigningKeyForProjectWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	CreateSigningKeyForProject(ctx context.Context, ref string, body CreateSigningKeyForProjectJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteSigningKey request
	DeleteSigningKey(ctx context.Context, ref string, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetSigningKeyForProject request
	GetSigningKeyForProject(ctx context.Context, ref string, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PatchSigningKeyWithBody request with any body
	PatchSigningKeyWithBody(ctx context.Context, ref string, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PatchSigningKey(ctx context.Context, ref string, id openapi_types.UUID, body PatchSigningKeyJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ListAllSsoProvider request
	V1ListAllSsoProvider(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1CreateASsoProviderWithBody request with any body
	V1CreateASsoProviderWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1CreateASsoProvider(ctx context.Context, ref string, body V1CreateASsoProviderJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1DeleteASsoProvider request
	V1DeleteASsoProvider(ctx context.Context, ref string, providerId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetASsoProvider request
	V1GetASsoProvider(ctx context.Context, ref string, providerId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1UpdateASsoProviderWithBody request with any body
	V1UpdateASsoProviderWithBody(ctx context.Context, ref string, providerId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1UpdateASsoProvider(ctx context.Context, ref string, providerId openapi_types.UUID, body V1UpdateASsoProviderJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// ListTPAForProject request
	ListTPAForProject(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// CreateTPAForProjectWithBody request with any body
	CreateTPAForProjectWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	CreateTPAForProject(ctx context.Context, ref string, body CreateTPAForProjectJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteTPAForProject request
	DeleteTPAForProject(ctx context.Context, ref string, tpaId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetTPAForProject request
	GetTPAForProject(ctx context.Context, ref string, tpaId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetProjectPgbouncerConfig request
	V1GetProjectPgbouncerConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetPoolerConfig request
	V1GetPoolerConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1UpdatePoolerConfigWithBody request with any body
	V1UpdatePoolerConfigWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1UpdatePoolerConfig(ctx context.Context, ref string, body V1UpdatePoolerConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetPostgresConfig request
	V1GetPostgresConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1UpdatePostgresConfigWithBody request with any body
	V1UpdatePostgresConfigWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1UpdatePostgresConfig(ctx context.Context, ref string, body V1UpdatePostgresConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetStorageConfig request
	V1GetStorageConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1UpdateStorageConfigWithBody request with any body
	V1UpdateStorageConfigWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1UpdateStorageConfig(ctx context.Context, ref string, body V1UpdateStorageConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1DeleteHostnameConfig request
	V1DeleteHostnameConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetHostnameConfig request
	V1GetHostnameConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ActivateCustomHostname request
	V1ActivateCustomHostname(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1UpdateHostnameConfigWithBody request with any body
	V1UpdateHostnameConfigWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1UpdateHostnameConfig(ctx context.Context, ref string, body V1UpdateHostnameConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1VerifyDnsConfig request
	V1VerifyDnsConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ListAllBackups request
	V1ListAllBackups(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1RestorePitrBackupWithBody request with any body
	V1RestorePitrBackupWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1RestorePitrBackup(ctx context.Context, ref string, body V1RestorePitrBackupJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetDatabaseMetadata request
	GetDatabaseMetadata(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ListMigrationHistory request
	V1ListMigrationHistory(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ApplyAMigrationWithBody request with any body
	V1ApplyAMigrationWithBody(ctx context.Context, ref string, params *V1ApplyAMigrationParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1ApplyAMigration(ctx context.Context, ref string, params *V1ApplyAMigrationParams, body V1ApplyAMigrationJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1RunAQueryWithBody request with any body
	V1RunAQueryWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1RunAQuery(ctx context.Context, ref string, body V1RunAQueryJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1EnableDatabaseWebhook request
	V1EnableDatabaseWebhook(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ListAllFunctions request
	V1ListAllFunctions(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1CreateAFunctionWithBody request with any body
	V1CreateAFunctionWithBody(ctx context.Context, ref string, params *V1CreateAFunctionParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1CreateAFunction(ctx context.Context, ref string, params *V1CreateAFunctionParams, body V1CreateAFunctionJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1BulkUpdateFunctionsWithBody request with any body
	V1BulkUpdateFunctionsWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1BulkUpdateFunctions(ctx context.Context, ref string, body V1BulkUpdateFunctionsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1DeployAFunctionWithBody request with any body
	V1DeployAFunctionWithBody(ctx context.Context, ref string, params *V1DeployAFunctionParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1DeleteAFunction request
	V1DeleteAFunction(ctx context.Context, ref string, functionSlug string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetAFunction request
	V1GetAFunction(ctx context.Context, ref string, functionSlug string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1UpdateAFunctionWithBody request with any body
	V1UpdateAFunctionWithBody(ctx context.Context, ref string, functionSlug string, params *V1UpdateAFunctionParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1UpdateAFunction(ctx context.Context, ref string, functionSlug string, params *V1UpdateAFunctionParams, body V1UpdateAFunctionJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetAFunctionBody request
	V1GetAFunctionBody(ctx context.Context, ref string, functionSlug string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetServicesHealth request
	V1GetServicesHealth(ctx context.Context, ref string, params *V1GetServicesHealthParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1DeleteNetworkBansWithBody request with any body
	V1DeleteNetworkBansWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1DeleteNetworkBans(ctx context.Context, ref string, body V1DeleteNetworkBansJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ListAllNetworkBans request
	V1ListAllNetworkBans(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ListAllNetworkBansEnriched request
	V1ListAllNetworkBansEnriched(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetNetworkRestrictions request
	V1GetNetworkRestrictions(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1UpdateNetworkRestrictionsWithBody request with any body
	V1UpdateNetworkRestrictionsWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1UpdateNetworkRestrictions(ctx context.Context, ref string, body V1UpdateNetworkRestrictionsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1PauseAProject request
	V1PauseAProject(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetPgsodiumConfig request
	V1GetPgsodiumConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1UpdatePgsodiumConfigWithBody request with any body
	V1UpdatePgsodiumConfigWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1UpdatePgsodiumConfig(ctx context.Context, ref string, body V1UpdatePgsodiumConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetPostgrestServiceConfig request
	V1GetPostgrestServiceConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1UpdatePostgrestServiceConfigWithBody request with any body
	V1UpdatePostgrestServiceConfigWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1UpdatePostgrestServiceConfig(ctx context.Context, ref string, body V1UpdatePostgrestServiceConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1RemoveAReadReplicaWithBody request with any body
	V1RemoveAReadReplicaWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1RemoveAReadReplica(ctx context.Context, ref string, body V1RemoveAReadReplicaJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1SetupAReadReplicaWithBody request with any body
	V1SetupAReadReplicaWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1SetupAReadReplica(ctx context.Context, ref string, body V1SetupAReadReplicaJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetReadonlyModeStatus request
	V1GetReadonlyModeStatus(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1DisableReadonlyModeTemporarily request
	V1DisableReadonlyModeTemporarily(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ListAvailableRestoreVersions request
	V1ListAvailableRestoreVersions(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1RestoreAProject request
	V1RestoreAProject(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1CancelAProjectRestoration request
	V1CancelAProjectRestoration(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1BulkDeleteSecretsWithBody request with any body
	V1BulkDeleteSecretsWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1BulkDeleteSecrets(ctx context.Context, ref string, body V1BulkDeleteSecretsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ListAllSecrets request
	V1ListAllSecrets(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1BulkCreateSecretsWithBody request with any body
	V1BulkCreateSecretsWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1BulkCreateSecrets(ctx context.Context, ref string, body V1BulkCreateSecretsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetSslEnforcementConfig request
	V1GetSslEnforcementConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1UpdateSslEnforcementConfigWithBody request with any body
	V1UpdateSslEnforcementConfigWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1UpdateSslEnforcementConfig(ctx context.Context, ref string, body V1UpdateSslEnforcementConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ListAllBuckets request
	V1ListAllBuckets(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GenerateTypescriptTypes request
	V1GenerateTypescriptTypes(ctx context.Context, ref string, params *V1GenerateTypescriptTypesParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1UpgradePostgresVersionWithBody request with any body
	V1UpgradePostgresVersionWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1UpgradePostgresVersion(ctx context.Context, ref string, body V1UpgradePostgresVersionJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetPostgresUpgradeEligibility request
	V1GetPostgresUpgradeEligibility(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetPostgresUpgradeStatus request
	V1GetPostgresUpgradeStatus(ctx context.Context, ref string, params *V1GetPostgresUpgradeStatusParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1DeactivateVanitySubdomainConfig request
	V1DeactivateVanitySubdomainConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetVanitySubdomainConfig request
	V1GetVanitySubdomainConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ActivateVanitySubdomainConfigWithBody request with any body
	V1ActivateVanitySubdomainConfigWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1ActivateVanitySubdomainConfig(ctx context.Context, ref string, body V1ActivateVanitySubdomainConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1CheckVanitySubdomainAvailabilityWithBody request with any body
	V1CheckVanitySubdomainAvailabilityWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	V1CheckVanitySubdomainAvailability(ctx context.Context, ref string, body V1CheckVanitySubdomainAvailabilityJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1ListAllSnippets request
	V1ListAllSnippets(ctx context.Context, params *V1ListAllSnippetsParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// V1GetASnippet request
	V1GetASnippet(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error)
}

func (c *Client) V1DeleteABranch(ctx context.Context, branchId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1DeleteABranchRequest(c.Server, branchId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetABranchConfig(ctx context.Context, branchId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetABranchConfigRequest(c.Server, branchId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdateABranchConfigWithBody(ctx context.Context, branchId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdateABranchConfigRequestWithBody(c.Server, branchId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdateABranchConfig(ctx context.Context, branchId openapi_types.UUID, body V1UpdateABranchConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdateABranchConfigRequest(c.Server, branchId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1DiffABranch(ctx context.Context, branchId openapi_types.UUID, params *V1DiffABranchParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1DiffABranchRequest(c.Server, branchId, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1MergeABranchWithBody(ctx context.Context, branchId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1MergeABranchRequestWithBody(c.Server, branchId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1MergeABranch(ctx context.Context, branchId openapi_types.UUID, body V1MergeABranchJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1MergeABranchRequest(c.Server, branchId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1PushABranchWithBody(ctx context.Context, branchId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1PushABranchRequestWithBody(c.Server, branchId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1PushABranch(ctx context.Context, branchId openapi_types.UUID, body V1PushABranchJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1PushABranchRequest(c.Server, branchId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ResetABranchWithBody(ctx context.Context, branchId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ResetABranchRequestWithBody(c.Server, branchId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ResetABranch(ctx context.Context, branchId openapi_types.UUID, body V1ResetABranchJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ResetABranchRequest(c.Server, branchId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1AuthorizeUser(ctx context.Context, params *V1AuthorizeUserParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1AuthorizeUserRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1RevokeTokenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1RevokeTokenRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1RevokeToken(ctx context.Context, body V1RevokeTokenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1RevokeTokenRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ExchangeOauthTokenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ExchangeOauthTokenRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ExchangeOauthTokenWithFormdataBody(ctx context.Context, body V1ExchangeOauthTokenFormdataRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ExchangeOauthTokenRequestWithFormdataBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ListAllOrganizations(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ListAllOrganizationsRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1CreateAnOrganizationWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1CreateAnOrganizationRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1CreateAnOrganization(ctx context.Context, body V1CreateAnOrganizationJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1CreateAnOrganizationRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetAnOrganization(ctx context.Context, slug string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetAnOrganizationRequest(c.Server, slug)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ListOrganizationMembers(ctx context.Context, slug string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ListOrganizationMembersRequest(c.Server, slug)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetOrganizationProjectClaim(ctx context.Context, slug string, token string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetOrganizationProjectClaimRequest(c.Server, slug, token)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ClaimProjectForOrganization(ctx context.Context, slug string, token string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ClaimProjectForOrganizationRequest(c.Server, slug, token)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ListAllProjects(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ListAllProjectsRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1CreateAProjectWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1CreateAProjectRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1CreateAProject(ctx context.Context, body V1CreateAProjectJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1CreateAProjectRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1DeleteAProject(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1DeleteAProjectRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetProject(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetProjectRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetPerformanceAdvisors(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetPerformanceAdvisorsRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetSecurityAdvisors(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetSecurityAdvisorsRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetLogs(ctx context.Context, ref string, params *GetLogsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetLogsRequest(c.Server, ref, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApiCounts(ctx context.Context, ref string, params *GetApiCountsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApiCountsRequest(c.Server, ref, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApiRequestsCount(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApiRequestsCountRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetProjectApiKeys(ctx context.Context, ref string, params *V1GetProjectApiKeysParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetProjectApiKeysRequest(c.Server, ref, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreateApiKeyWithBody(ctx context.Context, ref string, params *CreateApiKeyParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreateApiKeyRequestWithBody(c.Server, ref, params, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreateApiKey(ctx context.Context, ref string, params *CreateApiKeyParams, body CreateApiKeyJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreateApiKeyRequest(c.Server, ref, params, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteApiKey(ctx context.Context, ref string, id openapi_types.UUID, params *DeleteApiKeyParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteApiKeyRequest(c.Server, ref, id, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApiKey(ctx context.Context, ref string, id openapi_types.UUID, params *GetApiKeyParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApiKeyRequest(c.Server, ref, id, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApiKeyWithBody(ctx context.Context, ref string, id openapi_types.UUID, params *UpdateApiKeyParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApiKeyRequestWithBody(c.Server, ref, id, params, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateApiKey(ctx context.Context, ref string, id openapi_types.UUID, params *UpdateApiKeyParams, body UpdateApiKeyJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateApiKeyRequest(c.Server, ref, id, params, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ListProjectAddons(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ListProjectAddonsRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ApplyProjectAddonWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ApplyProjectAddonRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ApplyProjectAddon(ctx context.Context, ref string, body V1ApplyProjectAddonJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ApplyProjectAddonRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1RemoveProjectAddon(ctx context.Context, ref string, addonVariant interface{}, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1RemoveProjectAddonRequest(c.Server, ref, addonVariant)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1DisablePreviewBranching(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1DisablePreviewBranchingRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ListAllBranches(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ListAllBranchesRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1CreateABranchWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1CreateABranchRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1CreateABranch(ctx context.Context, ref string, body V1CreateABranchJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1CreateABranchRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1DeleteProjectClaimToken(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1DeleteProjectClaimTokenRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetProjectClaimToken(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetProjectClaimTokenRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1CreateProjectClaimToken(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1CreateProjectClaimTokenRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetAuthServiceConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetAuthServiceConfigRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdateAuthServiceConfigWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdateAuthServiceConfigRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdateAuthServiceConfig(ctx context.Context, ref string, body V1UpdateAuthServiceConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdateAuthServiceConfigRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) ListSigningKeysForProject(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewListSigningKeysForProjectRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreateSigningKeyForProjectWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreateSigningKeyForProjectRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreateSigningKeyForProject(ctx context.Context, ref string, body CreateSigningKeyForProjectJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreateSigningKeyForProjectRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteSigningKey(ctx context.Context, ref string, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteSigningKeyRequest(c.Server, ref, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetSigningKeyForProject(ctx context.Context, ref string, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetSigningKeyForProjectRequest(c.Server, ref, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PatchSigningKeyWithBody(ctx context.Context, ref string, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPatchSigningKeyRequestWithBody(c.Server, ref, id, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PatchSigningKey(ctx context.Context, ref string, id openapi_types.UUID, body PatchSigningKeyJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPatchSigningKeyRequest(c.Server, ref, id, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ListAllSsoProvider(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ListAllSsoProviderRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1CreateASsoProviderWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1CreateASsoProviderRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1CreateASsoProvider(ctx context.Context, ref string, body V1CreateASsoProviderJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1CreateASsoProviderRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1DeleteASsoProvider(ctx context.Context, ref string, providerId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1DeleteASsoProviderRequest(c.Server, ref, providerId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetASsoProvider(ctx context.Context, ref string, providerId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetASsoProviderRequest(c.Server, ref, providerId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdateASsoProviderWithBody(ctx context.Context, ref string, providerId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdateASsoProviderRequestWithBody(c.Server, ref, providerId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdateASsoProvider(ctx context.Context, ref string, providerId openapi_types.UUID, body V1UpdateASsoProviderJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdateASsoProviderRequest(c.Server, ref, providerId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) ListTPAForProject(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewListTPAForProjectRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreateTPAForProjectWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreateTPAForProjectRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreateTPAForProject(ctx context.Context, ref string, body CreateTPAForProjectJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreateTPAForProjectRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteTPAForProject(ctx context.Context, ref string, tpaId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteTPAForProjectRequest(c.Server, ref, tpaId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetTPAForProject(ctx context.Context, ref string, tpaId openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetTPAForProjectRequest(c.Server, ref, tpaId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetProjectPgbouncerConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetProjectPgbouncerConfigRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetPoolerConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetPoolerConfigRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdatePoolerConfigWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdatePoolerConfigRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdatePoolerConfig(ctx context.Context, ref string, body V1UpdatePoolerConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdatePoolerConfigRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetPostgresConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetPostgresConfigRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdatePostgresConfigWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdatePostgresConfigRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdatePostgresConfig(ctx context.Context, ref string, body V1UpdatePostgresConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdatePostgresConfigRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetStorageConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetStorageConfigRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdateStorageConfigWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdateStorageConfigRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdateStorageConfig(ctx context.Context, ref string, body V1UpdateStorageConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdateStorageConfigRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1DeleteHostnameConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1DeleteHostnameConfigRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetHostnameConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetHostnameConfigRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ActivateCustomHostname(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ActivateCustomHostnameRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdateHostnameConfigWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdateHostnameConfigRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdateHostnameConfig(ctx context.Context, ref string, body V1UpdateHostnameConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdateHostnameConfigRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1VerifyDnsConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1VerifyDnsConfigRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ListAllBackups(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ListAllBackupsRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1RestorePitrBackupWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1RestorePitrBackupRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1RestorePitrBackup(ctx context.Context, ref string, body V1RestorePitrBackupJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1RestorePitrBackupRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetDatabaseMetadata(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetDatabaseMetadataRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ListMigrationHistory(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ListMigrationHistoryRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ApplyAMigrationWithBody(ctx context.Context, ref string, params *V1ApplyAMigrationParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ApplyAMigrationRequestWithBody(c.Server, ref, params, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ApplyAMigration(ctx context.Context, ref string, params *V1ApplyAMigrationParams, body V1ApplyAMigrationJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ApplyAMigrationRequest(c.Server, ref, params, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1RunAQueryWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1RunAQueryRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1RunAQuery(ctx context.Context, ref string, body V1RunAQueryJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1RunAQueryRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1EnableDatabaseWebhook(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1EnableDatabaseWebhookRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ListAllFunctions(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ListAllFunctionsRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1CreateAFunctionWithBody(ctx context.Context, ref string, params *V1CreateAFunctionParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1CreateAFunctionRequestWithBody(c.Server, ref, params, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1CreateAFunction(ctx context.Context, ref string, params *V1CreateAFunctionParams, body V1CreateAFunctionJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1CreateAFunctionRequest(c.Server, ref, params, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1BulkUpdateFunctionsWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1BulkUpdateFunctionsRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1BulkUpdateFunctions(ctx context.Context, ref string, body V1BulkUpdateFunctionsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1BulkUpdateFunctionsRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1DeployAFunctionWithBody(ctx context.Context, ref string, params *V1DeployAFunctionParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1DeployAFunctionRequestWithBody(c.Server, ref, params, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1DeleteAFunction(ctx context.Context, ref string, functionSlug string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1DeleteAFunctionRequest(c.Server, ref, functionSlug)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetAFunction(ctx context.Context, ref string, functionSlug string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetAFunctionRequest(c.Server, ref, functionSlug)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdateAFunctionWithBody(ctx context.Context, ref string, functionSlug string, params *V1UpdateAFunctionParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdateAFunctionRequestWithBody(c.Server, ref, functionSlug, params, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdateAFunction(ctx context.Context, ref string, functionSlug string, params *V1UpdateAFunctionParams, body V1UpdateAFunctionJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdateAFunctionRequest(c.Server, ref, functionSlug, params, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetAFunctionBody(ctx context.Context, ref string, functionSlug string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetAFunctionBodyRequest(c.Server, ref, functionSlug)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetServicesHealth(ctx context.Context, ref string, params *V1GetServicesHealthParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetServicesHealthRequest(c.Server, ref, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1DeleteNetworkBansWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1DeleteNetworkBansRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1DeleteNetworkBans(ctx context.Context, ref string, body V1DeleteNetworkBansJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1DeleteNetworkBansRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ListAllNetworkBans(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ListAllNetworkBansRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ListAllNetworkBansEnriched(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ListAllNetworkBansEnrichedRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetNetworkRestrictions(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetNetworkRestrictionsRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdateNetworkRestrictionsWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdateNetworkRestrictionsRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdateNetworkRestrictions(ctx context.Context, ref string, body V1UpdateNetworkRestrictionsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdateNetworkRestrictionsRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1PauseAProject(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1PauseAProjectRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetPgsodiumConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetPgsodiumConfigRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdatePgsodiumConfigWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdatePgsodiumConfigRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdatePgsodiumConfig(ctx context.Context, ref string, body V1UpdatePgsodiumConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdatePgsodiumConfigRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetPostgrestServiceConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetPostgrestServiceConfigRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdatePostgrestServiceConfigWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdatePostgrestServiceConfigRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdatePostgrestServiceConfig(ctx context.Context, ref string, body V1UpdatePostgrestServiceConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdatePostgrestServiceConfigRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1RemoveAReadReplicaWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1RemoveAReadReplicaRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1RemoveAReadReplica(ctx context.Context, ref string, body V1RemoveAReadReplicaJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1RemoveAReadReplicaRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1SetupAReadReplicaWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1SetupAReadReplicaRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1SetupAReadReplica(ctx context.Context, ref string, body V1SetupAReadReplicaJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1SetupAReadReplicaRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetReadonlyModeStatus(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetReadonlyModeStatusRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1DisableReadonlyModeTemporarily(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1DisableReadonlyModeTemporarilyRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ListAvailableRestoreVersions(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ListAvailableRestoreVersionsRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1RestoreAProject(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1RestoreAProjectRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1CancelAProjectRestoration(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1CancelAProjectRestorationRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1BulkDeleteSecretsWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1BulkDeleteSecretsRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1BulkDeleteSecrets(ctx context.Context, ref string, body V1BulkDeleteSecretsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1BulkDeleteSecretsRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ListAllSecrets(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ListAllSecretsRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1BulkCreateSecretsWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1BulkCreateSecretsRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1BulkCreateSecrets(ctx context.Context, ref string, body V1BulkCreateSecretsJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1BulkCreateSecretsRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetSslEnforcementConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetSslEnforcementConfigRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdateSslEnforcementConfigWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdateSslEnforcementConfigRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpdateSslEnforcementConfig(ctx context.Context, ref string, body V1UpdateSslEnforcementConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpdateSslEnforcementConfigRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ListAllBuckets(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ListAllBucketsRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GenerateTypescriptTypes(ctx context.Context, ref string, params *V1GenerateTypescriptTypesParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GenerateTypescriptTypesRequest(c.Server, ref, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpgradePostgresVersionWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpgradePostgresVersionRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1UpgradePostgresVersion(ctx context.Context, ref string, body V1UpgradePostgresVersionJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1UpgradePostgresVersionRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetPostgresUpgradeEligibility(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetPostgresUpgradeEligibilityRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetPostgresUpgradeStatus(ctx context.Context, ref string, params *V1GetPostgresUpgradeStatusParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetPostgresUpgradeStatusRequest(c.Server, ref, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1DeactivateVanitySubdomainConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1DeactivateVanitySubdomainConfigRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetVanitySubdomainConfig(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetVanitySubdomainConfigRequest(c.Server, ref)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ActivateVanitySubdomainConfigWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ActivateVanitySubdomainConfigRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ActivateVanitySubdomainConfig(ctx context.Context, ref string, body V1ActivateVanitySubdomainConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ActivateVanitySubdomainConfigRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1CheckVanitySubdomainAvailabilityWithBody(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1CheckVanitySubdomainAvailabilityRequestWithBody(c.Server, ref, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1CheckVanitySubdomainAvailability(ctx context.Context, ref string, body V1CheckVanitySubdomainAvailabilityJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1CheckVanitySubdomainAvailabilityRequest(c.Server, ref, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1ListAllSnippets(ctx context.Context, params *V1ListAllSnippetsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1ListAllSnippetsRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) V1GetASnippet(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewV1GetASnippetRequest(c.Server, id)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

// NewV1DeleteABranchRequest generates requests for V1DeleteABranch
func NewV1DeleteABranchRequest(server string, branchId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "branch_id", runtime.ParamLocationPath, branchId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/branches/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1GetABranchConfigRequest generates requests for V1GetABranchConfig
func NewV1GetABranchConfigRequest(server string, branchId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "branch_id", runtime.ParamLocationPath, branchId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/branches/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1UpdateABranchConfigRequest calls the generic V1UpdateABranchConfig builder with application/json body
func NewV1UpdateABranchConfigRequest(server string, branchId openapi_types.UUID, body V1UpdateABranchConfigJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1UpdateABranchConfigRequestWithBody(server, branchId, "application/json", bodyReader)
}

// NewV1UpdateABranchConfigRequestWithBody generates requests for V1UpdateABranchConfig with any type of body
func NewV1UpdateABranchConfigRequestWithBody(server string, branchId openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "branch_id", runtime.ParamLocationPath, branchId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/branches/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1DiffABranchRequest generates requests for V1DiffABranch
func NewV1DiffABranchRequest(server string, branchId openapi_types.UUID, params *V1DiffABranchParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "branch_id", runtime.ParamLocationPath, branchId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/branches/%s/diff", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.IncludedSchemas != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "included_schemas", runtime.ParamLocationQuery, *params.IncludedSchemas); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1MergeABranchRequest calls the generic V1MergeABranch builder with application/json body
func NewV1MergeABranchRequest(server string, branchId openapi_types.UUID, body V1MergeABranchJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1MergeABranchRequestWithBody(server, branchId, "application/json", bodyReader)
}

// NewV1MergeABranchRequestWithBody generates requests for V1MergeABranch with any type of body
func NewV1MergeABranchRequestWithBody(server string, branchId openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "branch_id", runtime.ParamLocationPath, branchId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/branches/%s/merge", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1PushABranchRequest calls the generic V1PushABranch builder with application/json body
func NewV1PushABranchRequest(server string, branchId openapi_types.UUID, body V1PushABranchJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1PushABranchRequestWithBody(server, branchId, "application/json", bodyReader)
}

// NewV1PushABranchRequestWithBody generates requests for V1PushABranch with any type of body
func NewV1PushABranchRequestWithBody(server string, branchId openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "branch_id", runtime.ParamLocationPath, branchId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/branches/%s/push", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1ResetABranchRequest calls the generic V1ResetABranch builder with application/json body
func NewV1ResetABranchRequest(server string, branchId openapi_types.UUID, body V1ResetABranchJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1ResetABranchRequestWithBody(server, branchId, "application/json", bodyReader)
}

// NewV1ResetABranchRequestWithBody generates requests for V1ResetABranch with any type of body
func NewV1ResetABranchRequestWithBody(server string, branchId openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "branch_id", runtime.ParamLocationPath, branchId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/branches/%s/reset", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1AuthorizeUserRequest generates requests for V1AuthorizeUser
func NewV1AuthorizeUserRequest(server string, params *V1AuthorizeUserParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/oauth/authorize")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "client_id", runtime.ParamLocationQuery, params.ClientId); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "response_type", runtime.ParamLocationQuery, params.ResponseType); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "redirect_uri", runtime.ParamLocationQuery, params.RedirectUri); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if params.Scope != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "scope", runtime.ParamLocationQuery, *params.Scope); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.State != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "state", runtime.ParamLocationQuery, *params.State); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.ResponseMode != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "response_mode", runtime.ParamLocationQuery, *params.ResponseMode); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.CodeChallenge != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "code_challenge", runtime.ParamLocationQuery, *params.CodeChallenge); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.CodeChallengeMethod != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "code_challenge_method", runtime.ParamLocationQuery, *params.CodeChallengeMethod); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1RevokeTokenRequest calls the generic V1RevokeToken builder with application/json body
func NewV1RevokeTokenRequest(server string, body V1RevokeTokenJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1RevokeTokenRequestWithBody(server, "application/json", bodyReader)
}

// NewV1RevokeTokenRequestWithBody generates requests for V1RevokeToken with any type of body
func NewV1RevokeTokenRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/oauth/revoke")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1ExchangeOauthTokenRequestWithFormdataBody calls the generic V1ExchangeOauthToken builder with application/x-www-form-urlencoded body
func NewV1ExchangeOauthTokenRequestWithFormdataBody(server string, body V1ExchangeOauthTokenFormdataRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	bodyStr, err := runtime.MarshalForm(body, nil)
	if err != nil {
		return nil, err
	}
	bodyReader = strings.NewReader(bodyStr.Encode())
	return NewV1ExchangeOauthTokenRequestWithBody(server, "application/x-www-form-urlencoded", bodyReader)
}

// NewV1ExchangeOauthTokenRequestWithBody generates requests for V1ExchangeOauthToken with any type of body
func NewV1ExchangeOauthTokenRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/oauth/token")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1ListAllOrganizationsRequest generates requests for V1ListAllOrganizations
func NewV1ListAllOrganizationsRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/organizations")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1CreateAnOrganizationRequest calls the generic V1CreateAnOrganization builder with application/json body
func NewV1CreateAnOrganizationRequest(server string, body V1CreateAnOrganizationJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1CreateAnOrganizationRequestWithBody(server, "application/json", bodyReader)
}

// NewV1CreateAnOrganizationRequestWithBody generates requests for V1CreateAnOrganization with any type of body
func NewV1CreateAnOrganizationRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/organizations")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1GetAnOrganizationRequest generates requests for V1GetAnOrganization
func NewV1GetAnOrganizationRequest(server string, slug string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "slug", runtime.ParamLocationPath, slug)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/organizations/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1ListOrganizationMembersRequest generates requests for V1ListOrganizationMembers
func NewV1ListOrganizationMembersRequest(server string, slug string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "slug", runtime.ParamLocationPath, slug)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/organizations/%s/members", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1GetOrganizationProjectClaimRequest generates requests for V1GetOrganizationProjectClaim
func NewV1GetOrganizationProjectClaimRequest(server string, slug string, token string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "slug", runtime.ParamLocationPath, slug)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "token", runtime.ParamLocationPath, token)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/organizations/%s/project-claim/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1ClaimProjectForOrganizationRequest generates requests for V1ClaimProjectForOrganization
func NewV1ClaimProjectForOrganizationRequest(server string, slug string, token string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "slug", runtime.ParamLocationPath, slug)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "token", runtime.ParamLocationPath, token)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/organizations/%s/project-claim/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1ListAllProjectsRequest generates requests for V1ListAllProjects
func NewV1ListAllProjectsRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1CreateAProjectRequest calls the generic V1CreateAProject builder with application/json body
func NewV1CreateAProjectRequest(server string, body V1CreateAProjectJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1CreateAProjectRequestWithBody(server, "application/json", bodyReader)
}

// NewV1CreateAProjectRequestWithBody generates requests for V1CreateAProject with any type of body
func NewV1CreateAProjectRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1DeleteAProjectRequest generates requests for V1DeleteAProject
func NewV1DeleteAProjectRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1GetProjectRequest generates requests for V1GetProject
func NewV1GetProjectRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetPerformanceAdvisorsRequest generates requests for GetPerformanceAdvisors
func NewGetPerformanceAdvisorsRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/advisors/performance", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetSecurityAdvisorsRequest generates requests for GetSecurityAdvisors
func NewGetSecurityAdvisorsRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/advisors/security", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetLogsRequest generates requests for GetLogs
func NewGetLogsRequest(server string, ref string, params *GetLogsParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/analytics/endpoints/logs.all", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Sql != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "sql", runtime.ParamLocationQuery, *params.Sql); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.IsoTimestampStart != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "iso_timestamp_start", runtime.ParamLocationQuery, *params.IsoTimestampStart); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.IsoTimestampEnd != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "iso_timestamp_end", runtime.ParamLocationQuery, *params.IsoTimestampEnd); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetApiCountsRequest generates requests for GetApiCounts
func NewGetApiCountsRequest(server string, ref string, params *GetApiCountsParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/analytics/endpoints/usage.api-counts", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Interval != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "interval", runtime.ParamLocationQuery, *params.Interval); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetApiRequestsCountRequest generates requests for GetApiRequestsCount
func NewGetApiRequestsCountRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/analytics/endpoints/usage.api-requests-count", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1GetProjectApiKeysRequest generates requests for V1GetProjectApiKeys
func NewV1GetProjectApiKeysRequest(server string, ref string, params *V1GetProjectApiKeysParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/api-keys", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Reveal != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "reveal", runtime.ParamLocationQuery, *params.Reveal); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewCreateApiKeyRequest calls the generic CreateApiKey builder with application/json body
func NewCreateApiKeyRequest(server string, ref string, params *CreateApiKeyParams, body CreateApiKeyJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewCreateApiKeyRequestWithBody(server, ref, params, "application/json", bodyReader)
}

// NewCreateApiKeyRequestWithBody generates requests for CreateApiKey with any type of body
func NewCreateApiKeyRequestWithBody(server string, ref string, params *CreateApiKeyParams, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/api-keys", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Reveal != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "reveal", runtime.ParamLocationQuery, *params.Reveal); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteApiKeyRequest generates requests for DeleteApiKey
func NewDeleteApiKeyRequest(server string, ref string, id openapi_types.UUID, params *DeleteApiKeyParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/api-keys/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Reveal != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "reveal", runtime.ParamLocationQuery, *params.Reveal); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetApiKeyRequest generates requests for GetApiKey
func NewGetApiKeyRequest(server string, ref string, id openapi_types.UUID, params *GetApiKeyParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/api-keys/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Reveal != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "reveal", runtime.ParamLocationQuery, *params.Reveal); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewUpdateApiKeyRequest calls the generic UpdateApiKey builder with application/json body
func NewUpdateApiKeyRequest(server string, ref string, id openapi_types.UUID, params *UpdateApiKeyParams, body UpdateApiKeyJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewUpdateApiKeyRequestWithBody(server, ref, id, params, "application/json", bodyReader)
}

// NewUpdateApiKeyRequestWithBody generates requests for UpdateApiKey with any type of body
func NewUpdateApiKeyRequestWithBody(server string, ref string, id openapi_types.UUID, params *UpdateApiKeyParams, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/api-keys/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Reveal != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "reveal", runtime.ParamLocationQuery, *params.Reveal); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1ListProjectAddonsRequest generates requests for V1ListProjectAddons
func NewV1ListProjectAddonsRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/billing/addons", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1ApplyProjectAddonRequest calls the generic V1ApplyProjectAddon builder with application/json body
func NewV1ApplyProjectAddonRequest(server string, ref string, body V1ApplyProjectAddonJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1ApplyProjectAddonRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1ApplyProjectAddonRequestWithBody generates requests for V1ApplyProjectAddon with any type of body
func NewV1ApplyProjectAddonRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/billing/addons", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1RemoveProjectAddonRequest generates requests for V1RemoveProjectAddon
func NewV1RemoveProjectAddonRequest(server string, ref string, addonVariant interface{}) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "addon_variant", runtime.ParamLocationPath, addonVariant)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/billing/addons/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1DisablePreviewBranchingRequest generates requests for V1DisablePreviewBranching
func NewV1DisablePreviewBranchingRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/branches", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1ListAllBranchesRequest generates requests for V1ListAllBranches
func NewV1ListAllBranchesRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/branches", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1CreateABranchRequest calls the generic V1CreateABranch builder with application/json body
func NewV1CreateABranchRequest(server string, ref string, body V1CreateABranchJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1CreateABranchRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1CreateABranchRequestWithBody generates requests for V1CreateABranch with any type of body
func NewV1CreateABranchRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/branches", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1DeleteProjectClaimTokenRequest generates requests for V1DeleteProjectClaimToken
func NewV1DeleteProjectClaimTokenRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/claim-token", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1GetProjectClaimTokenRequest generates requests for V1GetProjectClaimToken
func NewV1GetProjectClaimTokenRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/claim-token", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1CreateProjectClaimTokenRequest generates requests for V1CreateProjectClaimToken
func NewV1CreateProjectClaimTokenRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/claim-token", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1GetAuthServiceConfigRequest generates requests for V1GetAuthServiceConfig
func NewV1GetAuthServiceConfigRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/auth", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1UpdateAuthServiceConfigRequest calls the generic V1UpdateAuthServiceConfig builder with application/json body
func NewV1UpdateAuthServiceConfigRequest(server string, ref string, body V1UpdateAuthServiceConfigJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1UpdateAuthServiceConfigRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1UpdateAuthServiceConfigRequestWithBody generates requests for V1UpdateAuthServiceConfig with any type of body
func NewV1UpdateAuthServiceConfigRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/auth", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewListSigningKeysForProjectRequest generates requests for ListSigningKeysForProject
func NewListSigningKeysForProjectRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/auth/signing-keys", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewCreateSigningKeyForProjectRequest calls the generic CreateSigningKeyForProject builder with application/json body
func NewCreateSigningKeyForProjectRequest(server string, ref string, body CreateSigningKeyForProjectJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewCreateSigningKeyForProjectRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewCreateSigningKeyForProjectRequestWithBody generates requests for CreateSigningKeyForProject with any type of body
func NewCreateSigningKeyForProjectRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/auth/signing-keys", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteSigningKeyRequest generates requests for DeleteSigningKey
func NewDeleteSigningKeyRequest(server string, ref string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/auth/signing-keys/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetSigningKeyForProjectRequest generates requests for GetSigningKeyForProject
func NewGetSigningKeyForProjectRequest(server string, ref string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/auth/signing-keys/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPatchSigningKeyRequest calls the generic PatchSigningKey builder with application/json body
func NewPatchSigningKeyRequest(server string, ref string, id openapi_types.UUID, body PatchSigningKeyJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPatchSigningKeyRequestWithBody(server, ref, id, "application/json", bodyReader)
}

// NewPatchSigningKeyRequestWithBody generates requests for PatchSigningKey with any type of body
func NewPatchSigningKeyRequestWithBody(server string, ref string, id openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/auth/signing-keys/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1ListAllSsoProviderRequest generates requests for V1ListAllSsoProvider
func NewV1ListAllSsoProviderRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/auth/sso/providers", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1CreateASsoProviderRequest calls the generic V1CreateASsoProvider builder with application/json body
func NewV1CreateASsoProviderRequest(server string, ref string, body V1CreateASsoProviderJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1CreateASsoProviderRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1CreateASsoProviderRequestWithBody generates requests for V1CreateASsoProvider with any type of body
func NewV1CreateASsoProviderRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/auth/sso/providers", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1DeleteASsoProviderRequest generates requests for V1DeleteASsoProvider
func NewV1DeleteASsoProviderRequest(server string, ref string, providerId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "provider_id", runtime.ParamLocationPath, providerId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/auth/sso/providers/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1GetASsoProviderRequest generates requests for V1GetASsoProvider
func NewV1GetASsoProviderRequest(server string, ref string, providerId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "provider_id", runtime.ParamLocationPath, providerId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/auth/sso/providers/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1UpdateASsoProviderRequest calls the generic V1UpdateASsoProvider builder with application/json body
func NewV1UpdateASsoProviderRequest(server string, ref string, providerId openapi_types.UUID, body V1UpdateASsoProviderJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1UpdateASsoProviderRequestWithBody(server, ref, providerId, "application/json", bodyReader)
}

// NewV1UpdateASsoProviderRequestWithBody generates requests for V1UpdateASsoProvider with any type of body
func NewV1UpdateASsoProviderRequestWithBody(server string, ref string, providerId openapi_types.UUID, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "provider_id", runtime.ParamLocationPath, providerId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/auth/sso/providers/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewListTPAForProjectRequest generates requests for ListTPAForProject
func NewListTPAForProjectRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/auth/third-party-auth", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewCreateTPAForProjectRequest calls the generic CreateTPAForProject builder with application/json body
func NewCreateTPAForProjectRequest(server string, ref string, body CreateTPAForProjectJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewCreateTPAForProjectRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewCreateTPAForProjectRequestWithBody generates requests for CreateTPAForProject with any type of body
func NewCreateTPAForProjectRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/auth/third-party-auth", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewDeleteTPAForProjectRequest generates requests for DeleteTPAForProject
func NewDeleteTPAForProjectRequest(server string, ref string, tpaId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "tpa_id", runtime.ParamLocationPath, tpaId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/auth/third-party-auth/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetTPAForProjectRequest generates requests for GetTPAForProject
func NewGetTPAForProjectRequest(server string, ref string, tpaId openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "tpa_id", runtime.ParamLocationPath, tpaId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/auth/third-party-auth/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1GetProjectPgbouncerConfigRequest generates requests for V1GetProjectPgbouncerConfig
func NewV1GetProjectPgbouncerConfigRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/database/pgbouncer", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1GetPoolerConfigRequest generates requests for V1GetPoolerConfig
func NewV1GetPoolerConfigRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/database/pooler", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1UpdatePoolerConfigRequest calls the generic V1UpdatePoolerConfig builder with application/json body
func NewV1UpdatePoolerConfigRequest(server string, ref string, body V1UpdatePoolerConfigJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1UpdatePoolerConfigRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1UpdatePoolerConfigRequestWithBody generates requests for V1UpdatePoolerConfig with any type of body
func NewV1UpdatePoolerConfigRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/database/pooler", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1GetPostgresConfigRequest generates requests for V1GetPostgresConfig
func NewV1GetPostgresConfigRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/database/postgres", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1UpdatePostgresConfigRequest calls the generic V1UpdatePostgresConfig builder with application/json body
func NewV1UpdatePostgresConfigRequest(server string, ref string, body V1UpdatePostgresConfigJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1UpdatePostgresConfigRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1UpdatePostgresConfigRequestWithBody generates requests for V1UpdatePostgresConfig with any type of body
func NewV1UpdatePostgresConfigRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/database/postgres", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1GetStorageConfigRequest generates requests for V1GetStorageConfig
func NewV1GetStorageConfigRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/storage", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1UpdateStorageConfigRequest calls the generic V1UpdateStorageConfig builder with application/json body
func NewV1UpdateStorageConfigRequest(server string, ref string, body V1UpdateStorageConfigJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1UpdateStorageConfigRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1UpdateStorageConfigRequestWithBody generates requests for V1UpdateStorageConfig with any type of body
func NewV1UpdateStorageConfigRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/config/storage", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1DeleteHostnameConfigRequest generates requests for V1DeleteHostnameConfig
func NewV1DeleteHostnameConfigRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/custom-hostname", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1GetHostnameConfigRequest generates requests for V1GetHostnameConfig
func NewV1GetHostnameConfigRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/custom-hostname", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1ActivateCustomHostnameRequest generates requests for V1ActivateCustomHostname
func NewV1ActivateCustomHostnameRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/custom-hostname/activate", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1UpdateHostnameConfigRequest calls the generic V1UpdateHostnameConfig builder with application/json body
func NewV1UpdateHostnameConfigRequest(server string, ref string, body V1UpdateHostnameConfigJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1UpdateHostnameConfigRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1UpdateHostnameConfigRequestWithBody generates requests for V1UpdateHostnameConfig with any type of body
func NewV1UpdateHostnameConfigRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/custom-hostname/initialize", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1VerifyDnsConfigRequest generates requests for V1VerifyDnsConfig
func NewV1VerifyDnsConfigRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/custom-hostname/reverify", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1ListAllBackupsRequest generates requests for V1ListAllBackups
func NewV1ListAllBackupsRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/database/backups", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1RestorePitrBackupRequest calls the generic V1RestorePitrBackup builder with application/json body
func NewV1RestorePitrBackupRequest(server string, ref string, body V1RestorePitrBackupJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1RestorePitrBackupRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1RestorePitrBackupRequestWithBody generates requests for V1RestorePitrBackup with any type of body
func NewV1RestorePitrBackupRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/database/backups/restore-pitr", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetDatabaseMetadataRequest generates requests for GetDatabaseMetadata
func NewGetDatabaseMetadataRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/database/context", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1ListMigrationHistoryRequest generates requests for V1ListMigrationHistory
func NewV1ListMigrationHistoryRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/database/migrations", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1ApplyAMigrationRequest calls the generic V1ApplyAMigration builder with application/json body
func NewV1ApplyAMigrationRequest(server string, ref string, params *V1ApplyAMigrationParams, body V1ApplyAMigrationJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1ApplyAMigrationRequestWithBody(server, ref, params, "application/json", bodyReader)
}

// NewV1ApplyAMigrationRequestWithBody generates requests for V1ApplyAMigration with any type of body
func NewV1ApplyAMigrationRequestWithBody(server string, ref string, params *V1ApplyAMigrationParams, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/database/migrations", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	if params != nil {

		if params.IdempotencyKey != nil {
			var headerParam0 string

			headerParam0, err = runtime.StyleParamWithLocation("simple", false, "Idempotency-Key", runtime.ParamLocationHeader, *params.IdempotencyKey)
			if err != nil {
				return nil, err
			}

			req.Header.Set("Idempotency-Key", headerParam0)
		}

	}

	return req, nil
}

// NewV1RunAQueryRequest calls the generic V1RunAQuery builder with application/json body
func NewV1RunAQueryRequest(server string, ref string, body V1RunAQueryJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1RunAQueryRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1RunAQueryRequestWithBody generates requests for V1RunAQuery with any type of body
func NewV1RunAQueryRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/database/query", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1EnableDatabaseWebhookRequest generates requests for V1EnableDatabaseWebhook
func NewV1EnableDatabaseWebhookRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/database/webhooks/enable", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1ListAllFunctionsRequest generates requests for V1ListAllFunctions
func NewV1ListAllFunctionsRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/functions", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1CreateAFunctionRequest calls the generic V1CreateAFunction builder with application/json body
func NewV1CreateAFunctionRequest(server string, ref string, params *V1CreateAFunctionParams, body V1CreateAFunctionJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1CreateAFunctionRequestWithBody(server, ref, params, "application/json", bodyReader)
}

// NewV1CreateAFunctionRequestWithBody generates requests for V1CreateAFunction with any type of body
func NewV1CreateAFunctionRequestWithBody(server string, ref string, params *V1CreateAFunctionParams, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/functions", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Slug != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "slug", runtime.ParamLocationQuery, *params.Slug); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Name != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "name", runtime.ParamLocationQuery, *params.Name); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.VerifyJwt != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "verify_jwt", runtime.ParamLocationQuery, *params.VerifyJwt); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.ImportMap != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "import_map", runtime.ParamLocationQuery, *params.ImportMap); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.EntrypointPath != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "entrypoint_path", runtime.ParamLocationQuery, *params.EntrypointPath); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.ImportMapPath != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "import_map_path", runtime.ParamLocationQuery, *params.ImportMapPath); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1BulkUpdateFunctionsRequest calls the generic V1BulkUpdateFunctions builder with application/json body
func NewV1BulkUpdateFunctionsRequest(server string, ref string, body V1BulkUpdateFunctionsJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1BulkUpdateFunctionsRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1BulkUpdateFunctionsRequestWithBody generates requests for V1BulkUpdateFunctions with any type of body
func NewV1BulkUpdateFunctionsRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/functions", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1DeployAFunctionRequestWithBody generates requests for V1DeployAFunction with any type of body
func NewV1DeployAFunctionRequestWithBody(server string, ref string, params *V1DeployAFunctionParams, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/functions/deploy", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Slug != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "slug", runtime.ParamLocationQuery, *params.Slug); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.BundleOnly != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "bundleOnly", runtime.ParamLocationQuery, *params.BundleOnly); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1DeleteAFunctionRequest generates requests for V1DeleteAFunction
func NewV1DeleteAFunctionRequest(server string, ref string, functionSlug string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "function_slug", runtime.ParamLocationPath, functionSlug)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/functions/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1GetAFunctionRequest generates requests for V1GetAFunction
func NewV1GetAFunctionRequest(server string, ref string, functionSlug string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "function_slug", runtime.ParamLocationPath, functionSlug)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/functions/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1UpdateAFunctionRequest calls the generic V1UpdateAFunction builder with application/json body
func NewV1UpdateAFunctionRequest(server string, ref string, functionSlug string, params *V1UpdateAFunctionParams, body V1UpdateAFunctionJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1UpdateAFunctionRequestWithBody(server, ref, functionSlug, params, "application/json", bodyReader)
}

// NewV1UpdateAFunctionRequestWithBody generates requests for V1UpdateAFunction with any type of body
func NewV1UpdateAFunctionRequestWithBody(server string, ref string, functionSlug string, params *V1UpdateAFunctionParams, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "function_slug", runtime.ParamLocationPath, functionSlug)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/functions/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Slug != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "slug", runtime.ParamLocationQuery, *params.Slug); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Name != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "name", runtime.ParamLocationQuery, *params.Name); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.VerifyJwt != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "verify_jwt", runtime.ParamLocationQuery, *params.VerifyJwt); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.ImportMap != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "import_map", runtime.ParamLocationQuery, *params.ImportMap); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.EntrypointPath != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "entrypoint_path", runtime.ParamLocationQuery, *params.EntrypointPath); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.ImportMapPath != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "import_map_path", runtime.ParamLocationQuery, *params.ImportMapPath); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1GetAFunctionBodyRequest generates requests for V1GetAFunctionBody
func NewV1GetAFunctionBodyRequest(server string, ref string, functionSlug string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "function_slug", runtime.ParamLocationPath, functionSlug)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/functions/%s/body", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1GetServicesHealthRequest generates requests for V1GetServicesHealth
func NewV1GetServicesHealthRequest(server string, ref string, params *V1GetServicesHealthParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/health", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "services", runtime.ParamLocationQuery, params.Services); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		if params.TimeoutMs != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "timeout_ms", runtime.ParamLocationQuery, *params.TimeoutMs); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1DeleteNetworkBansRequest calls the generic V1DeleteNetworkBans builder with application/json body
func NewV1DeleteNetworkBansRequest(server string, ref string, body V1DeleteNetworkBansJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1DeleteNetworkBansRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1DeleteNetworkBansRequestWithBody generates requests for V1DeleteNetworkBans with any type of body
func NewV1DeleteNetworkBansRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/network-bans", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1ListAllNetworkBansRequest generates requests for V1ListAllNetworkBans
func NewV1ListAllNetworkBansRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/network-bans/retrieve", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1ListAllNetworkBansEnrichedRequest generates requests for V1ListAllNetworkBansEnriched
func NewV1ListAllNetworkBansEnrichedRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/network-bans/retrieve/enriched", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1GetNetworkRestrictionsRequest generates requests for V1GetNetworkRestrictions
func NewV1GetNetworkRestrictionsRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/network-restrictions", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1UpdateNetworkRestrictionsRequest calls the generic V1UpdateNetworkRestrictions builder with application/json body
func NewV1UpdateNetworkRestrictionsRequest(server string, ref string, body V1UpdateNetworkRestrictionsJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1UpdateNetworkRestrictionsRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1UpdateNetworkRestrictionsRequestWithBody generates requests for V1UpdateNetworkRestrictions with any type of body
func NewV1UpdateNetworkRestrictionsRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/network-restrictions/apply", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1PauseAProjectRequest generates requests for V1PauseAProject
func NewV1PauseAProjectRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/pause", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1GetPgsodiumConfigRequest generates requests for V1GetPgsodiumConfig
func NewV1GetPgsodiumConfigRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/pgsodium", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1UpdatePgsodiumConfigRequest calls the generic V1UpdatePgsodiumConfig builder with application/json body
func NewV1UpdatePgsodiumConfigRequest(server string, ref string, body V1UpdatePgsodiumConfigJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1UpdatePgsodiumConfigRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1UpdatePgsodiumConfigRequestWithBody generates requests for V1UpdatePgsodiumConfig with any type of body
func NewV1UpdatePgsodiumConfigRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/pgsodium", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1GetPostgrestServiceConfigRequest generates requests for V1GetPostgrestServiceConfig
func NewV1GetPostgrestServiceConfigRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/postgrest", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1UpdatePostgrestServiceConfigRequest calls the generic V1UpdatePostgrestServiceConfig builder with application/json body
func NewV1UpdatePostgrestServiceConfigRequest(server string, ref string, body V1UpdatePostgrestServiceConfigJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1UpdatePostgrestServiceConfigRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1UpdatePostgrestServiceConfigRequestWithBody generates requests for V1UpdatePostgrestServiceConfig with any type of body
func NewV1UpdatePostgrestServiceConfigRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/postgrest", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1RemoveAReadReplicaRequest calls the generic V1RemoveAReadReplica builder with application/json body
func NewV1RemoveAReadReplicaRequest(server string, ref string, body V1RemoveAReadReplicaJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1RemoveAReadReplicaRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1RemoveAReadReplicaRequestWithBody generates requests for V1RemoveAReadReplica with any type of body
func NewV1RemoveAReadReplicaRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/read-replicas/remove", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1SetupAReadReplicaRequest calls the generic V1SetupAReadReplica builder with application/json body
func NewV1SetupAReadReplicaRequest(server string, ref string, body V1SetupAReadReplicaJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1SetupAReadReplicaRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1SetupAReadReplicaRequestWithBody generates requests for V1SetupAReadReplica with any type of body
func NewV1SetupAReadReplicaRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/read-replicas/setup", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1GetReadonlyModeStatusRequest generates requests for V1GetReadonlyModeStatus
func NewV1GetReadonlyModeStatusRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/readonly", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1DisableReadonlyModeTemporarilyRequest generates requests for V1DisableReadonlyModeTemporarily
func NewV1DisableReadonlyModeTemporarilyRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/readonly/temporary-disable", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1ListAvailableRestoreVersionsRequest generates requests for V1ListAvailableRestoreVersions
func NewV1ListAvailableRestoreVersionsRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/restore", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1RestoreAProjectRequest generates requests for V1RestoreAProject
func NewV1RestoreAProjectRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/restore", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1CancelAProjectRestorationRequest generates requests for V1CancelAProjectRestoration
func NewV1CancelAProjectRestorationRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/restore/cancel", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1BulkDeleteSecretsRequest calls the generic V1BulkDeleteSecrets builder with application/json body
func NewV1BulkDeleteSecretsRequest(server string, ref string, body V1BulkDeleteSecretsJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1BulkDeleteSecretsRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1BulkDeleteSecretsRequestWithBody generates requests for V1BulkDeleteSecrets with any type of body
func NewV1BulkDeleteSecretsRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/secrets", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1ListAllSecretsRequest generates requests for V1ListAllSecrets
func NewV1ListAllSecretsRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/secrets", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1BulkCreateSecretsRequest calls the generic V1BulkCreateSecrets builder with application/json body
func NewV1BulkCreateSecretsRequest(server string, ref string, body V1BulkCreateSecretsJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1BulkCreateSecretsRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1BulkCreateSecretsRequestWithBody generates requests for V1BulkCreateSecrets with any type of body
func NewV1BulkCreateSecretsRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/secrets", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1GetSslEnforcementConfigRequest generates requests for V1GetSslEnforcementConfig
func NewV1GetSslEnforcementConfigRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/ssl-enforcement", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1UpdateSslEnforcementConfigRequest calls the generic V1UpdateSslEnforcementConfig builder with application/json body
func NewV1UpdateSslEnforcementConfigRequest(server string, ref string, body V1UpdateSslEnforcementConfigJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1UpdateSslEnforcementConfigRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1UpdateSslEnforcementConfigRequestWithBody generates requests for V1UpdateSslEnforcementConfig with any type of body
func NewV1UpdateSslEnforcementConfigRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/ssl-enforcement", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1ListAllBucketsRequest generates requests for V1ListAllBuckets
func NewV1ListAllBucketsRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/storage/buckets", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1GenerateTypescriptTypesRequest generates requests for V1GenerateTypescriptTypes
func NewV1GenerateTypescriptTypesRequest(server string, ref string, params *V1GenerateTypescriptTypesParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/types/typescript", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.IncludedSchemas != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "included_schemas", runtime.ParamLocationQuery, *params.IncludedSchemas); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1UpgradePostgresVersionRequest calls the generic V1UpgradePostgresVersion builder with application/json body
func NewV1UpgradePostgresVersionRequest(server string, ref string, body V1UpgradePostgresVersionJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1UpgradePostgresVersionRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1UpgradePostgresVersionRequestWithBody generates requests for V1UpgradePostgresVersion with any type of body
func NewV1UpgradePostgresVersionRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/upgrade", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1GetPostgresUpgradeEligibilityRequest generates requests for V1GetPostgresUpgradeEligibility
func NewV1GetPostgresUpgradeEligibilityRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/upgrade/eligibility", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1GetPostgresUpgradeStatusRequest generates requests for V1GetPostgresUpgradeStatus
func NewV1GetPostgresUpgradeStatusRequest(server string, ref string, params *V1GetPostgresUpgradeStatusParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/upgrade/status", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.TrackingId != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "tracking_id", runtime.ParamLocationQuery, *params.TrackingId); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1DeactivateVanitySubdomainConfigRequest generates requests for V1DeactivateVanitySubdomainConfig
func NewV1DeactivateVanitySubdomainConfigRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/vanity-subdomain", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1GetVanitySubdomainConfigRequest generates requests for V1GetVanitySubdomainConfig
func NewV1GetVanitySubdomainConfigRequest(server string, ref string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/vanity-subdomain", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1ActivateVanitySubdomainConfigRequest calls the generic V1ActivateVanitySubdomainConfig builder with application/json body
func NewV1ActivateVanitySubdomainConfigRequest(server string, ref string, body V1ActivateVanitySubdomainConfigJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1ActivateVanitySubdomainConfigRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1ActivateVanitySubdomainConfigRequestWithBody generates requests for V1ActivateVanitySubdomainConfig with any type of body
func NewV1ActivateVanitySubdomainConfigRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/vanity-subdomain/activate", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1CheckVanitySubdomainAvailabilityRequest calls the generic V1CheckVanitySubdomainAvailability builder with application/json body
func NewV1CheckVanitySubdomainAvailabilityRequest(server string, ref string, body V1CheckVanitySubdomainAvailabilityJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewV1CheckVanitySubdomainAvailabilityRequestWithBody(server, ref, "application/json", bodyReader)
}

// NewV1CheckVanitySubdomainAvailabilityRequestWithBody generates requests for V1CheckVanitySubdomainAvailability with any type of body
func NewV1CheckVanitySubdomainAvailabilityRequestWithBody(server string, ref string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "ref", runtime.ParamLocationPath, ref)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/projects/%s/vanity-subdomain/check-availability", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewV1ListAllSnippetsRequest generates requests for V1ListAllSnippets
func NewV1ListAllSnippetsRequest(server string, params *V1ListAllSnippetsParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/snippets")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.ProjectRef != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "project_ref", runtime.ParamLocationQuery, *params.ProjectRef); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Cursor != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "cursor", runtime.ParamLocationQuery, *params.Cursor); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.SortBy != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "sort_by", runtime.ParamLocationQuery, *params.SortBy); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.SortOrder != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "sort_order", runtime.ParamLocationQuery, *params.SortOrder); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewV1GetASnippetRequest generates requests for V1GetASnippet
func NewV1GetASnippetRequest(server string, id openapi_types.UUID) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "id", runtime.ParamLocationPath, id)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1/snippets/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

func (c *Client) applyEditors(ctx context.Context, req *http.Request, additionalEditors []RequestEditorFn) error {
	for _, r := range c.RequestEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	for _, r := range additionalEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	return nil
}

// ClientWithResponses builds on ClientInterface to offer response payloads
type ClientWithResponses struct {
	ClientInterface
}

// NewClientWithResponses creates a new ClientWithResponses, which wraps
// Client with return type handling
func NewClientWithResponses(server string, opts ...ClientOption) (*ClientWithResponses, error) {
	client, err := NewClient(server, opts...)
	if err != nil {
		return nil, err
	}
	return &ClientWithResponses{client}, nil
}

// WithBaseURL overrides the baseURL.
func WithBaseURL(baseURL string) ClientOption {
	return func(c *Client) error {
		newBaseURL, err := url.Parse(baseURL)
		if err != nil {
			return err
		}
		c.Server = newBaseURL.String()
		return nil
	}
}

// ClientWithResponsesInterface is the interface specification for the client with responses above.
type ClientWithResponsesInterface interface {
	// V1DeleteABranchWithResponse request
	V1DeleteABranchWithResponse(ctx context.Context, branchId openapi_types.UUID, reqEditors ...RequestEditorFn) (*V1DeleteABranchResponse, error)

	// V1GetABranchConfigWithResponse request
	V1GetABranchConfigWithResponse(ctx context.Context, branchId openapi_types.UUID, reqEditors ...RequestEditorFn) (*V1GetABranchConfigResponse, error)

	// V1UpdateABranchConfigWithBodyWithResponse request with any body
	V1UpdateABranchConfigWithBodyWithResponse(ctx context.Context, branchId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdateABranchConfigResponse, error)

	V1UpdateABranchConfigWithResponse(ctx context.Context, branchId openapi_types.UUID, body V1UpdateABranchConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdateABranchConfigResponse, error)

	// V1DiffABranchWithResponse request
	V1DiffABranchWithResponse(ctx context.Context, branchId openapi_types.UUID, params *V1DiffABranchParams, reqEditors ...RequestEditorFn) (*V1DiffABranchResponse, error)

	// V1MergeABranchWithBodyWithResponse request with any body
	V1MergeABranchWithBodyWithResponse(ctx context.Context, branchId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1MergeABranchResponse, error)

	V1MergeABranchWithResponse(ctx context.Context, branchId openapi_types.UUID, body V1MergeABranchJSONRequestBody, reqEditors ...RequestEditorFn) (*V1MergeABranchResponse, error)

	// V1PushABranchWithBodyWithResponse request with any body
	V1PushABranchWithBodyWithResponse(ctx context.Context, branchId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1PushABranchResponse, error)

	V1PushABranchWithResponse(ctx context.Context, branchId openapi_types.UUID, body V1PushABranchJSONRequestBody, reqEditors ...RequestEditorFn) (*V1PushABranchResponse, error)

	// V1ResetABranchWithBodyWithResponse request with any body
	V1ResetABranchWithBodyWithResponse(ctx context.Context, branchId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1ResetABranchResponse, error)

	V1ResetABranchWithResponse(ctx context.Context, branchId openapi_types.UUID, body V1ResetABranchJSONRequestBody, reqEditors ...RequestEditorFn) (*V1ResetABranchResponse, error)

	// V1AuthorizeUserWithResponse request
	V1AuthorizeUserWithResponse(ctx context.Context, params *V1AuthorizeUserParams, reqEditors ...RequestEditorFn) (*V1AuthorizeUserResponse, error)

	// V1RevokeTokenWithBodyWithResponse request with any body
	V1RevokeTokenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1RevokeTokenResponse, error)

	V1RevokeTokenWithResponse(ctx context.Context, body V1RevokeTokenJSONRequestBody, reqEditors ...RequestEditorFn) (*V1RevokeTokenResponse, error)

	// V1ExchangeOauthTokenWithBodyWithResponse request with any body
	V1ExchangeOauthTokenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1ExchangeOauthTokenResponse, error)

	V1ExchangeOauthTokenWithFormdataBodyWithResponse(ctx context.Context, body V1ExchangeOauthTokenFormdataRequestBody, reqEditors ...RequestEditorFn) (*V1ExchangeOauthTokenResponse, error)

	// V1ListAllOrganizationsWithResponse request
	V1ListAllOrganizationsWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*V1ListAllOrganizationsResponse, error)

	// V1CreateAnOrganizationWithBodyWithResponse request with any body
	V1CreateAnOrganizationWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1CreateAnOrganizationResponse, error)

	V1CreateAnOrganizationWithResponse(ctx context.Context, body V1CreateAnOrganizationJSONRequestBody, reqEditors ...RequestEditorFn) (*V1CreateAnOrganizationResponse, error)

	// V1GetAnOrganizationWithResponse request
	V1GetAnOrganizationWithResponse(ctx context.Context, slug string, reqEditors ...RequestEditorFn) (*V1GetAnOrganizationResponse, error)

	// V1ListOrganizationMembersWithResponse request
	V1ListOrganizationMembersWithResponse(ctx context.Context, slug string, reqEditors ...RequestEditorFn) (*V1ListOrganizationMembersResponse, error)

	// V1GetOrganizationProjectClaimWithResponse request
	V1GetOrganizationProjectClaimWithResponse(ctx context.Context, slug string, token string, reqEditors ...RequestEditorFn) (*V1GetOrganizationProjectClaimResponse, error)

	// V1ClaimProjectForOrganizationWithResponse request
	V1ClaimProjectForOrganizationWithResponse(ctx context.Context, slug string, token string, reqEditors ...RequestEditorFn) (*V1ClaimProjectForOrganizationResponse, error)

	// V1ListAllProjectsWithResponse request
	V1ListAllProjectsWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*V1ListAllProjectsResponse, error)

	// V1CreateAProjectWithBodyWithResponse request with any body
	V1CreateAProjectWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1CreateAProjectResponse, error)

	V1CreateAProjectWithResponse(ctx context.Context, body V1CreateAProjectJSONRequestBody, reqEditors ...RequestEditorFn) (*V1CreateAProjectResponse, error)

	// V1DeleteAProjectWithResponse request
	V1DeleteAProjectWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1DeleteAProjectResponse, error)

	// V1GetProjectWithResponse request
	V1GetProjectWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetProjectResponse, error)

	// GetPerformanceAdvisorsWithResponse request
	GetPerformanceAdvisorsWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*GetPerformanceAdvisorsResponse, error)

	// GetSecurityAdvisorsWithResponse request
	GetSecurityAdvisorsWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*GetSecurityAdvisorsResponse, error)

	// GetLogsWithResponse request
	GetLogsWithResponse(ctx context.Context, ref string, params *GetLogsParams, reqEditors ...RequestEditorFn) (*GetLogsResponse, error)

	// GetApiCountsWithResponse request
	GetApiCountsWithResponse(ctx context.Context, ref string, params *GetApiCountsParams, reqEditors ...RequestEditorFn) (*GetApiCountsResponse, error)

	// GetApiRequestsCountWithResponse request
	GetApiRequestsCountWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*GetApiRequestsCountResponse, error)

	// V1GetProjectApiKeysWithResponse request
	V1GetProjectApiKeysWithResponse(ctx context.Context, ref string, params *V1GetProjectApiKeysParams, reqEditors ...RequestEditorFn) (*V1GetProjectApiKeysResponse, error)

	// CreateApiKeyWithBodyWithResponse request with any body
	CreateApiKeyWithBodyWithResponse(ctx context.Context, ref string, params *CreateApiKeyParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreateApiKeyResponse, error)

	CreateApiKeyWithResponse(ctx context.Context, ref string, params *CreateApiKeyParams, body CreateApiKeyJSONRequestBody, reqEditors ...RequestEditorFn) (*CreateApiKeyResponse, error)

	// DeleteApiKeyWithResponse request
	DeleteApiKeyWithResponse(ctx context.Context, ref string, id openapi_types.UUID, params *DeleteApiKeyParams, reqEditors ...RequestEditorFn) (*DeleteApiKeyResponse, error)

	// GetApiKeyWithResponse request
	GetApiKeyWithResponse(ctx context.Context, ref string, id openapi_types.UUID, params *GetApiKeyParams, reqEditors ...RequestEditorFn) (*GetApiKeyResponse, error)

	// UpdateApiKeyWithBodyWithResponse request with any body
	UpdateApiKeyWithBodyWithResponse(ctx context.Context, ref string, id openapi_types.UUID, params *UpdateApiKeyParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApiKeyResponse, error)

	UpdateApiKeyWithResponse(ctx context.Context, ref string, id openapi_types.UUID, params *UpdateApiKeyParams, body UpdateApiKeyJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApiKeyResponse, error)

	// V1ListProjectAddonsWithResponse request
	V1ListProjectAddonsWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListProjectAddonsResponse, error)

	// V1ApplyProjectAddonWithBodyWithResponse request with any body
	V1ApplyProjectAddonWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1ApplyProjectAddonResponse, error)

	V1ApplyProjectAddonWithResponse(ctx context.Context, ref string, body V1ApplyProjectAddonJSONRequestBody, reqEditors ...RequestEditorFn) (*V1ApplyProjectAddonResponse, error)

	// V1RemoveProjectAddonWithResponse request
	V1RemoveProjectAddonWithResponse(ctx context.Context, ref string, addonVariant interface{}, reqEditors ...RequestEditorFn) (*V1RemoveProjectAddonResponse, error)

	// V1DisablePreviewBranchingWithResponse request
	V1DisablePreviewBranchingWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1DisablePreviewBranchingResponse, error)

	// V1ListAllBranchesWithResponse request
	V1ListAllBranchesWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListAllBranchesResponse, error)

	// V1CreateABranchWithBodyWithResponse request with any body
	V1CreateABranchWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1CreateABranchResponse, error)

	V1CreateABranchWithResponse(ctx context.Context, ref string, body V1CreateABranchJSONRequestBody, reqEditors ...RequestEditorFn) (*V1CreateABranchResponse, error)

	// V1DeleteProjectClaimTokenWithResponse request
	V1DeleteProjectClaimTokenWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1DeleteProjectClaimTokenResponse, error)

	// V1GetProjectClaimTokenWithResponse request
	V1GetProjectClaimTokenWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetProjectClaimTokenResponse, error)

	// V1CreateProjectClaimTokenWithResponse request
	V1CreateProjectClaimTokenWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1CreateProjectClaimTokenResponse, error)

	// V1GetAuthServiceConfigWithResponse request
	V1GetAuthServiceConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetAuthServiceConfigResponse, error)

	// V1UpdateAuthServiceConfigWithBodyWithResponse request with any body
	V1UpdateAuthServiceConfigWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdateAuthServiceConfigResponse, error)

	V1UpdateAuthServiceConfigWithResponse(ctx context.Context, ref string, body V1UpdateAuthServiceConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdateAuthServiceConfigResponse, error)

	// ListSigningKeysForProjectWithResponse request
	ListSigningKeysForProjectWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*ListSigningKeysForProjectResponse, error)

	// CreateSigningKeyForProjectWithBodyWithResponse request with any body
	CreateSigningKeyForProjectWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreateSigningKeyForProjectResponse, error)

	CreateSigningKeyForProjectWithResponse(ctx context.Context, ref string, body CreateSigningKeyForProjectJSONRequestBody, reqEditors ...RequestEditorFn) (*CreateSigningKeyForProjectResponse, error)

	// DeleteSigningKeyWithResponse request
	DeleteSigningKeyWithResponse(ctx context.Context, ref string, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteSigningKeyResponse, error)

	// GetSigningKeyForProjectWithResponse request
	GetSigningKeyForProjectWithResponse(ctx context.Context, ref string, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetSigningKeyForProjectResponse, error)

	// PatchSigningKeyWithBodyWithResponse request with any body
	PatchSigningKeyWithBodyWithResponse(ctx context.Context, ref string, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PatchSigningKeyResponse, error)

	PatchSigningKeyWithResponse(ctx context.Context, ref string, id openapi_types.UUID, body PatchSigningKeyJSONRequestBody, reqEditors ...RequestEditorFn) (*PatchSigningKeyResponse, error)

	// V1ListAllSsoProviderWithResponse request
	V1ListAllSsoProviderWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListAllSsoProviderResponse, error)

	// V1CreateASsoProviderWithBodyWithResponse request with any body
	V1CreateASsoProviderWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1CreateASsoProviderResponse, error)

	V1CreateASsoProviderWithResponse(ctx context.Context, ref string, body V1CreateASsoProviderJSONRequestBody, reqEditors ...RequestEditorFn) (*V1CreateASsoProviderResponse, error)

	// V1DeleteASsoProviderWithResponse request
	V1DeleteASsoProviderWithResponse(ctx context.Context, ref string, providerId openapi_types.UUID, reqEditors ...RequestEditorFn) (*V1DeleteASsoProviderResponse, error)

	// V1GetASsoProviderWithResponse request
	V1GetASsoProviderWithResponse(ctx context.Context, ref string, providerId openapi_types.UUID, reqEditors ...RequestEditorFn) (*V1GetASsoProviderResponse, error)

	// V1UpdateASsoProviderWithBodyWithResponse request with any body
	V1UpdateASsoProviderWithBodyWithResponse(ctx context.Context, ref string, providerId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdateASsoProviderResponse, error)

	V1UpdateASsoProviderWithResponse(ctx context.Context, ref string, providerId openapi_types.UUID, body V1UpdateASsoProviderJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdateASsoProviderResponse, error)

	// ListTPAForProjectWithResponse request
	ListTPAForProjectWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*ListTPAForProjectResponse, error)

	// CreateTPAForProjectWithBodyWithResponse request with any body
	CreateTPAForProjectWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreateTPAForProjectResponse, error)

	CreateTPAForProjectWithResponse(ctx context.Context, ref string, body CreateTPAForProjectJSONRequestBody, reqEditors ...RequestEditorFn) (*CreateTPAForProjectResponse, error)

	// DeleteTPAForProjectWithResponse request
	DeleteTPAForProjectWithResponse(ctx context.Context, ref string, tpaId openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteTPAForProjectResponse, error)

	// GetTPAForProjectWithResponse request
	GetTPAForProjectWithResponse(ctx context.Context, ref string, tpaId openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetTPAForProjectResponse, error)

	// V1GetProjectPgbouncerConfigWithResponse request
	V1GetProjectPgbouncerConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetProjectPgbouncerConfigResponse, error)

	// V1GetPoolerConfigWithResponse request
	V1GetPoolerConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetPoolerConfigResponse, error)

	// V1UpdatePoolerConfigWithBodyWithResponse request with any body
	V1UpdatePoolerConfigWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdatePoolerConfigResponse, error)

	V1UpdatePoolerConfigWithResponse(ctx context.Context, ref string, body V1UpdatePoolerConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdatePoolerConfigResponse, error)

	// V1GetPostgresConfigWithResponse request
	V1GetPostgresConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetPostgresConfigResponse, error)

	// V1UpdatePostgresConfigWithBodyWithResponse request with any body
	V1UpdatePostgresConfigWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdatePostgresConfigResponse, error)

	V1UpdatePostgresConfigWithResponse(ctx context.Context, ref string, body V1UpdatePostgresConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdatePostgresConfigResponse, error)

	// V1GetStorageConfigWithResponse request
	V1GetStorageConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetStorageConfigResponse, error)

	// V1UpdateStorageConfigWithBodyWithResponse request with any body
	V1UpdateStorageConfigWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdateStorageConfigResponse, error)

	V1UpdateStorageConfigWithResponse(ctx context.Context, ref string, body V1UpdateStorageConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdateStorageConfigResponse, error)

	// V1DeleteHostnameConfigWithResponse request
	V1DeleteHostnameConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1DeleteHostnameConfigResponse, error)

	// V1GetHostnameConfigWithResponse request
	V1GetHostnameConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetHostnameConfigResponse, error)

	// V1ActivateCustomHostnameWithResponse request
	V1ActivateCustomHostnameWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ActivateCustomHostnameResponse, error)

	// V1UpdateHostnameConfigWithBodyWithResponse request with any body
	V1UpdateHostnameConfigWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdateHostnameConfigResponse, error)

	V1UpdateHostnameConfigWithResponse(ctx context.Context, ref string, body V1UpdateHostnameConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdateHostnameConfigResponse, error)

	// V1VerifyDnsConfigWithResponse request
	V1VerifyDnsConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1VerifyDnsConfigResponse, error)

	// V1ListAllBackupsWithResponse request
	V1ListAllBackupsWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListAllBackupsResponse, error)

	// V1RestorePitrBackupWithBodyWithResponse request with any body
	V1RestorePitrBackupWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1RestorePitrBackupResponse, error)

	V1RestorePitrBackupWithResponse(ctx context.Context, ref string, body V1RestorePitrBackupJSONRequestBody, reqEditors ...RequestEditorFn) (*V1RestorePitrBackupResponse, error)

	// GetDatabaseMetadataWithResponse request
	GetDatabaseMetadataWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*GetDatabaseMetadataResponse, error)

	// V1ListMigrationHistoryWithResponse request
	V1ListMigrationHistoryWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListMigrationHistoryResponse, error)

	// V1ApplyAMigrationWithBodyWithResponse request with any body
	V1ApplyAMigrationWithBodyWithResponse(ctx context.Context, ref string, params *V1ApplyAMigrationParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1ApplyAMigrationResponse, error)

	V1ApplyAMigrationWithResponse(ctx context.Context, ref string, params *V1ApplyAMigrationParams, body V1ApplyAMigrationJSONRequestBody, reqEditors ...RequestEditorFn) (*V1ApplyAMigrationResponse, error)

	// V1RunAQueryWithBodyWithResponse request with any body
	V1RunAQueryWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1RunAQueryResponse, error)

	V1RunAQueryWithResponse(ctx context.Context, ref string, body V1RunAQueryJSONRequestBody, reqEditors ...RequestEditorFn) (*V1RunAQueryResponse, error)

	// V1EnableDatabaseWebhookWithResponse request
	V1EnableDatabaseWebhookWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1EnableDatabaseWebhookResponse, error)

	// V1ListAllFunctionsWithResponse request
	V1ListAllFunctionsWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListAllFunctionsResponse, error)

	// V1CreateAFunctionWithBodyWithResponse request with any body
	V1CreateAFunctionWithBodyWithResponse(ctx context.Context, ref string, params *V1CreateAFunctionParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1CreateAFunctionResponse, error)

	V1CreateAFunctionWithResponse(ctx context.Context, ref string, params *V1CreateAFunctionParams, body V1CreateAFunctionJSONRequestBody, reqEditors ...RequestEditorFn) (*V1CreateAFunctionResponse, error)

	// V1BulkUpdateFunctionsWithBodyWithResponse request with any body
	V1BulkUpdateFunctionsWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1BulkUpdateFunctionsResponse, error)

	V1BulkUpdateFunctionsWithResponse(ctx context.Context, ref string, body V1BulkUpdateFunctionsJSONRequestBody, reqEditors ...RequestEditorFn) (*V1BulkUpdateFunctionsResponse, error)

	// V1DeployAFunctionWithBodyWithResponse request with any body
	V1DeployAFunctionWithBodyWithResponse(ctx context.Context, ref string, params *V1DeployAFunctionParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1DeployAFunctionResponse, error)

	// V1DeleteAFunctionWithResponse request
	V1DeleteAFunctionWithResponse(ctx context.Context, ref string, functionSlug string, reqEditors ...RequestEditorFn) (*V1DeleteAFunctionResponse, error)

	// V1GetAFunctionWithResponse request
	V1GetAFunctionWithResponse(ctx context.Context, ref string, functionSlug string, reqEditors ...RequestEditorFn) (*V1GetAFunctionResponse, error)

	// V1UpdateAFunctionWithBodyWithResponse request with any body
	V1UpdateAFunctionWithBodyWithResponse(ctx context.Context, ref string, functionSlug string, params *V1UpdateAFunctionParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdateAFunctionResponse, error)

	V1UpdateAFunctionWithResponse(ctx context.Context, ref string, functionSlug string, params *V1UpdateAFunctionParams, body V1UpdateAFunctionJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdateAFunctionResponse, error)

	// V1GetAFunctionBodyWithResponse request
	V1GetAFunctionBodyWithResponse(ctx context.Context, ref string, functionSlug string, reqEditors ...RequestEditorFn) (*V1GetAFunctionBodyResponse, error)

	// V1GetServicesHealthWithResponse request
	V1GetServicesHealthWithResponse(ctx context.Context, ref string, params *V1GetServicesHealthParams, reqEditors ...RequestEditorFn) (*V1GetServicesHealthResponse, error)

	// V1DeleteNetworkBansWithBodyWithResponse request with any body
	V1DeleteNetworkBansWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1DeleteNetworkBansResponse, error)

	V1DeleteNetworkBansWithResponse(ctx context.Context, ref string, body V1DeleteNetworkBansJSONRequestBody, reqEditors ...RequestEditorFn) (*V1DeleteNetworkBansResponse, error)

	// V1ListAllNetworkBansWithResponse request
	V1ListAllNetworkBansWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListAllNetworkBansResponse, error)

	// V1ListAllNetworkBansEnrichedWithResponse request
	V1ListAllNetworkBansEnrichedWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListAllNetworkBansEnrichedResponse, error)

	// V1GetNetworkRestrictionsWithResponse request
	V1GetNetworkRestrictionsWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetNetworkRestrictionsResponse, error)

	// V1UpdateNetworkRestrictionsWithBodyWithResponse request with any body
	V1UpdateNetworkRestrictionsWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdateNetworkRestrictionsResponse, error)

	V1UpdateNetworkRestrictionsWithResponse(ctx context.Context, ref string, body V1UpdateNetworkRestrictionsJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdateNetworkRestrictionsResponse, error)

	// V1PauseAProjectWithResponse request
	V1PauseAProjectWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1PauseAProjectResponse, error)

	// V1GetPgsodiumConfigWithResponse request
	V1GetPgsodiumConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetPgsodiumConfigResponse, error)

	// V1UpdatePgsodiumConfigWithBodyWithResponse request with any body
	V1UpdatePgsodiumConfigWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdatePgsodiumConfigResponse, error)

	V1UpdatePgsodiumConfigWithResponse(ctx context.Context, ref string, body V1UpdatePgsodiumConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdatePgsodiumConfigResponse, error)

	// V1GetPostgrestServiceConfigWithResponse request
	V1GetPostgrestServiceConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetPostgrestServiceConfigResponse, error)

	// V1UpdatePostgrestServiceConfigWithBodyWithResponse request with any body
	V1UpdatePostgrestServiceConfigWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdatePostgrestServiceConfigResponse, error)

	V1UpdatePostgrestServiceConfigWithResponse(ctx context.Context, ref string, body V1UpdatePostgrestServiceConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdatePostgrestServiceConfigResponse, error)

	// V1RemoveAReadReplicaWithBodyWithResponse request with any body
	V1RemoveAReadReplicaWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1RemoveAReadReplicaResponse, error)

	V1RemoveAReadReplicaWithResponse(ctx context.Context, ref string, body V1RemoveAReadReplicaJSONRequestBody, reqEditors ...RequestEditorFn) (*V1RemoveAReadReplicaResponse, error)

	// V1SetupAReadReplicaWithBodyWithResponse request with any body
	V1SetupAReadReplicaWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1SetupAReadReplicaResponse, error)

	V1SetupAReadReplicaWithResponse(ctx context.Context, ref string, body V1SetupAReadReplicaJSONRequestBody, reqEditors ...RequestEditorFn) (*V1SetupAReadReplicaResponse, error)

	// V1GetReadonlyModeStatusWithResponse request
	V1GetReadonlyModeStatusWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetReadonlyModeStatusResponse, error)

	// V1DisableReadonlyModeTemporarilyWithResponse request
	V1DisableReadonlyModeTemporarilyWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1DisableReadonlyModeTemporarilyResponse, error)

	// V1ListAvailableRestoreVersionsWithResponse request
	V1ListAvailableRestoreVersionsWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListAvailableRestoreVersionsResponse, error)

	// V1RestoreAProjectWithResponse request
	V1RestoreAProjectWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1RestoreAProjectResponse, error)

	// V1CancelAProjectRestorationWithResponse request
	V1CancelAProjectRestorationWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1CancelAProjectRestorationResponse, error)

	// V1BulkDeleteSecretsWithBodyWithResponse request with any body
	V1BulkDeleteSecretsWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1BulkDeleteSecretsResponse, error)

	V1BulkDeleteSecretsWithResponse(ctx context.Context, ref string, body V1BulkDeleteSecretsJSONRequestBody, reqEditors ...RequestEditorFn) (*V1BulkDeleteSecretsResponse, error)

	// V1ListAllSecretsWithResponse request
	V1ListAllSecretsWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListAllSecretsResponse, error)

	// V1BulkCreateSecretsWithBodyWithResponse request with any body
	V1BulkCreateSecretsWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1BulkCreateSecretsResponse, error)

	V1BulkCreateSecretsWithResponse(ctx context.Context, ref string, body V1BulkCreateSecretsJSONRequestBody, reqEditors ...RequestEditorFn) (*V1BulkCreateSecretsResponse, error)

	// V1GetSslEnforcementConfigWithResponse request
	V1GetSslEnforcementConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetSslEnforcementConfigResponse, error)

	// V1UpdateSslEnforcementConfigWithBodyWithResponse request with any body
	V1UpdateSslEnforcementConfigWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdateSslEnforcementConfigResponse, error)

	V1UpdateSslEnforcementConfigWithResponse(ctx context.Context, ref string, body V1UpdateSslEnforcementConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdateSslEnforcementConfigResponse, error)

	// V1ListAllBucketsWithResponse request
	V1ListAllBucketsWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListAllBucketsResponse, error)

	// V1GenerateTypescriptTypesWithResponse request
	V1GenerateTypescriptTypesWithResponse(ctx context.Context, ref string, params *V1GenerateTypescriptTypesParams, reqEditors ...RequestEditorFn) (*V1GenerateTypescriptTypesResponse, error)

	// V1UpgradePostgresVersionWithBodyWithResponse request with any body
	V1UpgradePostgresVersionWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpgradePostgresVersionResponse, error)

	V1UpgradePostgresVersionWithResponse(ctx context.Context, ref string, body V1UpgradePostgresVersionJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpgradePostgresVersionResponse, error)

	// V1GetPostgresUpgradeEligibilityWithResponse request
	V1GetPostgresUpgradeEligibilityWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetPostgresUpgradeEligibilityResponse, error)

	// V1GetPostgresUpgradeStatusWithResponse request
	V1GetPostgresUpgradeStatusWithResponse(ctx context.Context, ref string, params *V1GetPostgresUpgradeStatusParams, reqEditors ...RequestEditorFn) (*V1GetPostgresUpgradeStatusResponse, error)

	// V1DeactivateVanitySubdomainConfigWithResponse request
	V1DeactivateVanitySubdomainConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1DeactivateVanitySubdomainConfigResponse, error)

	// V1GetVanitySubdomainConfigWithResponse request
	V1GetVanitySubdomainConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetVanitySubdomainConfigResponse, error)

	// V1ActivateVanitySubdomainConfigWithBodyWithResponse request with any body
	V1ActivateVanitySubdomainConfigWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1ActivateVanitySubdomainConfigResponse, error)

	V1ActivateVanitySubdomainConfigWithResponse(ctx context.Context, ref string, body V1ActivateVanitySubdomainConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*V1ActivateVanitySubdomainConfigResponse, error)

	// V1CheckVanitySubdomainAvailabilityWithBodyWithResponse request with any body
	V1CheckVanitySubdomainAvailabilityWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1CheckVanitySubdomainAvailabilityResponse, error)

	V1CheckVanitySubdomainAvailabilityWithResponse(ctx context.Context, ref string, body V1CheckVanitySubdomainAvailabilityJSONRequestBody, reqEditors ...RequestEditorFn) (*V1CheckVanitySubdomainAvailabilityResponse, error)

	// V1ListAllSnippetsWithResponse request
	V1ListAllSnippetsWithResponse(ctx context.Context, params *V1ListAllSnippetsParams, reqEditors ...RequestEditorFn) (*V1ListAllSnippetsResponse, error)

	// V1GetASnippetWithResponse request
	V1GetASnippetWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*V1GetASnippetResponse, error)
}

type V1DeleteABranchResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *BranchDeleteResponse
}

// Status returns HTTPResponse.Status
func (r V1DeleteABranchResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1DeleteABranchResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetABranchConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *BranchDetailResponse
}

// Status returns HTTPResponse.Status
func (r V1GetABranchConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetABranchConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1UpdateABranchConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *BranchResponse
}

// Status returns HTTPResponse.Status
func (r V1UpdateABranchConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1UpdateABranchConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1DiffABranchResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1DiffABranchResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1DiffABranchResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1MergeABranchResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *BranchUpdateResponse
}

// Status returns HTTPResponse.Status
func (r V1MergeABranchResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1MergeABranchResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1PushABranchResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *BranchUpdateResponse
}

// Status returns HTTPResponse.Status
func (r V1PushABranchResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1PushABranchResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ResetABranchResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *BranchUpdateResponse
}

// Status returns HTTPResponse.Status
func (r V1ResetABranchResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ResetABranchResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1AuthorizeUserResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1AuthorizeUserResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1AuthorizeUserResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1RevokeTokenResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1RevokeTokenResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1RevokeTokenResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ExchangeOauthTokenResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *OAuthTokenResponse
}

// Status returns HTTPResponse.Status
func (r V1ExchangeOauthTokenResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ExchangeOauthTokenResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ListAllOrganizationsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]OrganizationResponseV1
}

// Status returns HTTPResponse.Status
func (r V1ListAllOrganizationsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ListAllOrganizationsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1CreateAnOrganizationResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *OrganizationResponseV1
}

// Status returns HTTPResponse.Status
func (r V1CreateAnOrganizationResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1CreateAnOrganizationResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetAnOrganizationResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *V1OrganizationSlugResponse
}

// Status returns HTTPResponse.Status
func (r V1GetAnOrganizationResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetAnOrganizationResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ListOrganizationMembersResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]V1OrganizationMemberResponse
}

// Status returns HTTPResponse.Status
func (r V1ListOrganizationMembersResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ListOrganizationMembersResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetOrganizationProjectClaimResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *OrganizationProjectClaimResponse
}

// Status returns HTTPResponse.Status
func (r V1GetOrganizationProjectClaimResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetOrganizationProjectClaimResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ClaimProjectForOrganizationResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1ClaimProjectForOrganizationResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ClaimProjectForOrganizationResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ListAllProjectsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]V1ProjectWithDatabaseResponse
}

// Status returns HTTPResponse.Status
func (r V1ListAllProjectsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ListAllProjectsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1CreateAProjectResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *V1ProjectResponse
}

// Status returns HTTPResponse.Status
func (r V1CreateAProjectResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1CreateAProjectResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1DeleteAProjectResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *V1ProjectRefResponse
}

// Status returns HTTPResponse.Status
func (r V1DeleteAProjectResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1DeleteAProjectResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetProjectResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *V1ProjectWithDatabaseResponse
}

// Status returns HTTPResponse.Status
func (r V1GetProjectResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetProjectResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetPerformanceAdvisorsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *V1ProjectAdvisorsResponse
}

// Status returns HTTPResponse.Status
func (r GetPerformanceAdvisorsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetPerformanceAdvisorsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetSecurityAdvisorsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *V1ProjectAdvisorsResponse
}

// Status returns HTTPResponse.Status
func (r GetSecurityAdvisorsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetSecurityAdvisorsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetLogsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *AnalyticsResponse
}

// Status returns HTTPResponse.Status
func (r GetLogsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetLogsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApiCountsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *AnalyticsResponse
}

// Status returns HTTPResponse.Status
func (r GetApiCountsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApiCountsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApiRequestsCountResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *AnalyticsResponse
}

// Status returns HTTPResponse.Status
func (r GetApiRequestsCountResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApiRequestsCountResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetProjectApiKeysResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]ApiKeyResponse
}

// Status returns HTTPResponse.Status
func (r V1GetProjectApiKeysResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetProjectApiKeysResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type CreateApiKeyResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *ApiKeyResponse
}

// Status returns HTTPResponse.Status
func (r CreateApiKeyResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r CreateApiKeyResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteApiKeyResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ApiKeyResponse
}

// Status returns HTTPResponse.Status
func (r DeleteApiKeyResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteApiKeyResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApiKeyResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ApiKeyResponse
}

// Status returns HTTPResponse.Status
func (r GetApiKeyResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApiKeyResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type UpdateApiKeyResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ApiKeyResponse
}

// Status returns HTTPResponse.Status
func (r UpdateApiKeyResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r UpdateApiKeyResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ListProjectAddonsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ListProjectAddonsResponse
}

// Status returns HTTPResponse.Status
func (r V1ListProjectAddonsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ListProjectAddonsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ApplyProjectAddonResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1ApplyProjectAddonResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ApplyProjectAddonResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1RemoveProjectAddonResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1RemoveProjectAddonResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1RemoveProjectAddonResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1DisablePreviewBranchingResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1DisablePreviewBranchingResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1DisablePreviewBranchingResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ListAllBranchesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]BranchResponse
}

// Status returns HTTPResponse.Status
func (r V1ListAllBranchesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ListAllBranchesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1CreateABranchResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *BranchResponse
}

// Status returns HTTPResponse.Status
func (r V1CreateABranchResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1CreateABranchResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1DeleteProjectClaimTokenResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1DeleteProjectClaimTokenResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1DeleteProjectClaimTokenResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetProjectClaimTokenResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ProjectClaimTokenResponse
}

// Status returns HTTPResponse.Status
func (r V1GetProjectClaimTokenResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetProjectClaimTokenResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1CreateProjectClaimTokenResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *CreateProjectClaimTokenResponse
}

// Status returns HTTPResponse.Status
func (r V1CreateProjectClaimTokenResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1CreateProjectClaimTokenResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetAuthServiceConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *AuthConfigResponse
}

// Status returns HTTPResponse.Status
func (r V1GetAuthServiceConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetAuthServiceConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1UpdateAuthServiceConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *AuthConfigResponse
}

// Status returns HTTPResponse.Status
func (r V1UpdateAuthServiceConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1UpdateAuthServiceConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type ListSigningKeysForProjectResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *SigningKeysResponse
}

// Status returns HTTPResponse.Status
func (r ListSigningKeysForProjectResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r ListSigningKeysForProjectResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type CreateSigningKeyForProjectResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *SigningKeyResponse
}

// Status returns HTTPResponse.Status
func (r CreateSigningKeyForProjectResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r CreateSigningKeyForProjectResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteSigningKeyResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *SigningKeyResponse
}

// Status returns HTTPResponse.Status
func (r DeleteSigningKeyResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteSigningKeyResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetSigningKeyForProjectResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *SigningKeyResponse
}

// Status returns HTTPResponse.Status
func (r GetSigningKeyForProjectResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetSigningKeyForProjectResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PatchSigningKeyResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *SigningKeyResponse
}

// Status returns HTTPResponse.Status
func (r PatchSigningKeyResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PatchSigningKeyResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ListAllSsoProviderResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ListProvidersResponse
}

// Status returns HTTPResponse.Status
func (r V1ListAllSsoProviderResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ListAllSsoProviderResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1CreateASsoProviderResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *CreateProviderResponse
}

// Status returns HTTPResponse.Status
func (r V1CreateASsoProviderResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1CreateASsoProviderResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1DeleteASsoProviderResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DeleteProviderResponse
}

// Status returns HTTPResponse.Status
func (r V1DeleteASsoProviderResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1DeleteASsoProviderResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetASsoProviderResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *GetProviderResponse
}

// Status returns HTTPResponse.Status
func (r V1GetASsoProviderResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetASsoProviderResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1UpdateASsoProviderResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *UpdateProviderResponse
}

// Status returns HTTPResponse.Status
func (r V1UpdateASsoProviderResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1UpdateASsoProviderResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type ListTPAForProjectResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]ThirdPartyAuth
}

// Status returns HTTPResponse.Status
func (r ListTPAForProjectResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r ListTPAForProjectResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type CreateTPAForProjectResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *ThirdPartyAuth
}

// Status returns HTTPResponse.Status
func (r CreateTPAForProjectResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r CreateTPAForProjectResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteTPAForProjectResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ThirdPartyAuth
}

// Status returns HTTPResponse.Status
func (r DeleteTPAForProjectResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteTPAForProjectResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetTPAForProjectResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ThirdPartyAuth
}

// Status returns HTTPResponse.Status
func (r GetTPAForProjectResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetTPAForProjectResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetProjectPgbouncerConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *V1PgbouncerConfigResponse
}

// Status returns HTTPResponse.Status
func (r V1GetProjectPgbouncerConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetProjectPgbouncerConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetPoolerConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]SupavisorConfigResponse
}

// Status returns HTTPResponse.Status
func (r V1GetPoolerConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetPoolerConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1UpdatePoolerConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *UpdateSupavisorConfigResponse
}

// Status returns HTTPResponse.Status
func (r V1UpdatePoolerConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1UpdatePoolerConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetPostgresConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *PostgresConfigResponse
}

// Status returns HTTPResponse.Status
func (r V1GetPostgresConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetPostgresConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1UpdatePostgresConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *PostgresConfigResponse
}

// Status returns HTTPResponse.Status
func (r V1UpdatePostgresConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1UpdatePostgresConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetStorageConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *StorageConfigResponse
}

// Status returns HTTPResponse.Status
func (r V1GetStorageConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetStorageConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1UpdateStorageConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1UpdateStorageConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1UpdateStorageConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1DeleteHostnameConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1DeleteHostnameConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1DeleteHostnameConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetHostnameConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *UpdateCustomHostnameResponse
}

// Status returns HTTPResponse.Status
func (r V1GetHostnameConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetHostnameConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ActivateCustomHostnameResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *UpdateCustomHostnameResponse
}

// Status returns HTTPResponse.Status
func (r V1ActivateCustomHostnameResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ActivateCustomHostnameResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1UpdateHostnameConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *UpdateCustomHostnameResponse
}

// Status returns HTTPResponse.Status
func (r V1UpdateHostnameConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1UpdateHostnameConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1VerifyDnsConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *UpdateCustomHostnameResponse
}

// Status returns HTTPResponse.Status
func (r V1VerifyDnsConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1VerifyDnsConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ListAllBackupsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *V1BackupsResponse
}

// Status returns HTTPResponse.Status
func (r V1ListAllBackupsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ListAllBackupsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1RestorePitrBackupResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1RestorePitrBackupResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1RestorePitrBackupResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetDatabaseMetadataResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *GetProjectDbMetadataResponse
}

// Status returns HTTPResponse.Status
func (r GetDatabaseMetadataResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetDatabaseMetadataResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ListMigrationHistoryResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *V1ListMigrationsResponse
}

// Status returns HTTPResponse.Status
func (r V1ListMigrationHistoryResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ListMigrationHistoryResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ApplyAMigrationResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1ApplyAMigrationResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ApplyAMigrationResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1RunAQueryResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1RunAQueryResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1RunAQueryResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1EnableDatabaseWebhookResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1EnableDatabaseWebhookResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1EnableDatabaseWebhookResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ListAllFunctionsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]FunctionResponse
}

// Status returns HTTPResponse.Status
func (r V1ListAllFunctionsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ListAllFunctionsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1CreateAFunctionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *FunctionResponse
}

// Status returns HTTPResponse.Status
func (r V1CreateAFunctionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1CreateAFunctionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1BulkUpdateFunctionsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *BulkUpdateFunctionResponse
}

// Status returns HTTPResponse.Status
func (r V1BulkUpdateFunctionsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1BulkUpdateFunctionsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1DeployAFunctionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *DeployFunctionResponse
}

// Status returns HTTPResponse.Status
func (r V1DeployAFunctionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1DeployAFunctionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1DeleteAFunctionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1DeleteAFunctionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1DeleteAFunctionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetAFunctionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *FunctionSlugResponse
}

// Status returns HTTPResponse.Status
func (r V1GetAFunctionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetAFunctionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1UpdateAFunctionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *FunctionResponse
}

// Status returns HTTPResponse.Status
func (r V1UpdateAFunctionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1UpdateAFunctionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetAFunctionBodyResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *StreamableFile
}

// Status returns HTTPResponse.Status
func (r V1GetAFunctionBodyResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetAFunctionBodyResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetServicesHealthResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]V1ServiceHealthResponse
}

// Status returns HTTPResponse.Status
func (r V1GetServicesHealthResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetServicesHealthResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1DeleteNetworkBansResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1DeleteNetworkBansResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1DeleteNetworkBansResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ListAllNetworkBansResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *NetworkBanResponse
}

// Status returns HTTPResponse.Status
func (r V1ListAllNetworkBansResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ListAllNetworkBansResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ListAllNetworkBansEnrichedResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *NetworkBanResponseEnriched
}

// Status returns HTTPResponse.Status
func (r V1ListAllNetworkBansEnrichedResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ListAllNetworkBansEnrichedResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetNetworkRestrictionsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *NetworkRestrictionsResponse
}

// Status returns HTTPResponse.Status
func (r V1GetNetworkRestrictionsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetNetworkRestrictionsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1UpdateNetworkRestrictionsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *NetworkRestrictionsResponse
}

// Status returns HTTPResponse.Status
func (r V1UpdateNetworkRestrictionsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1UpdateNetworkRestrictionsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1PauseAProjectResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1PauseAProjectResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1PauseAProjectResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetPgsodiumConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *PgsodiumConfigResponse
}

// Status returns HTTPResponse.Status
func (r V1GetPgsodiumConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetPgsodiumConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1UpdatePgsodiumConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *PgsodiumConfigResponse
}

// Status returns HTTPResponse.Status
func (r V1UpdatePgsodiumConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1UpdatePgsodiumConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetPostgrestServiceConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *PostgrestConfigWithJWTSecretResponse
}

// Status returns HTTPResponse.Status
func (r V1GetPostgrestServiceConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetPostgrestServiceConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1UpdatePostgrestServiceConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *V1PostgrestConfigResponse
}

// Status returns HTTPResponse.Status
func (r V1UpdatePostgrestServiceConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1UpdatePostgrestServiceConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1RemoveAReadReplicaResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1RemoveAReadReplicaResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1RemoveAReadReplicaResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1SetupAReadReplicaResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1SetupAReadReplicaResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1SetupAReadReplicaResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetReadonlyModeStatusResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ReadOnlyStatusResponse
}

// Status returns HTTPResponse.Status
func (r V1GetReadonlyModeStatusResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetReadonlyModeStatusResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1DisableReadonlyModeTemporarilyResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1DisableReadonlyModeTemporarilyResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1DisableReadonlyModeTemporarilyResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ListAvailableRestoreVersionsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *GetProjectAvailableRestoreVersionsResponse
}

// Status returns HTTPResponse.Status
func (r V1ListAvailableRestoreVersionsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ListAvailableRestoreVersionsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1RestoreAProjectResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1RestoreAProjectResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1RestoreAProjectResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1CancelAProjectRestorationResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1CancelAProjectRestorationResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1CancelAProjectRestorationResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1BulkDeleteSecretsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1BulkDeleteSecretsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1BulkDeleteSecretsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ListAllSecretsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]SecretResponse
}

// Status returns HTTPResponse.Status
func (r V1ListAllSecretsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ListAllSecretsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1BulkCreateSecretsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1BulkCreateSecretsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1BulkCreateSecretsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetSslEnforcementConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *SslEnforcementResponse
}

// Status returns HTTPResponse.Status
func (r V1GetSslEnforcementConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetSslEnforcementConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1UpdateSslEnforcementConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *SslEnforcementResponse
}

// Status returns HTTPResponse.Status
func (r V1UpdateSslEnforcementConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1UpdateSslEnforcementConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ListAllBucketsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *[]V1StorageBucketResponse
}

// Status returns HTTPResponse.Status
func (r V1ListAllBucketsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ListAllBucketsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GenerateTypescriptTypesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *TypescriptResponse
}

// Status returns HTTPResponse.Status
func (r V1GenerateTypescriptTypesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GenerateTypescriptTypesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1UpgradePostgresVersionResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *ProjectUpgradeInitiateResponse
}

// Status returns HTTPResponse.Status
func (r V1UpgradePostgresVersionResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1UpgradePostgresVersionResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetPostgresUpgradeEligibilityResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ProjectUpgradeEligibilityResponse
}

// Status returns HTTPResponse.Status
func (r V1GetPostgresUpgradeEligibilityResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetPostgresUpgradeEligibilityResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetPostgresUpgradeStatusResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DatabaseUpgradeStatusResponse
}

// Status returns HTTPResponse.Status
func (r V1GetPostgresUpgradeStatusResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetPostgresUpgradeStatusResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1DeactivateVanitySubdomainConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
}

// Status returns HTTPResponse.Status
func (r V1DeactivateVanitySubdomainConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1DeactivateVanitySubdomainConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetVanitySubdomainConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *VanitySubdomainConfigResponse
}

// Status returns HTTPResponse.Status
func (r V1GetVanitySubdomainConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetVanitySubdomainConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ActivateVanitySubdomainConfigResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *ActivateVanitySubdomainResponse
}

// Status returns HTTPResponse.Status
func (r V1ActivateVanitySubdomainConfigResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ActivateVanitySubdomainConfigResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1CheckVanitySubdomainAvailabilityResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON201      *SubdomainAvailabilityResponse
}

// Status returns HTTPResponse.Status
func (r V1CheckVanitySubdomainAvailabilityResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1CheckVanitySubdomainAvailabilityResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1ListAllSnippetsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *SnippetList
}

// Status returns HTTPResponse.Status
func (r V1ListAllSnippetsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1ListAllSnippetsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type V1GetASnippetResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *SnippetResponse
}

// Status returns HTTPResponse.Status
func (r V1GetASnippetResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r V1GetASnippetResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

// V1DeleteABranchWithResponse request returning *V1DeleteABranchResponse
func (c *ClientWithResponses) V1DeleteABranchWithResponse(ctx context.Context, branchId openapi_types.UUID, reqEditors ...RequestEditorFn) (*V1DeleteABranchResponse, error) {
	rsp, err := c.V1DeleteABranch(ctx, branchId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1DeleteABranchResponse(rsp)
}

// V1GetABranchConfigWithResponse request returning *V1GetABranchConfigResponse
func (c *ClientWithResponses) V1GetABranchConfigWithResponse(ctx context.Context, branchId openapi_types.UUID, reqEditors ...RequestEditorFn) (*V1GetABranchConfigResponse, error) {
	rsp, err := c.V1GetABranchConfig(ctx, branchId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetABranchConfigResponse(rsp)
}

// V1UpdateABranchConfigWithBodyWithResponse request with arbitrary body returning *V1UpdateABranchConfigResponse
func (c *ClientWithResponses) V1UpdateABranchConfigWithBodyWithResponse(ctx context.Context, branchId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdateABranchConfigResponse, error) {
	rsp, err := c.V1UpdateABranchConfigWithBody(ctx, branchId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdateABranchConfigResponse(rsp)
}

func (c *ClientWithResponses) V1UpdateABranchConfigWithResponse(ctx context.Context, branchId openapi_types.UUID, body V1UpdateABranchConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdateABranchConfigResponse, error) {
	rsp, err := c.V1UpdateABranchConfig(ctx, branchId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdateABranchConfigResponse(rsp)
}

// V1DiffABranchWithResponse request returning *V1DiffABranchResponse
func (c *ClientWithResponses) V1DiffABranchWithResponse(ctx context.Context, branchId openapi_types.UUID, params *V1DiffABranchParams, reqEditors ...RequestEditorFn) (*V1DiffABranchResponse, error) {
	rsp, err := c.V1DiffABranch(ctx, branchId, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1DiffABranchResponse(rsp)
}

// V1MergeABranchWithBodyWithResponse request with arbitrary body returning *V1MergeABranchResponse
func (c *ClientWithResponses) V1MergeABranchWithBodyWithResponse(ctx context.Context, branchId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1MergeABranchResponse, error) {
	rsp, err := c.V1MergeABranchWithBody(ctx, branchId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1MergeABranchResponse(rsp)
}

func (c *ClientWithResponses) V1MergeABranchWithResponse(ctx context.Context, branchId openapi_types.UUID, body V1MergeABranchJSONRequestBody, reqEditors ...RequestEditorFn) (*V1MergeABranchResponse, error) {
	rsp, err := c.V1MergeABranch(ctx, branchId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1MergeABranchResponse(rsp)
}

// V1PushABranchWithBodyWithResponse request with arbitrary body returning *V1PushABranchResponse
func (c *ClientWithResponses) V1PushABranchWithBodyWithResponse(ctx context.Context, branchId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1PushABranchResponse, error) {
	rsp, err := c.V1PushABranchWithBody(ctx, branchId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1PushABranchResponse(rsp)
}

func (c *ClientWithResponses) V1PushABranchWithResponse(ctx context.Context, branchId openapi_types.UUID, body V1PushABranchJSONRequestBody, reqEditors ...RequestEditorFn) (*V1PushABranchResponse, error) {
	rsp, err := c.V1PushABranch(ctx, branchId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1PushABranchResponse(rsp)
}

// V1ResetABranchWithBodyWithResponse request with arbitrary body returning *V1ResetABranchResponse
func (c *ClientWithResponses) V1ResetABranchWithBodyWithResponse(ctx context.Context, branchId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1ResetABranchResponse, error) {
	rsp, err := c.V1ResetABranchWithBody(ctx, branchId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ResetABranchResponse(rsp)
}

func (c *ClientWithResponses) V1ResetABranchWithResponse(ctx context.Context, branchId openapi_types.UUID, body V1ResetABranchJSONRequestBody, reqEditors ...RequestEditorFn) (*V1ResetABranchResponse, error) {
	rsp, err := c.V1ResetABranch(ctx, branchId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ResetABranchResponse(rsp)
}

// V1AuthorizeUserWithResponse request returning *V1AuthorizeUserResponse
func (c *ClientWithResponses) V1AuthorizeUserWithResponse(ctx context.Context, params *V1AuthorizeUserParams, reqEditors ...RequestEditorFn) (*V1AuthorizeUserResponse, error) {
	rsp, err := c.V1AuthorizeUser(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1AuthorizeUserResponse(rsp)
}

// V1RevokeTokenWithBodyWithResponse request with arbitrary body returning *V1RevokeTokenResponse
func (c *ClientWithResponses) V1RevokeTokenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1RevokeTokenResponse, error) {
	rsp, err := c.V1RevokeTokenWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1RevokeTokenResponse(rsp)
}

func (c *ClientWithResponses) V1RevokeTokenWithResponse(ctx context.Context, body V1RevokeTokenJSONRequestBody, reqEditors ...RequestEditorFn) (*V1RevokeTokenResponse, error) {
	rsp, err := c.V1RevokeToken(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1RevokeTokenResponse(rsp)
}

// V1ExchangeOauthTokenWithBodyWithResponse request with arbitrary body returning *V1ExchangeOauthTokenResponse
func (c *ClientWithResponses) V1ExchangeOauthTokenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1ExchangeOauthTokenResponse, error) {
	rsp, err := c.V1ExchangeOauthTokenWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ExchangeOauthTokenResponse(rsp)
}

func (c *ClientWithResponses) V1ExchangeOauthTokenWithFormdataBodyWithResponse(ctx context.Context, body V1ExchangeOauthTokenFormdataRequestBody, reqEditors ...RequestEditorFn) (*V1ExchangeOauthTokenResponse, error) {
	rsp, err := c.V1ExchangeOauthTokenWithFormdataBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ExchangeOauthTokenResponse(rsp)
}

// V1ListAllOrganizationsWithResponse request returning *V1ListAllOrganizationsResponse
func (c *ClientWithResponses) V1ListAllOrganizationsWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*V1ListAllOrganizationsResponse, error) {
	rsp, err := c.V1ListAllOrganizations(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ListAllOrganizationsResponse(rsp)
}

// V1CreateAnOrganizationWithBodyWithResponse request with arbitrary body returning *V1CreateAnOrganizationResponse
func (c *ClientWithResponses) V1CreateAnOrganizationWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1CreateAnOrganizationResponse, error) {
	rsp, err := c.V1CreateAnOrganizationWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1CreateAnOrganizationResponse(rsp)
}

func (c *ClientWithResponses) V1CreateAnOrganizationWithResponse(ctx context.Context, body V1CreateAnOrganizationJSONRequestBody, reqEditors ...RequestEditorFn) (*V1CreateAnOrganizationResponse, error) {
	rsp, err := c.V1CreateAnOrganization(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1CreateAnOrganizationResponse(rsp)
}

// V1GetAnOrganizationWithResponse request returning *V1GetAnOrganizationResponse
func (c *ClientWithResponses) V1GetAnOrganizationWithResponse(ctx context.Context, slug string, reqEditors ...RequestEditorFn) (*V1GetAnOrganizationResponse, error) {
	rsp, err := c.V1GetAnOrganization(ctx, slug, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetAnOrganizationResponse(rsp)
}

// V1ListOrganizationMembersWithResponse request returning *V1ListOrganizationMembersResponse
func (c *ClientWithResponses) V1ListOrganizationMembersWithResponse(ctx context.Context, slug string, reqEditors ...RequestEditorFn) (*V1ListOrganizationMembersResponse, error) {
	rsp, err := c.V1ListOrganizationMembers(ctx, slug, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ListOrganizationMembersResponse(rsp)
}

// V1GetOrganizationProjectClaimWithResponse request returning *V1GetOrganizationProjectClaimResponse
func (c *ClientWithResponses) V1GetOrganizationProjectClaimWithResponse(ctx context.Context, slug string, token string, reqEditors ...RequestEditorFn) (*V1GetOrganizationProjectClaimResponse, error) {
	rsp, err := c.V1GetOrganizationProjectClaim(ctx, slug, token, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetOrganizationProjectClaimResponse(rsp)
}

// V1ClaimProjectForOrganizationWithResponse request returning *V1ClaimProjectForOrganizationResponse
func (c *ClientWithResponses) V1ClaimProjectForOrganizationWithResponse(ctx context.Context, slug string, token string, reqEditors ...RequestEditorFn) (*V1ClaimProjectForOrganizationResponse, error) {
	rsp, err := c.V1ClaimProjectForOrganization(ctx, slug, token, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ClaimProjectForOrganizationResponse(rsp)
}

// V1ListAllProjectsWithResponse request returning *V1ListAllProjectsResponse
func (c *ClientWithResponses) V1ListAllProjectsWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*V1ListAllProjectsResponse, error) {
	rsp, err := c.V1ListAllProjects(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ListAllProjectsResponse(rsp)
}

// V1CreateAProjectWithBodyWithResponse request with arbitrary body returning *V1CreateAProjectResponse
func (c *ClientWithResponses) V1CreateAProjectWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1CreateAProjectResponse, error) {
	rsp, err := c.V1CreateAProjectWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1CreateAProjectResponse(rsp)
}

func (c *ClientWithResponses) V1CreateAProjectWithResponse(ctx context.Context, body V1CreateAProjectJSONRequestBody, reqEditors ...RequestEditorFn) (*V1CreateAProjectResponse, error) {
	rsp, err := c.V1CreateAProject(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1CreateAProjectResponse(rsp)
}

// V1DeleteAProjectWithResponse request returning *V1DeleteAProjectResponse
func (c *ClientWithResponses) V1DeleteAProjectWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1DeleteAProjectResponse, error) {
	rsp, err := c.V1DeleteAProject(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1DeleteAProjectResponse(rsp)
}

// V1GetProjectWithResponse request returning *V1GetProjectResponse
func (c *ClientWithResponses) V1GetProjectWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetProjectResponse, error) {
	rsp, err := c.V1GetProject(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetProjectResponse(rsp)
}

// GetPerformanceAdvisorsWithResponse request returning *GetPerformanceAdvisorsResponse
func (c *ClientWithResponses) GetPerformanceAdvisorsWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*GetPerformanceAdvisorsResponse, error) {
	rsp, err := c.GetPerformanceAdvisors(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetPerformanceAdvisorsResponse(rsp)
}

// GetSecurityAdvisorsWithResponse request returning *GetSecurityAdvisorsResponse
func (c *ClientWithResponses) GetSecurityAdvisorsWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*GetSecurityAdvisorsResponse, error) {
	rsp, err := c.GetSecurityAdvisors(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetSecurityAdvisorsResponse(rsp)
}

// GetLogsWithResponse request returning *GetLogsResponse
func (c *ClientWithResponses) GetLogsWithResponse(ctx context.Context, ref string, params *GetLogsParams, reqEditors ...RequestEditorFn) (*GetLogsResponse, error) {
	rsp, err := c.GetLogs(ctx, ref, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetLogsResponse(rsp)
}

// GetApiCountsWithResponse request returning *GetApiCountsResponse
func (c *ClientWithResponses) GetApiCountsWithResponse(ctx context.Context, ref string, params *GetApiCountsParams, reqEditors ...RequestEditorFn) (*GetApiCountsResponse, error) {
	rsp, err := c.GetApiCounts(ctx, ref, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApiCountsResponse(rsp)
}

// GetApiRequestsCountWithResponse request returning *GetApiRequestsCountResponse
func (c *ClientWithResponses) GetApiRequestsCountWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*GetApiRequestsCountResponse, error) {
	rsp, err := c.GetApiRequestsCount(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApiRequestsCountResponse(rsp)
}

// V1GetProjectApiKeysWithResponse request returning *V1GetProjectApiKeysResponse
func (c *ClientWithResponses) V1GetProjectApiKeysWithResponse(ctx context.Context, ref string, params *V1GetProjectApiKeysParams, reqEditors ...RequestEditorFn) (*V1GetProjectApiKeysResponse, error) {
	rsp, err := c.V1GetProjectApiKeys(ctx, ref, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetProjectApiKeysResponse(rsp)
}

// CreateApiKeyWithBodyWithResponse request with arbitrary body returning *CreateApiKeyResponse
func (c *ClientWithResponses) CreateApiKeyWithBodyWithResponse(ctx context.Context, ref string, params *CreateApiKeyParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreateApiKeyResponse, error) {
	rsp, err := c.CreateApiKeyWithBody(ctx, ref, params, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreateApiKeyResponse(rsp)
}

func (c *ClientWithResponses) CreateApiKeyWithResponse(ctx context.Context, ref string, params *CreateApiKeyParams, body CreateApiKeyJSONRequestBody, reqEditors ...RequestEditorFn) (*CreateApiKeyResponse, error) {
	rsp, err := c.CreateApiKey(ctx, ref, params, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreateApiKeyResponse(rsp)
}

// DeleteApiKeyWithResponse request returning *DeleteApiKeyResponse
func (c *ClientWithResponses) DeleteApiKeyWithResponse(ctx context.Context, ref string, id openapi_types.UUID, params *DeleteApiKeyParams, reqEditors ...RequestEditorFn) (*DeleteApiKeyResponse, error) {
	rsp, err := c.DeleteApiKey(ctx, ref, id, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteApiKeyResponse(rsp)
}

// GetApiKeyWithResponse request returning *GetApiKeyResponse
func (c *ClientWithResponses) GetApiKeyWithResponse(ctx context.Context, ref string, id openapi_types.UUID, params *GetApiKeyParams, reqEditors ...RequestEditorFn) (*GetApiKeyResponse, error) {
	rsp, err := c.GetApiKey(ctx, ref, id, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApiKeyResponse(rsp)
}

// UpdateApiKeyWithBodyWithResponse request with arbitrary body returning *UpdateApiKeyResponse
func (c *ClientWithResponses) UpdateApiKeyWithBodyWithResponse(ctx context.Context, ref string, id openapi_types.UUID, params *UpdateApiKeyParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateApiKeyResponse, error) {
	rsp, err := c.UpdateApiKeyWithBody(ctx, ref, id, params, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApiKeyResponse(rsp)
}

func (c *ClientWithResponses) UpdateApiKeyWithResponse(ctx context.Context, ref string, id openapi_types.UUID, params *UpdateApiKeyParams, body UpdateApiKeyJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateApiKeyResponse, error) {
	rsp, err := c.UpdateApiKey(ctx, ref, id, params, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateApiKeyResponse(rsp)
}

// V1ListProjectAddonsWithResponse request returning *V1ListProjectAddonsResponse
func (c *ClientWithResponses) V1ListProjectAddonsWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListProjectAddonsResponse, error) {
	rsp, err := c.V1ListProjectAddons(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ListProjectAddonsResponse(rsp)
}

// V1ApplyProjectAddonWithBodyWithResponse request with arbitrary body returning *V1ApplyProjectAddonResponse
func (c *ClientWithResponses) V1ApplyProjectAddonWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1ApplyProjectAddonResponse, error) {
	rsp, err := c.V1ApplyProjectAddonWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ApplyProjectAddonResponse(rsp)
}

func (c *ClientWithResponses) V1ApplyProjectAddonWithResponse(ctx context.Context, ref string, body V1ApplyProjectAddonJSONRequestBody, reqEditors ...RequestEditorFn) (*V1ApplyProjectAddonResponse, error) {
	rsp, err := c.V1ApplyProjectAddon(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ApplyProjectAddonResponse(rsp)
}

// V1RemoveProjectAddonWithResponse request returning *V1RemoveProjectAddonResponse
func (c *ClientWithResponses) V1RemoveProjectAddonWithResponse(ctx context.Context, ref string, addonVariant interface{}, reqEditors ...RequestEditorFn) (*V1RemoveProjectAddonResponse, error) {
	rsp, err := c.V1RemoveProjectAddon(ctx, ref, addonVariant, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1RemoveProjectAddonResponse(rsp)
}

// V1DisablePreviewBranchingWithResponse request returning *V1DisablePreviewBranchingResponse
func (c *ClientWithResponses) V1DisablePreviewBranchingWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1DisablePreviewBranchingResponse, error) {
	rsp, err := c.V1DisablePreviewBranching(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1DisablePreviewBranchingResponse(rsp)
}

// V1ListAllBranchesWithResponse request returning *V1ListAllBranchesResponse
func (c *ClientWithResponses) V1ListAllBranchesWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListAllBranchesResponse, error) {
	rsp, err := c.V1ListAllBranches(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ListAllBranchesResponse(rsp)
}

// V1CreateABranchWithBodyWithResponse request with arbitrary body returning *V1CreateABranchResponse
func (c *ClientWithResponses) V1CreateABranchWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1CreateABranchResponse, error) {
	rsp, err := c.V1CreateABranchWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1CreateABranchResponse(rsp)
}

func (c *ClientWithResponses) V1CreateABranchWithResponse(ctx context.Context, ref string, body V1CreateABranchJSONRequestBody, reqEditors ...RequestEditorFn) (*V1CreateABranchResponse, error) {
	rsp, err := c.V1CreateABranch(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1CreateABranchResponse(rsp)
}

// V1DeleteProjectClaimTokenWithResponse request returning *V1DeleteProjectClaimTokenResponse
func (c *ClientWithResponses) V1DeleteProjectClaimTokenWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1DeleteProjectClaimTokenResponse, error) {
	rsp, err := c.V1DeleteProjectClaimToken(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1DeleteProjectClaimTokenResponse(rsp)
}

// V1GetProjectClaimTokenWithResponse request returning *V1GetProjectClaimTokenResponse
func (c *ClientWithResponses) V1GetProjectClaimTokenWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetProjectClaimTokenResponse, error) {
	rsp, err := c.V1GetProjectClaimToken(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetProjectClaimTokenResponse(rsp)
}

// V1CreateProjectClaimTokenWithResponse request returning *V1CreateProjectClaimTokenResponse
func (c *ClientWithResponses) V1CreateProjectClaimTokenWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1CreateProjectClaimTokenResponse, error) {
	rsp, err := c.V1CreateProjectClaimToken(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1CreateProjectClaimTokenResponse(rsp)
}

// V1GetAuthServiceConfigWithResponse request returning *V1GetAuthServiceConfigResponse
func (c *ClientWithResponses) V1GetAuthServiceConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetAuthServiceConfigResponse, error) {
	rsp, err := c.V1GetAuthServiceConfig(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetAuthServiceConfigResponse(rsp)
}

// V1UpdateAuthServiceConfigWithBodyWithResponse request with arbitrary body returning *V1UpdateAuthServiceConfigResponse
func (c *ClientWithResponses) V1UpdateAuthServiceConfigWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdateAuthServiceConfigResponse, error) {
	rsp, err := c.V1UpdateAuthServiceConfigWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdateAuthServiceConfigResponse(rsp)
}

func (c *ClientWithResponses) V1UpdateAuthServiceConfigWithResponse(ctx context.Context, ref string, body V1UpdateAuthServiceConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdateAuthServiceConfigResponse, error) {
	rsp, err := c.V1UpdateAuthServiceConfig(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdateAuthServiceConfigResponse(rsp)
}

// ListSigningKeysForProjectWithResponse request returning *ListSigningKeysForProjectResponse
func (c *ClientWithResponses) ListSigningKeysForProjectWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*ListSigningKeysForProjectResponse, error) {
	rsp, err := c.ListSigningKeysForProject(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseListSigningKeysForProjectResponse(rsp)
}

// CreateSigningKeyForProjectWithBodyWithResponse request with arbitrary body returning *CreateSigningKeyForProjectResponse
func (c *ClientWithResponses) CreateSigningKeyForProjectWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreateSigningKeyForProjectResponse, error) {
	rsp, err := c.CreateSigningKeyForProjectWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreateSigningKeyForProjectResponse(rsp)
}

func (c *ClientWithResponses) CreateSigningKeyForProjectWithResponse(ctx context.Context, ref string, body CreateSigningKeyForProjectJSONRequestBody, reqEditors ...RequestEditorFn) (*CreateSigningKeyForProjectResponse, error) {
	rsp, err := c.CreateSigningKeyForProject(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreateSigningKeyForProjectResponse(rsp)
}

// DeleteSigningKeyWithResponse request returning *DeleteSigningKeyResponse
func (c *ClientWithResponses) DeleteSigningKeyWithResponse(ctx context.Context, ref string, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteSigningKeyResponse, error) {
	rsp, err := c.DeleteSigningKey(ctx, ref, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteSigningKeyResponse(rsp)
}

// GetSigningKeyForProjectWithResponse request returning *GetSigningKeyForProjectResponse
func (c *ClientWithResponses) GetSigningKeyForProjectWithResponse(ctx context.Context, ref string, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetSigningKeyForProjectResponse, error) {
	rsp, err := c.GetSigningKeyForProject(ctx, ref, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetSigningKeyForProjectResponse(rsp)
}

// PatchSigningKeyWithBodyWithResponse request with arbitrary body returning *PatchSigningKeyResponse
func (c *ClientWithResponses) PatchSigningKeyWithBodyWithResponse(ctx context.Context, ref string, id openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PatchSigningKeyResponse, error) {
	rsp, err := c.PatchSigningKeyWithBody(ctx, ref, id, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePatchSigningKeyResponse(rsp)
}

func (c *ClientWithResponses) PatchSigningKeyWithResponse(ctx context.Context, ref string, id openapi_types.UUID, body PatchSigningKeyJSONRequestBody, reqEditors ...RequestEditorFn) (*PatchSigningKeyResponse, error) {
	rsp, err := c.PatchSigningKey(ctx, ref, id, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePatchSigningKeyResponse(rsp)
}

// V1ListAllSsoProviderWithResponse request returning *V1ListAllSsoProviderResponse
func (c *ClientWithResponses) V1ListAllSsoProviderWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListAllSsoProviderResponse, error) {
	rsp, err := c.V1ListAllSsoProvider(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ListAllSsoProviderResponse(rsp)
}

// V1CreateASsoProviderWithBodyWithResponse request with arbitrary body returning *V1CreateASsoProviderResponse
func (c *ClientWithResponses) V1CreateASsoProviderWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1CreateASsoProviderResponse, error) {
	rsp, err := c.V1CreateASsoProviderWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1CreateASsoProviderResponse(rsp)
}

func (c *ClientWithResponses) V1CreateASsoProviderWithResponse(ctx context.Context, ref string, body V1CreateASsoProviderJSONRequestBody, reqEditors ...RequestEditorFn) (*V1CreateASsoProviderResponse, error) {
	rsp, err := c.V1CreateASsoProvider(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1CreateASsoProviderResponse(rsp)
}

// V1DeleteASsoProviderWithResponse request returning *V1DeleteASsoProviderResponse
func (c *ClientWithResponses) V1DeleteASsoProviderWithResponse(ctx context.Context, ref string, providerId openapi_types.UUID, reqEditors ...RequestEditorFn) (*V1DeleteASsoProviderResponse, error) {
	rsp, err := c.V1DeleteASsoProvider(ctx, ref, providerId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1DeleteASsoProviderResponse(rsp)
}

// V1GetASsoProviderWithResponse request returning *V1GetASsoProviderResponse
func (c *ClientWithResponses) V1GetASsoProviderWithResponse(ctx context.Context, ref string, providerId openapi_types.UUID, reqEditors ...RequestEditorFn) (*V1GetASsoProviderResponse, error) {
	rsp, err := c.V1GetASsoProvider(ctx, ref, providerId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetASsoProviderResponse(rsp)
}

// V1UpdateASsoProviderWithBodyWithResponse request with arbitrary body returning *V1UpdateASsoProviderResponse
func (c *ClientWithResponses) V1UpdateASsoProviderWithBodyWithResponse(ctx context.Context, ref string, providerId openapi_types.UUID, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdateASsoProviderResponse, error) {
	rsp, err := c.V1UpdateASsoProviderWithBody(ctx, ref, providerId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdateASsoProviderResponse(rsp)
}

func (c *ClientWithResponses) V1UpdateASsoProviderWithResponse(ctx context.Context, ref string, providerId openapi_types.UUID, body V1UpdateASsoProviderJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdateASsoProviderResponse, error) {
	rsp, err := c.V1UpdateASsoProvider(ctx, ref, providerId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdateASsoProviderResponse(rsp)
}

// ListTPAForProjectWithResponse request returning *ListTPAForProjectResponse
func (c *ClientWithResponses) ListTPAForProjectWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*ListTPAForProjectResponse, error) {
	rsp, err := c.ListTPAForProject(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseListTPAForProjectResponse(rsp)
}

// CreateTPAForProjectWithBodyWithResponse request with arbitrary body returning *CreateTPAForProjectResponse
func (c *ClientWithResponses) CreateTPAForProjectWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreateTPAForProjectResponse, error) {
	rsp, err := c.CreateTPAForProjectWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreateTPAForProjectResponse(rsp)
}

func (c *ClientWithResponses) CreateTPAForProjectWithResponse(ctx context.Context, ref string, body CreateTPAForProjectJSONRequestBody, reqEditors ...RequestEditorFn) (*CreateTPAForProjectResponse, error) {
	rsp, err := c.CreateTPAForProject(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreateTPAForProjectResponse(rsp)
}

// DeleteTPAForProjectWithResponse request returning *DeleteTPAForProjectResponse
func (c *ClientWithResponses) DeleteTPAForProjectWithResponse(ctx context.Context, ref string, tpaId openapi_types.UUID, reqEditors ...RequestEditorFn) (*DeleteTPAForProjectResponse, error) {
	rsp, err := c.DeleteTPAForProject(ctx, ref, tpaId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteTPAForProjectResponse(rsp)
}

// GetTPAForProjectWithResponse request returning *GetTPAForProjectResponse
func (c *ClientWithResponses) GetTPAForProjectWithResponse(ctx context.Context, ref string, tpaId openapi_types.UUID, reqEditors ...RequestEditorFn) (*GetTPAForProjectResponse, error) {
	rsp, err := c.GetTPAForProject(ctx, ref, tpaId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetTPAForProjectResponse(rsp)
}

// V1GetProjectPgbouncerConfigWithResponse request returning *V1GetProjectPgbouncerConfigResponse
func (c *ClientWithResponses) V1GetProjectPgbouncerConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetProjectPgbouncerConfigResponse, error) {
	rsp, err := c.V1GetProjectPgbouncerConfig(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetProjectPgbouncerConfigResponse(rsp)
}

// V1GetPoolerConfigWithResponse request returning *V1GetPoolerConfigResponse
func (c *ClientWithResponses) V1GetPoolerConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetPoolerConfigResponse, error) {
	rsp, err := c.V1GetPoolerConfig(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetPoolerConfigResponse(rsp)
}

// V1UpdatePoolerConfigWithBodyWithResponse request with arbitrary body returning *V1UpdatePoolerConfigResponse
func (c *ClientWithResponses) V1UpdatePoolerConfigWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdatePoolerConfigResponse, error) {
	rsp, err := c.V1UpdatePoolerConfigWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdatePoolerConfigResponse(rsp)
}

func (c *ClientWithResponses) V1UpdatePoolerConfigWithResponse(ctx context.Context, ref string, body V1UpdatePoolerConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdatePoolerConfigResponse, error) {
	rsp, err := c.V1UpdatePoolerConfig(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdatePoolerConfigResponse(rsp)
}

// V1GetPostgresConfigWithResponse request returning *V1GetPostgresConfigResponse
func (c *ClientWithResponses) V1GetPostgresConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetPostgresConfigResponse, error) {
	rsp, err := c.V1GetPostgresConfig(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetPostgresConfigResponse(rsp)
}

// V1UpdatePostgresConfigWithBodyWithResponse request with arbitrary body returning *V1UpdatePostgresConfigResponse
func (c *ClientWithResponses) V1UpdatePostgresConfigWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdatePostgresConfigResponse, error) {
	rsp, err := c.V1UpdatePostgresConfigWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdatePostgresConfigResponse(rsp)
}

func (c *ClientWithResponses) V1UpdatePostgresConfigWithResponse(ctx context.Context, ref string, body V1UpdatePostgresConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdatePostgresConfigResponse, error) {
	rsp, err := c.V1UpdatePostgresConfig(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdatePostgresConfigResponse(rsp)
}

// V1GetStorageConfigWithResponse request returning *V1GetStorageConfigResponse
func (c *ClientWithResponses) V1GetStorageConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetStorageConfigResponse, error) {
	rsp, err := c.V1GetStorageConfig(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetStorageConfigResponse(rsp)
}

// V1UpdateStorageConfigWithBodyWithResponse request with arbitrary body returning *V1UpdateStorageConfigResponse
func (c *ClientWithResponses) V1UpdateStorageConfigWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdateStorageConfigResponse, error) {
	rsp, err := c.V1UpdateStorageConfigWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdateStorageConfigResponse(rsp)
}

func (c *ClientWithResponses) V1UpdateStorageConfigWithResponse(ctx context.Context, ref string, body V1UpdateStorageConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdateStorageConfigResponse, error) {
	rsp, err := c.V1UpdateStorageConfig(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdateStorageConfigResponse(rsp)
}

// V1DeleteHostnameConfigWithResponse request returning *V1DeleteHostnameConfigResponse
func (c *ClientWithResponses) V1DeleteHostnameConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1DeleteHostnameConfigResponse, error) {
	rsp, err := c.V1DeleteHostnameConfig(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1DeleteHostnameConfigResponse(rsp)
}

// V1GetHostnameConfigWithResponse request returning *V1GetHostnameConfigResponse
func (c *ClientWithResponses) V1GetHostnameConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetHostnameConfigResponse, error) {
	rsp, err := c.V1GetHostnameConfig(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetHostnameConfigResponse(rsp)
}

// V1ActivateCustomHostnameWithResponse request returning *V1ActivateCustomHostnameResponse
func (c *ClientWithResponses) V1ActivateCustomHostnameWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ActivateCustomHostnameResponse, error) {
	rsp, err := c.V1ActivateCustomHostname(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ActivateCustomHostnameResponse(rsp)
}

// V1UpdateHostnameConfigWithBodyWithResponse request with arbitrary body returning *V1UpdateHostnameConfigResponse
func (c *ClientWithResponses) V1UpdateHostnameConfigWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdateHostnameConfigResponse, error) {
	rsp, err := c.V1UpdateHostnameConfigWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdateHostnameConfigResponse(rsp)
}

func (c *ClientWithResponses) V1UpdateHostnameConfigWithResponse(ctx context.Context, ref string, body V1UpdateHostnameConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdateHostnameConfigResponse, error) {
	rsp, err := c.V1UpdateHostnameConfig(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdateHostnameConfigResponse(rsp)
}

// V1VerifyDnsConfigWithResponse request returning *V1VerifyDnsConfigResponse
func (c *ClientWithResponses) V1VerifyDnsConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1VerifyDnsConfigResponse, error) {
	rsp, err := c.V1VerifyDnsConfig(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1VerifyDnsConfigResponse(rsp)
}

// V1ListAllBackupsWithResponse request returning *V1ListAllBackupsResponse
func (c *ClientWithResponses) V1ListAllBackupsWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListAllBackupsResponse, error) {
	rsp, err := c.V1ListAllBackups(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ListAllBackupsResponse(rsp)
}

// V1RestorePitrBackupWithBodyWithResponse request with arbitrary body returning *V1RestorePitrBackupResponse
func (c *ClientWithResponses) V1RestorePitrBackupWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1RestorePitrBackupResponse, error) {
	rsp, err := c.V1RestorePitrBackupWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1RestorePitrBackupResponse(rsp)
}

func (c *ClientWithResponses) V1RestorePitrBackupWithResponse(ctx context.Context, ref string, body V1RestorePitrBackupJSONRequestBody, reqEditors ...RequestEditorFn) (*V1RestorePitrBackupResponse, error) {
	rsp, err := c.V1RestorePitrBackup(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1RestorePitrBackupResponse(rsp)
}

// GetDatabaseMetadataWithResponse request returning *GetDatabaseMetadataResponse
func (c *ClientWithResponses) GetDatabaseMetadataWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*GetDatabaseMetadataResponse, error) {
	rsp, err := c.GetDatabaseMetadata(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetDatabaseMetadataResponse(rsp)
}

// V1ListMigrationHistoryWithResponse request returning *V1ListMigrationHistoryResponse
func (c *ClientWithResponses) V1ListMigrationHistoryWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListMigrationHistoryResponse, error) {
	rsp, err := c.V1ListMigrationHistory(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ListMigrationHistoryResponse(rsp)
}

// V1ApplyAMigrationWithBodyWithResponse request with arbitrary body returning *V1ApplyAMigrationResponse
func (c *ClientWithResponses) V1ApplyAMigrationWithBodyWithResponse(ctx context.Context, ref string, params *V1ApplyAMigrationParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1ApplyAMigrationResponse, error) {
	rsp, err := c.V1ApplyAMigrationWithBody(ctx, ref, params, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ApplyAMigrationResponse(rsp)
}

func (c *ClientWithResponses) V1ApplyAMigrationWithResponse(ctx context.Context, ref string, params *V1ApplyAMigrationParams, body V1ApplyAMigrationJSONRequestBody, reqEditors ...RequestEditorFn) (*V1ApplyAMigrationResponse, error) {
	rsp, err := c.V1ApplyAMigration(ctx, ref, params, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ApplyAMigrationResponse(rsp)
}

// V1RunAQueryWithBodyWithResponse request with arbitrary body returning *V1RunAQueryResponse
func (c *ClientWithResponses) V1RunAQueryWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1RunAQueryResponse, error) {
	rsp, err := c.V1RunAQueryWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1RunAQueryResponse(rsp)
}

func (c *ClientWithResponses) V1RunAQueryWithResponse(ctx context.Context, ref string, body V1RunAQueryJSONRequestBody, reqEditors ...RequestEditorFn) (*V1RunAQueryResponse, error) {
	rsp, err := c.V1RunAQuery(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1RunAQueryResponse(rsp)
}

// V1EnableDatabaseWebhookWithResponse request returning *V1EnableDatabaseWebhookResponse
func (c *ClientWithResponses) V1EnableDatabaseWebhookWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1EnableDatabaseWebhookResponse, error) {
	rsp, err := c.V1EnableDatabaseWebhook(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1EnableDatabaseWebhookResponse(rsp)
}

// V1ListAllFunctionsWithResponse request returning *V1ListAllFunctionsResponse
func (c *ClientWithResponses) V1ListAllFunctionsWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListAllFunctionsResponse, error) {
	rsp, err := c.V1ListAllFunctions(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ListAllFunctionsResponse(rsp)
}

// V1CreateAFunctionWithBodyWithResponse request with arbitrary body returning *V1CreateAFunctionResponse
func (c *ClientWithResponses) V1CreateAFunctionWithBodyWithResponse(ctx context.Context, ref string, params *V1CreateAFunctionParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1CreateAFunctionResponse, error) {
	rsp, err := c.V1CreateAFunctionWithBody(ctx, ref, params, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1CreateAFunctionResponse(rsp)
}

func (c *ClientWithResponses) V1CreateAFunctionWithResponse(ctx context.Context, ref string, params *V1CreateAFunctionParams, body V1CreateAFunctionJSONRequestBody, reqEditors ...RequestEditorFn) (*V1CreateAFunctionResponse, error) {
	rsp, err := c.V1CreateAFunction(ctx, ref, params, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1CreateAFunctionResponse(rsp)
}

// V1BulkUpdateFunctionsWithBodyWithResponse request with arbitrary body returning *V1BulkUpdateFunctionsResponse
func (c *ClientWithResponses) V1BulkUpdateFunctionsWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1BulkUpdateFunctionsResponse, error) {
	rsp, err := c.V1BulkUpdateFunctionsWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1BulkUpdateFunctionsResponse(rsp)
}

func (c *ClientWithResponses) V1BulkUpdateFunctionsWithResponse(ctx context.Context, ref string, body V1BulkUpdateFunctionsJSONRequestBody, reqEditors ...RequestEditorFn) (*V1BulkUpdateFunctionsResponse, error) {
	rsp, err := c.V1BulkUpdateFunctions(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1BulkUpdateFunctionsResponse(rsp)
}

// V1DeployAFunctionWithBodyWithResponse request with arbitrary body returning *V1DeployAFunctionResponse
func (c *ClientWithResponses) V1DeployAFunctionWithBodyWithResponse(ctx context.Context, ref string, params *V1DeployAFunctionParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1DeployAFunctionResponse, error) {
	rsp, err := c.V1DeployAFunctionWithBody(ctx, ref, params, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1DeployAFunctionResponse(rsp)
}

// V1DeleteAFunctionWithResponse request returning *V1DeleteAFunctionResponse
func (c *ClientWithResponses) V1DeleteAFunctionWithResponse(ctx context.Context, ref string, functionSlug string, reqEditors ...RequestEditorFn) (*V1DeleteAFunctionResponse, error) {
	rsp, err := c.V1DeleteAFunction(ctx, ref, functionSlug, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1DeleteAFunctionResponse(rsp)
}

// V1GetAFunctionWithResponse request returning *V1GetAFunctionResponse
func (c *ClientWithResponses) V1GetAFunctionWithResponse(ctx context.Context, ref string, functionSlug string, reqEditors ...RequestEditorFn) (*V1GetAFunctionResponse, error) {
	rsp, err := c.V1GetAFunction(ctx, ref, functionSlug, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetAFunctionResponse(rsp)
}

// V1UpdateAFunctionWithBodyWithResponse request with arbitrary body returning *V1UpdateAFunctionResponse
func (c *ClientWithResponses) V1UpdateAFunctionWithBodyWithResponse(ctx context.Context, ref string, functionSlug string, params *V1UpdateAFunctionParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdateAFunctionResponse, error) {
	rsp, err := c.V1UpdateAFunctionWithBody(ctx, ref, functionSlug, params, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdateAFunctionResponse(rsp)
}

func (c *ClientWithResponses) V1UpdateAFunctionWithResponse(ctx context.Context, ref string, functionSlug string, params *V1UpdateAFunctionParams, body V1UpdateAFunctionJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdateAFunctionResponse, error) {
	rsp, err := c.V1UpdateAFunction(ctx, ref, functionSlug, params, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdateAFunctionResponse(rsp)
}

// V1GetAFunctionBodyWithResponse request returning *V1GetAFunctionBodyResponse
func (c *ClientWithResponses) V1GetAFunctionBodyWithResponse(ctx context.Context, ref string, functionSlug string, reqEditors ...RequestEditorFn) (*V1GetAFunctionBodyResponse, error) {
	rsp, err := c.V1GetAFunctionBody(ctx, ref, functionSlug, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetAFunctionBodyResponse(rsp)
}

// V1GetServicesHealthWithResponse request returning *V1GetServicesHealthResponse
func (c *ClientWithResponses) V1GetServicesHealthWithResponse(ctx context.Context, ref string, params *V1GetServicesHealthParams, reqEditors ...RequestEditorFn) (*V1GetServicesHealthResponse, error) {
	rsp, err := c.V1GetServicesHealth(ctx, ref, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetServicesHealthResponse(rsp)
}

// V1DeleteNetworkBansWithBodyWithResponse request with arbitrary body returning *V1DeleteNetworkBansResponse
func (c *ClientWithResponses) V1DeleteNetworkBansWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1DeleteNetworkBansResponse, error) {
	rsp, err := c.V1DeleteNetworkBansWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1DeleteNetworkBansResponse(rsp)
}

func (c *ClientWithResponses) V1DeleteNetworkBansWithResponse(ctx context.Context, ref string, body V1DeleteNetworkBansJSONRequestBody, reqEditors ...RequestEditorFn) (*V1DeleteNetworkBansResponse, error) {
	rsp, err := c.V1DeleteNetworkBans(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1DeleteNetworkBansResponse(rsp)
}

// V1ListAllNetworkBansWithResponse request returning *V1ListAllNetworkBansResponse
func (c *ClientWithResponses) V1ListAllNetworkBansWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListAllNetworkBansResponse, error) {
	rsp, err := c.V1ListAllNetworkBans(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ListAllNetworkBansResponse(rsp)
}

// V1ListAllNetworkBansEnrichedWithResponse request returning *V1ListAllNetworkBansEnrichedResponse
func (c *ClientWithResponses) V1ListAllNetworkBansEnrichedWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListAllNetworkBansEnrichedResponse, error) {
	rsp, err := c.V1ListAllNetworkBansEnriched(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ListAllNetworkBansEnrichedResponse(rsp)
}

// V1GetNetworkRestrictionsWithResponse request returning *V1GetNetworkRestrictionsResponse
func (c *ClientWithResponses) V1GetNetworkRestrictionsWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetNetworkRestrictionsResponse, error) {
	rsp, err := c.V1GetNetworkRestrictions(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetNetworkRestrictionsResponse(rsp)
}

// V1UpdateNetworkRestrictionsWithBodyWithResponse request with arbitrary body returning *V1UpdateNetworkRestrictionsResponse
func (c *ClientWithResponses) V1UpdateNetworkRestrictionsWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdateNetworkRestrictionsResponse, error) {
	rsp, err := c.V1UpdateNetworkRestrictionsWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdateNetworkRestrictionsResponse(rsp)
}

func (c *ClientWithResponses) V1UpdateNetworkRestrictionsWithResponse(ctx context.Context, ref string, body V1UpdateNetworkRestrictionsJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdateNetworkRestrictionsResponse, error) {
	rsp, err := c.V1UpdateNetworkRestrictions(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdateNetworkRestrictionsResponse(rsp)
}

// V1PauseAProjectWithResponse request returning *V1PauseAProjectResponse
func (c *ClientWithResponses) V1PauseAProjectWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1PauseAProjectResponse, error) {
	rsp, err := c.V1PauseAProject(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1PauseAProjectResponse(rsp)
}

// V1GetPgsodiumConfigWithResponse request returning *V1GetPgsodiumConfigResponse
func (c *ClientWithResponses) V1GetPgsodiumConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetPgsodiumConfigResponse, error) {
	rsp, err := c.V1GetPgsodiumConfig(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetPgsodiumConfigResponse(rsp)
}

// V1UpdatePgsodiumConfigWithBodyWithResponse request with arbitrary body returning *V1UpdatePgsodiumConfigResponse
func (c *ClientWithResponses) V1UpdatePgsodiumConfigWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdatePgsodiumConfigResponse, error) {
	rsp, err := c.V1UpdatePgsodiumConfigWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdatePgsodiumConfigResponse(rsp)
}

func (c *ClientWithResponses) V1UpdatePgsodiumConfigWithResponse(ctx context.Context, ref string, body V1UpdatePgsodiumConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdatePgsodiumConfigResponse, error) {
	rsp, err := c.V1UpdatePgsodiumConfig(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdatePgsodiumConfigResponse(rsp)
}

// V1GetPostgrestServiceConfigWithResponse request returning *V1GetPostgrestServiceConfigResponse
func (c *ClientWithResponses) V1GetPostgrestServiceConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetPostgrestServiceConfigResponse, error) {
	rsp, err := c.V1GetPostgrestServiceConfig(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetPostgrestServiceConfigResponse(rsp)
}

// V1UpdatePostgrestServiceConfigWithBodyWithResponse request with arbitrary body returning *V1UpdatePostgrestServiceConfigResponse
func (c *ClientWithResponses) V1UpdatePostgrestServiceConfigWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdatePostgrestServiceConfigResponse, error) {
	rsp, err := c.V1UpdatePostgrestServiceConfigWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdatePostgrestServiceConfigResponse(rsp)
}

func (c *ClientWithResponses) V1UpdatePostgrestServiceConfigWithResponse(ctx context.Context, ref string, body V1UpdatePostgrestServiceConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdatePostgrestServiceConfigResponse, error) {
	rsp, err := c.V1UpdatePostgrestServiceConfig(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdatePostgrestServiceConfigResponse(rsp)
}

// V1RemoveAReadReplicaWithBodyWithResponse request with arbitrary body returning *V1RemoveAReadReplicaResponse
func (c *ClientWithResponses) V1RemoveAReadReplicaWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1RemoveAReadReplicaResponse, error) {
	rsp, err := c.V1RemoveAReadReplicaWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1RemoveAReadReplicaResponse(rsp)
}

func (c *ClientWithResponses) V1RemoveAReadReplicaWithResponse(ctx context.Context, ref string, body V1RemoveAReadReplicaJSONRequestBody, reqEditors ...RequestEditorFn) (*V1RemoveAReadReplicaResponse, error) {
	rsp, err := c.V1RemoveAReadReplica(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1RemoveAReadReplicaResponse(rsp)
}

// V1SetupAReadReplicaWithBodyWithResponse request with arbitrary body returning *V1SetupAReadReplicaResponse
func (c *ClientWithResponses) V1SetupAReadReplicaWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1SetupAReadReplicaResponse, error) {
	rsp, err := c.V1SetupAReadReplicaWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1SetupAReadReplicaResponse(rsp)
}

func (c *ClientWithResponses) V1SetupAReadReplicaWithResponse(ctx context.Context, ref string, body V1SetupAReadReplicaJSONRequestBody, reqEditors ...RequestEditorFn) (*V1SetupAReadReplicaResponse, error) {
	rsp, err := c.V1SetupAReadReplica(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1SetupAReadReplicaResponse(rsp)
}

// V1GetReadonlyModeStatusWithResponse request returning *V1GetReadonlyModeStatusResponse
func (c *ClientWithResponses) V1GetReadonlyModeStatusWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetReadonlyModeStatusResponse, error) {
	rsp, err := c.V1GetReadonlyModeStatus(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetReadonlyModeStatusResponse(rsp)
}

// V1DisableReadonlyModeTemporarilyWithResponse request returning *V1DisableReadonlyModeTemporarilyResponse
func (c *ClientWithResponses) V1DisableReadonlyModeTemporarilyWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1DisableReadonlyModeTemporarilyResponse, error) {
	rsp, err := c.V1DisableReadonlyModeTemporarily(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1DisableReadonlyModeTemporarilyResponse(rsp)
}

// V1ListAvailableRestoreVersionsWithResponse request returning *V1ListAvailableRestoreVersionsResponse
func (c *ClientWithResponses) V1ListAvailableRestoreVersionsWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListAvailableRestoreVersionsResponse, error) {
	rsp, err := c.V1ListAvailableRestoreVersions(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ListAvailableRestoreVersionsResponse(rsp)
}

// V1RestoreAProjectWithResponse request returning *V1RestoreAProjectResponse
func (c *ClientWithResponses) V1RestoreAProjectWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1RestoreAProjectResponse, error) {
	rsp, err := c.V1RestoreAProject(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1RestoreAProjectResponse(rsp)
}

// V1CancelAProjectRestorationWithResponse request returning *V1CancelAProjectRestorationResponse
func (c *ClientWithResponses) V1CancelAProjectRestorationWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1CancelAProjectRestorationResponse, error) {
	rsp, err := c.V1CancelAProjectRestoration(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1CancelAProjectRestorationResponse(rsp)
}

// V1BulkDeleteSecretsWithBodyWithResponse request with arbitrary body returning *V1BulkDeleteSecretsResponse
func (c *ClientWithResponses) V1BulkDeleteSecretsWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1BulkDeleteSecretsResponse, error) {
	rsp, err := c.V1BulkDeleteSecretsWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1BulkDeleteSecretsResponse(rsp)
}

func (c *ClientWithResponses) V1BulkDeleteSecretsWithResponse(ctx context.Context, ref string, body V1BulkDeleteSecretsJSONRequestBody, reqEditors ...RequestEditorFn) (*V1BulkDeleteSecretsResponse, error) {
	rsp, err := c.V1BulkDeleteSecrets(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1BulkDeleteSecretsResponse(rsp)
}

// V1ListAllSecretsWithResponse request returning *V1ListAllSecretsResponse
func (c *ClientWithResponses) V1ListAllSecretsWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListAllSecretsResponse, error) {
	rsp, err := c.V1ListAllSecrets(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ListAllSecretsResponse(rsp)
}

// V1BulkCreateSecretsWithBodyWithResponse request with arbitrary body returning *V1BulkCreateSecretsResponse
func (c *ClientWithResponses) V1BulkCreateSecretsWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1BulkCreateSecretsResponse, error) {
	rsp, err := c.V1BulkCreateSecretsWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1BulkCreateSecretsResponse(rsp)
}

func (c *ClientWithResponses) V1BulkCreateSecretsWithResponse(ctx context.Context, ref string, body V1BulkCreateSecretsJSONRequestBody, reqEditors ...RequestEditorFn) (*V1BulkCreateSecretsResponse, error) {
	rsp, err := c.V1BulkCreateSecrets(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1BulkCreateSecretsResponse(rsp)
}

// V1GetSslEnforcementConfigWithResponse request returning *V1GetSslEnforcementConfigResponse
func (c *ClientWithResponses) V1GetSslEnforcementConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetSslEnforcementConfigResponse, error) {
	rsp, err := c.V1GetSslEnforcementConfig(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetSslEnforcementConfigResponse(rsp)
}

// V1UpdateSslEnforcementConfigWithBodyWithResponse request with arbitrary body returning *V1UpdateSslEnforcementConfigResponse
func (c *ClientWithResponses) V1UpdateSslEnforcementConfigWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpdateSslEnforcementConfigResponse, error) {
	rsp, err := c.V1UpdateSslEnforcementConfigWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdateSslEnforcementConfigResponse(rsp)
}

func (c *ClientWithResponses) V1UpdateSslEnforcementConfigWithResponse(ctx context.Context, ref string, body V1UpdateSslEnforcementConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpdateSslEnforcementConfigResponse, error) {
	rsp, err := c.V1UpdateSslEnforcementConfig(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpdateSslEnforcementConfigResponse(rsp)
}

// V1ListAllBucketsWithResponse request returning *V1ListAllBucketsResponse
func (c *ClientWithResponses) V1ListAllBucketsWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1ListAllBucketsResponse, error) {
	rsp, err := c.V1ListAllBuckets(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ListAllBucketsResponse(rsp)
}

// V1GenerateTypescriptTypesWithResponse request returning *V1GenerateTypescriptTypesResponse
func (c *ClientWithResponses) V1GenerateTypescriptTypesWithResponse(ctx context.Context, ref string, params *V1GenerateTypescriptTypesParams, reqEditors ...RequestEditorFn) (*V1GenerateTypescriptTypesResponse, error) {
	rsp, err := c.V1GenerateTypescriptTypes(ctx, ref, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GenerateTypescriptTypesResponse(rsp)
}

// V1UpgradePostgresVersionWithBodyWithResponse request with arbitrary body returning *V1UpgradePostgresVersionResponse
func (c *ClientWithResponses) V1UpgradePostgresVersionWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1UpgradePostgresVersionResponse, error) {
	rsp, err := c.V1UpgradePostgresVersionWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpgradePostgresVersionResponse(rsp)
}

func (c *ClientWithResponses) V1UpgradePostgresVersionWithResponse(ctx context.Context, ref string, body V1UpgradePostgresVersionJSONRequestBody, reqEditors ...RequestEditorFn) (*V1UpgradePostgresVersionResponse, error) {
	rsp, err := c.V1UpgradePostgresVersion(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1UpgradePostgresVersionResponse(rsp)
}

// V1GetPostgresUpgradeEligibilityWithResponse request returning *V1GetPostgresUpgradeEligibilityResponse
func (c *ClientWithResponses) V1GetPostgresUpgradeEligibilityWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetPostgresUpgradeEligibilityResponse, error) {
	rsp, err := c.V1GetPostgresUpgradeEligibility(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetPostgresUpgradeEligibilityResponse(rsp)
}

// V1GetPostgresUpgradeStatusWithResponse request returning *V1GetPostgresUpgradeStatusResponse
func (c *ClientWithResponses) V1GetPostgresUpgradeStatusWithResponse(ctx context.Context, ref string, params *V1GetPostgresUpgradeStatusParams, reqEditors ...RequestEditorFn) (*V1GetPostgresUpgradeStatusResponse, error) {
	rsp, err := c.V1GetPostgresUpgradeStatus(ctx, ref, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetPostgresUpgradeStatusResponse(rsp)
}

// V1DeactivateVanitySubdomainConfigWithResponse request returning *V1DeactivateVanitySubdomainConfigResponse
func (c *ClientWithResponses) V1DeactivateVanitySubdomainConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1DeactivateVanitySubdomainConfigResponse, error) {
	rsp, err := c.V1DeactivateVanitySubdomainConfig(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1DeactivateVanitySubdomainConfigResponse(rsp)
}

// V1GetVanitySubdomainConfigWithResponse request returning *V1GetVanitySubdomainConfigResponse
func (c *ClientWithResponses) V1GetVanitySubdomainConfigWithResponse(ctx context.Context, ref string, reqEditors ...RequestEditorFn) (*V1GetVanitySubdomainConfigResponse, error) {
	rsp, err := c.V1GetVanitySubdomainConfig(ctx, ref, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetVanitySubdomainConfigResponse(rsp)
}

// V1ActivateVanitySubdomainConfigWithBodyWithResponse request with arbitrary body returning *V1ActivateVanitySubdomainConfigResponse
func (c *ClientWithResponses) V1ActivateVanitySubdomainConfigWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1ActivateVanitySubdomainConfigResponse, error) {
	rsp, err := c.V1ActivateVanitySubdomainConfigWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ActivateVanitySubdomainConfigResponse(rsp)
}

func (c *ClientWithResponses) V1ActivateVanitySubdomainConfigWithResponse(ctx context.Context, ref string, body V1ActivateVanitySubdomainConfigJSONRequestBody, reqEditors ...RequestEditorFn) (*V1ActivateVanitySubdomainConfigResponse, error) {
	rsp, err := c.V1ActivateVanitySubdomainConfig(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ActivateVanitySubdomainConfigResponse(rsp)
}

// V1CheckVanitySubdomainAvailabilityWithBodyWithResponse request with arbitrary body returning *V1CheckVanitySubdomainAvailabilityResponse
func (c *ClientWithResponses) V1CheckVanitySubdomainAvailabilityWithBodyWithResponse(ctx context.Context, ref string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*V1CheckVanitySubdomainAvailabilityResponse, error) {
	rsp, err := c.V1CheckVanitySubdomainAvailabilityWithBody(ctx, ref, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1CheckVanitySubdomainAvailabilityResponse(rsp)
}

func (c *ClientWithResponses) V1CheckVanitySubdomainAvailabilityWithResponse(ctx context.Context, ref string, body V1CheckVanitySubdomainAvailabilityJSONRequestBody, reqEditors ...RequestEditorFn) (*V1CheckVanitySubdomainAvailabilityResponse, error) {
	rsp, err := c.V1CheckVanitySubdomainAvailability(ctx, ref, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1CheckVanitySubdomainAvailabilityResponse(rsp)
}

// V1ListAllSnippetsWithResponse request returning *V1ListAllSnippetsResponse
func (c *ClientWithResponses) V1ListAllSnippetsWithResponse(ctx context.Context, params *V1ListAllSnippetsParams, reqEditors ...RequestEditorFn) (*V1ListAllSnippetsResponse, error) {
	rsp, err := c.V1ListAllSnippets(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1ListAllSnippetsResponse(rsp)
}

// V1GetASnippetWithResponse request returning *V1GetASnippetResponse
func (c *ClientWithResponses) V1GetASnippetWithResponse(ctx context.Context, id openapi_types.UUID, reqEditors ...RequestEditorFn) (*V1GetASnippetResponse, error) {
	rsp, err := c.V1GetASnippet(ctx, id, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseV1GetASnippetResponse(rsp)
}

// ParseV1DeleteABranchResponse parses an HTTP response from a V1DeleteABranchWithResponse call
func ParseV1DeleteABranchResponse(rsp *http.Response) (*V1DeleteABranchResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1DeleteABranchResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest BranchDeleteResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1GetABranchConfigResponse parses an HTTP response from a V1GetABranchConfigWithResponse call
func ParseV1GetABranchConfigResponse(rsp *http.Response) (*V1GetABranchConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetABranchConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest BranchDetailResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1UpdateABranchConfigResponse parses an HTTP response from a V1UpdateABranchConfigWithResponse call
func ParseV1UpdateABranchConfigResponse(rsp *http.Response) (*V1UpdateABranchConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1UpdateABranchConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest BranchResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1DiffABranchResponse parses an HTTP response from a V1DiffABranchWithResponse call
func ParseV1DiffABranchResponse(rsp *http.Response) (*V1DiffABranchResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1DiffABranchResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1MergeABranchResponse parses an HTTP response from a V1MergeABranchWithResponse call
func ParseV1MergeABranchResponse(rsp *http.Response) (*V1MergeABranchResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1MergeABranchResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest BranchUpdateResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseV1PushABranchResponse parses an HTTP response from a V1PushABranchWithResponse call
func ParseV1PushABranchResponse(rsp *http.Response) (*V1PushABranchResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1PushABranchResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest BranchUpdateResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseV1ResetABranchResponse parses an HTTP response from a V1ResetABranchWithResponse call
func ParseV1ResetABranchResponse(rsp *http.Response) (*V1ResetABranchResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ResetABranchResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest BranchUpdateResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseV1AuthorizeUserResponse parses an HTTP response from a V1AuthorizeUserWithResponse call
func ParseV1AuthorizeUserResponse(rsp *http.Response) (*V1AuthorizeUserResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1AuthorizeUserResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1RevokeTokenResponse parses an HTTP response from a V1RevokeTokenWithResponse call
func ParseV1RevokeTokenResponse(rsp *http.Response) (*V1RevokeTokenResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1RevokeTokenResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1ExchangeOauthTokenResponse parses an HTTP response from a V1ExchangeOauthTokenWithResponse call
func ParseV1ExchangeOauthTokenResponse(rsp *http.Response) (*V1ExchangeOauthTokenResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ExchangeOauthTokenResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest OAuthTokenResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseV1ListAllOrganizationsResponse parses an HTTP response from a V1ListAllOrganizationsWithResponse call
func ParseV1ListAllOrganizationsResponse(rsp *http.Response) (*V1ListAllOrganizationsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ListAllOrganizationsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []OrganizationResponseV1
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1CreateAnOrganizationResponse parses an HTTP response from a V1CreateAnOrganizationWithResponse call
func ParseV1CreateAnOrganizationResponse(rsp *http.Response) (*V1CreateAnOrganizationResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1CreateAnOrganizationResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest OrganizationResponseV1
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseV1GetAnOrganizationResponse parses an HTTP response from a V1GetAnOrganizationWithResponse call
func ParseV1GetAnOrganizationResponse(rsp *http.Response) (*V1GetAnOrganizationResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetAnOrganizationResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest V1OrganizationSlugResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1ListOrganizationMembersResponse parses an HTTP response from a V1ListOrganizationMembersWithResponse call
func ParseV1ListOrganizationMembersResponse(rsp *http.Response) (*V1ListOrganizationMembersResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ListOrganizationMembersResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []V1OrganizationMemberResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1GetOrganizationProjectClaimResponse parses an HTTP response from a V1GetOrganizationProjectClaimWithResponse call
func ParseV1GetOrganizationProjectClaimResponse(rsp *http.Response) (*V1GetOrganizationProjectClaimResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetOrganizationProjectClaimResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest OrganizationProjectClaimResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1ClaimProjectForOrganizationResponse parses an HTTP response from a V1ClaimProjectForOrganizationWithResponse call
func ParseV1ClaimProjectForOrganizationResponse(rsp *http.Response) (*V1ClaimProjectForOrganizationResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ClaimProjectForOrganizationResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1ListAllProjectsResponse parses an HTTP response from a V1ListAllProjectsWithResponse call
func ParseV1ListAllProjectsResponse(rsp *http.Response) (*V1ListAllProjectsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ListAllProjectsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []V1ProjectWithDatabaseResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1CreateAProjectResponse parses an HTTP response from a V1CreateAProjectWithResponse call
func ParseV1CreateAProjectResponse(rsp *http.Response) (*V1CreateAProjectResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1CreateAProjectResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest V1ProjectResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseV1DeleteAProjectResponse parses an HTTP response from a V1DeleteAProjectWithResponse call
func ParseV1DeleteAProjectResponse(rsp *http.Response) (*V1DeleteAProjectResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1DeleteAProjectResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest V1ProjectRefResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1GetProjectResponse parses an HTTP response from a V1GetProjectWithResponse call
func ParseV1GetProjectResponse(rsp *http.Response) (*V1GetProjectResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetProjectResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest V1ProjectWithDatabaseResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetPerformanceAdvisorsResponse parses an HTTP response from a GetPerformanceAdvisorsWithResponse call
func ParseGetPerformanceAdvisorsResponse(rsp *http.Response) (*GetPerformanceAdvisorsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetPerformanceAdvisorsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest V1ProjectAdvisorsResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetSecurityAdvisorsResponse parses an HTTP response from a GetSecurityAdvisorsWithResponse call
func ParseGetSecurityAdvisorsResponse(rsp *http.Response) (*GetSecurityAdvisorsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetSecurityAdvisorsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest V1ProjectAdvisorsResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetLogsResponse parses an HTTP response from a GetLogsWithResponse call
func ParseGetLogsResponse(rsp *http.Response) (*GetLogsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetLogsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest AnalyticsResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetApiCountsResponse parses an HTTP response from a GetApiCountsWithResponse call
func ParseGetApiCountsResponse(rsp *http.Response) (*GetApiCountsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApiCountsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest AnalyticsResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetApiRequestsCountResponse parses an HTTP response from a GetApiRequestsCountWithResponse call
func ParseGetApiRequestsCountResponse(rsp *http.Response) (*GetApiRequestsCountResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApiRequestsCountResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest AnalyticsResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1GetProjectApiKeysResponse parses an HTTP response from a V1GetProjectApiKeysWithResponse call
func ParseV1GetProjectApiKeysResponse(rsp *http.Response) (*V1GetProjectApiKeysResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetProjectApiKeysResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []ApiKeyResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseCreateApiKeyResponse parses an HTTP response from a CreateApiKeyWithResponse call
func ParseCreateApiKeyResponse(rsp *http.Response) (*CreateApiKeyResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &CreateApiKeyResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest ApiKeyResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseDeleteApiKeyResponse parses an HTTP response from a DeleteApiKeyWithResponse call
func ParseDeleteApiKeyResponse(rsp *http.Response) (*DeleteApiKeyResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteApiKeyResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ApiKeyResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetApiKeyResponse parses an HTTP response from a GetApiKeyWithResponse call
func ParseGetApiKeyResponse(rsp *http.Response) (*GetApiKeyResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApiKeyResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ApiKeyResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseUpdateApiKeyResponse parses an HTTP response from a UpdateApiKeyWithResponse call
func ParseUpdateApiKeyResponse(rsp *http.Response) (*UpdateApiKeyResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &UpdateApiKeyResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ApiKeyResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1ListProjectAddonsResponse parses an HTTP response from a V1ListProjectAddonsWithResponse call
func ParseV1ListProjectAddonsResponse(rsp *http.Response) (*V1ListProjectAddonsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ListProjectAddonsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ListProjectAddonsResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1ApplyProjectAddonResponse parses an HTTP response from a V1ApplyProjectAddonWithResponse call
func ParseV1ApplyProjectAddonResponse(rsp *http.Response) (*V1ApplyProjectAddonResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ApplyProjectAddonResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1RemoveProjectAddonResponse parses an HTTP response from a V1RemoveProjectAddonWithResponse call
func ParseV1RemoveProjectAddonResponse(rsp *http.Response) (*V1RemoveProjectAddonResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1RemoveProjectAddonResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1DisablePreviewBranchingResponse parses an HTTP response from a V1DisablePreviewBranchingWithResponse call
func ParseV1DisablePreviewBranchingResponse(rsp *http.Response) (*V1DisablePreviewBranchingResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1DisablePreviewBranchingResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1ListAllBranchesResponse parses an HTTP response from a V1ListAllBranchesWithResponse call
func ParseV1ListAllBranchesResponse(rsp *http.Response) (*V1ListAllBranchesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ListAllBranchesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []BranchResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1CreateABranchResponse parses an HTTP response from a V1CreateABranchWithResponse call
func ParseV1CreateABranchResponse(rsp *http.Response) (*V1CreateABranchResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1CreateABranchResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest BranchResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseV1DeleteProjectClaimTokenResponse parses an HTTP response from a V1DeleteProjectClaimTokenWithResponse call
func ParseV1DeleteProjectClaimTokenResponse(rsp *http.Response) (*V1DeleteProjectClaimTokenResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1DeleteProjectClaimTokenResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1GetProjectClaimTokenResponse parses an HTTP response from a V1GetProjectClaimTokenWithResponse call
func ParseV1GetProjectClaimTokenResponse(rsp *http.Response) (*V1GetProjectClaimTokenResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetProjectClaimTokenResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ProjectClaimTokenResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1CreateProjectClaimTokenResponse parses an HTTP response from a V1CreateProjectClaimTokenWithResponse call
func ParseV1CreateProjectClaimTokenResponse(rsp *http.Response) (*V1CreateProjectClaimTokenResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1CreateProjectClaimTokenResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest CreateProjectClaimTokenResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1GetAuthServiceConfigResponse parses an HTTP response from a V1GetAuthServiceConfigWithResponse call
func ParseV1GetAuthServiceConfigResponse(rsp *http.Response) (*V1GetAuthServiceConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetAuthServiceConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest AuthConfigResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1UpdateAuthServiceConfigResponse parses an HTTP response from a V1UpdateAuthServiceConfigWithResponse call
func ParseV1UpdateAuthServiceConfigResponse(rsp *http.Response) (*V1UpdateAuthServiceConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1UpdateAuthServiceConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest AuthConfigResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseListSigningKeysForProjectResponse parses an HTTP response from a ListSigningKeysForProjectWithResponse call
func ParseListSigningKeysForProjectResponse(rsp *http.Response) (*ListSigningKeysForProjectResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &ListSigningKeysForProjectResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest SigningKeysResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseCreateSigningKeyForProjectResponse parses an HTTP response from a CreateSigningKeyForProjectWithResponse call
func ParseCreateSigningKeyForProjectResponse(rsp *http.Response) (*CreateSigningKeyForProjectResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &CreateSigningKeyForProjectResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest SigningKeyResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseDeleteSigningKeyResponse parses an HTTP response from a DeleteSigningKeyWithResponse call
func ParseDeleteSigningKeyResponse(rsp *http.Response) (*DeleteSigningKeyResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteSigningKeyResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest SigningKeyResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetSigningKeyForProjectResponse parses an HTTP response from a GetSigningKeyForProjectWithResponse call
func ParseGetSigningKeyForProjectResponse(rsp *http.Response) (*GetSigningKeyForProjectResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetSigningKeyForProjectResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest SigningKeyResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePatchSigningKeyResponse parses an HTTP response from a PatchSigningKeyWithResponse call
func ParsePatchSigningKeyResponse(rsp *http.Response) (*PatchSigningKeyResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PatchSigningKeyResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest SigningKeyResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1ListAllSsoProviderResponse parses an HTTP response from a V1ListAllSsoProviderWithResponse call
func ParseV1ListAllSsoProviderResponse(rsp *http.Response) (*V1ListAllSsoProviderResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ListAllSsoProviderResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ListProvidersResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1CreateASsoProviderResponse parses an HTTP response from a V1CreateASsoProviderWithResponse call
func ParseV1CreateASsoProviderResponse(rsp *http.Response) (*V1CreateASsoProviderResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1CreateASsoProviderResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest CreateProviderResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseV1DeleteASsoProviderResponse parses an HTTP response from a V1DeleteASsoProviderWithResponse call
func ParseV1DeleteASsoProviderResponse(rsp *http.Response) (*V1DeleteASsoProviderResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1DeleteASsoProviderResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DeleteProviderResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1GetASsoProviderResponse parses an HTTP response from a V1GetASsoProviderWithResponse call
func ParseV1GetASsoProviderResponse(rsp *http.Response) (*V1GetASsoProviderResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetASsoProviderResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest GetProviderResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1UpdateASsoProviderResponse parses an HTTP response from a V1UpdateASsoProviderWithResponse call
func ParseV1UpdateASsoProviderResponse(rsp *http.Response) (*V1UpdateASsoProviderResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1UpdateASsoProviderResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest UpdateProviderResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseListTPAForProjectResponse parses an HTTP response from a ListTPAForProjectWithResponse call
func ParseListTPAForProjectResponse(rsp *http.Response) (*ListTPAForProjectResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &ListTPAForProjectResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []ThirdPartyAuth
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseCreateTPAForProjectResponse parses an HTTP response from a CreateTPAForProjectWithResponse call
func ParseCreateTPAForProjectResponse(rsp *http.Response) (*CreateTPAForProjectResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &CreateTPAForProjectResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest ThirdPartyAuth
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseDeleteTPAForProjectResponse parses an HTTP response from a DeleteTPAForProjectWithResponse call
func ParseDeleteTPAForProjectResponse(rsp *http.Response) (*DeleteTPAForProjectResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteTPAForProjectResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ThirdPartyAuth
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetTPAForProjectResponse parses an HTTP response from a GetTPAForProjectWithResponse call
func ParseGetTPAForProjectResponse(rsp *http.Response) (*GetTPAForProjectResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetTPAForProjectResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ThirdPartyAuth
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1GetProjectPgbouncerConfigResponse parses an HTTP response from a V1GetProjectPgbouncerConfigWithResponse call
func ParseV1GetProjectPgbouncerConfigResponse(rsp *http.Response) (*V1GetProjectPgbouncerConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetProjectPgbouncerConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest V1PgbouncerConfigResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1GetPoolerConfigResponse parses an HTTP response from a V1GetPoolerConfigWithResponse call
func ParseV1GetPoolerConfigResponse(rsp *http.Response) (*V1GetPoolerConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetPoolerConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []SupavisorConfigResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1UpdatePoolerConfigResponse parses an HTTP response from a V1UpdatePoolerConfigWithResponse call
func ParseV1UpdatePoolerConfigResponse(rsp *http.Response) (*V1UpdatePoolerConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1UpdatePoolerConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest UpdateSupavisorConfigResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1GetPostgresConfigResponse parses an HTTP response from a V1GetPostgresConfigWithResponse call
func ParseV1GetPostgresConfigResponse(rsp *http.Response) (*V1GetPostgresConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetPostgresConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest PostgresConfigResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1UpdatePostgresConfigResponse parses an HTTP response from a V1UpdatePostgresConfigWithResponse call
func ParseV1UpdatePostgresConfigResponse(rsp *http.Response) (*V1UpdatePostgresConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1UpdatePostgresConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest PostgresConfigResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1GetStorageConfigResponse parses an HTTP response from a V1GetStorageConfigWithResponse call
func ParseV1GetStorageConfigResponse(rsp *http.Response) (*V1GetStorageConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetStorageConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest StorageConfigResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1UpdateStorageConfigResponse parses an HTTP response from a V1UpdateStorageConfigWithResponse call
func ParseV1UpdateStorageConfigResponse(rsp *http.Response) (*V1UpdateStorageConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1UpdateStorageConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1DeleteHostnameConfigResponse parses an HTTP response from a V1DeleteHostnameConfigWithResponse call
func ParseV1DeleteHostnameConfigResponse(rsp *http.Response) (*V1DeleteHostnameConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1DeleteHostnameConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1GetHostnameConfigResponse parses an HTTP response from a V1GetHostnameConfigWithResponse call
func ParseV1GetHostnameConfigResponse(rsp *http.Response) (*V1GetHostnameConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetHostnameConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest UpdateCustomHostnameResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1ActivateCustomHostnameResponse parses an HTTP response from a V1ActivateCustomHostnameWithResponse call
func ParseV1ActivateCustomHostnameResponse(rsp *http.Response) (*V1ActivateCustomHostnameResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ActivateCustomHostnameResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest UpdateCustomHostnameResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseV1UpdateHostnameConfigResponse parses an HTTP response from a V1UpdateHostnameConfigWithResponse call
func ParseV1UpdateHostnameConfigResponse(rsp *http.Response) (*V1UpdateHostnameConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1UpdateHostnameConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest UpdateCustomHostnameResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseV1VerifyDnsConfigResponse parses an HTTP response from a V1VerifyDnsConfigWithResponse call
func ParseV1VerifyDnsConfigResponse(rsp *http.Response) (*V1VerifyDnsConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1VerifyDnsConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest UpdateCustomHostnameResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseV1ListAllBackupsResponse parses an HTTP response from a V1ListAllBackupsWithResponse call
func ParseV1ListAllBackupsResponse(rsp *http.Response) (*V1ListAllBackupsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ListAllBackupsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest V1BackupsResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1RestorePitrBackupResponse parses an HTTP response from a V1RestorePitrBackupWithResponse call
func ParseV1RestorePitrBackupResponse(rsp *http.Response) (*V1RestorePitrBackupResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1RestorePitrBackupResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseGetDatabaseMetadataResponse parses an HTTP response from a GetDatabaseMetadataWithResponse call
func ParseGetDatabaseMetadataResponse(rsp *http.Response) (*GetDatabaseMetadataResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetDatabaseMetadataResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest GetProjectDbMetadataResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1ListMigrationHistoryResponse parses an HTTP response from a V1ListMigrationHistoryWithResponse call
func ParseV1ListMigrationHistoryResponse(rsp *http.Response) (*V1ListMigrationHistoryResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ListMigrationHistoryResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest V1ListMigrationsResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1ApplyAMigrationResponse parses an HTTP response from a V1ApplyAMigrationWithResponse call
func ParseV1ApplyAMigrationResponse(rsp *http.Response) (*V1ApplyAMigrationResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ApplyAMigrationResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1RunAQueryResponse parses an HTTP response from a V1RunAQueryWithResponse call
func ParseV1RunAQueryResponse(rsp *http.Response) (*V1RunAQueryResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1RunAQueryResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1EnableDatabaseWebhookResponse parses an HTTP response from a V1EnableDatabaseWebhookWithResponse call
func ParseV1EnableDatabaseWebhookResponse(rsp *http.Response) (*V1EnableDatabaseWebhookResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1EnableDatabaseWebhookResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1ListAllFunctionsResponse parses an HTTP response from a V1ListAllFunctionsWithResponse call
func ParseV1ListAllFunctionsResponse(rsp *http.Response) (*V1ListAllFunctionsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ListAllFunctionsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []FunctionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1CreateAFunctionResponse parses an HTTP response from a V1CreateAFunctionWithResponse call
func ParseV1CreateAFunctionResponse(rsp *http.Response) (*V1CreateAFunctionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1CreateAFunctionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest FunctionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseV1BulkUpdateFunctionsResponse parses an HTTP response from a V1BulkUpdateFunctionsWithResponse call
func ParseV1BulkUpdateFunctionsResponse(rsp *http.Response) (*V1BulkUpdateFunctionsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1BulkUpdateFunctionsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest BulkUpdateFunctionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1DeployAFunctionResponse parses an HTTP response from a V1DeployAFunctionWithResponse call
func ParseV1DeployAFunctionResponse(rsp *http.Response) (*V1DeployAFunctionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1DeployAFunctionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest DeployFunctionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseV1DeleteAFunctionResponse parses an HTTP response from a V1DeleteAFunctionWithResponse call
func ParseV1DeleteAFunctionResponse(rsp *http.Response) (*V1DeleteAFunctionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1DeleteAFunctionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1GetAFunctionResponse parses an HTTP response from a V1GetAFunctionWithResponse call
func ParseV1GetAFunctionResponse(rsp *http.Response) (*V1GetAFunctionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetAFunctionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest FunctionSlugResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1UpdateAFunctionResponse parses an HTTP response from a V1UpdateAFunctionWithResponse call
func ParseV1UpdateAFunctionResponse(rsp *http.Response) (*V1UpdateAFunctionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1UpdateAFunctionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest FunctionResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1GetAFunctionBodyResponse parses an HTTP response from a V1GetAFunctionBodyWithResponse call
func ParseV1GetAFunctionBodyResponse(rsp *http.Response) (*V1GetAFunctionBodyResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetAFunctionBodyResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest StreamableFile
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1GetServicesHealthResponse parses an HTTP response from a V1GetServicesHealthWithResponse call
func ParseV1GetServicesHealthResponse(rsp *http.Response) (*V1GetServicesHealthResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetServicesHealthResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []V1ServiceHealthResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1DeleteNetworkBansResponse parses an HTTP response from a V1DeleteNetworkBansWithResponse call
func ParseV1DeleteNetworkBansResponse(rsp *http.Response) (*V1DeleteNetworkBansResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1DeleteNetworkBansResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1ListAllNetworkBansResponse parses an HTTP response from a V1ListAllNetworkBansWithResponse call
func ParseV1ListAllNetworkBansResponse(rsp *http.Response) (*V1ListAllNetworkBansResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ListAllNetworkBansResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest NetworkBanResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseV1ListAllNetworkBansEnrichedResponse parses an HTTP response from a V1ListAllNetworkBansEnrichedWithResponse call
func ParseV1ListAllNetworkBansEnrichedResponse(rsp *http.Response) (*V1ListAllNetworkBansEnrichedResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ListAllNetworkBansEnrichedResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest NetworkBanResponseEnriched
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseV1GetNetworkRestrictionsResponse parses an HTTP response from a V1GetNetworkRestrictionsWithResponse call
func ParseV1GetNetworkRestrictionsResponse(rsp *http.Response) (*V1GetNetworkRestrictionsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetNetworkRestrictionsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest NetworkRestrictionsResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1UpdateNetworkRestrictionsResponse parses an HTTP response from a V1UpdateNetworkRestrictionsWithResponse call
func ParseV1UpdateNetworkRestrictionsResponse(rsp *http.Response) (*V1UpdateNetworkRestrictionsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1UpdateNetworkRestrictionsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest NetworkRestrictionsResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseV1PauseAProjectResponse parses an HTTP response from a V1PauseAProjectWithResponse call
func ParseV1PauseAProjectResponse(rsp *http.Response) (*V1PauseAProjectResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1PauseAProjectResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1GetPgsodiumConfigResponse parses an HTTP response from a V1GetPgsodiumConfigWithResponse call
func ParseV1GetPgsodiumConfigResponse(rsp *http.Response) (*V1GetPgsodiumConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetPgsodiumConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest PgsodiumConfigResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1UpdatePgsodiumConfigResponse parses an HTTP response from a V1UpdatePgsodiumConfigWithResponse call
func ParseV1UpdatePgsodiumConfigResponse(rsp *http.Response) (*V1UpdatePgsodiumConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1UpdatePgsodiumConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest PgsodiumConfigResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1GetPostgrestServiceConfigResponse parses an HTTP response from a V1GetPostgrestServiceConfigWithResponse call
func ParseV1GetPostgrestServiceConfigResponse(rsp *http.Response) (*V1GetPostgrestServiceConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetPostgrestServiceConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest PostgrestConfigWithJWTSecretResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1UpdatePostgrestServiceConfigResponse parses an HTTP response from a V1UpdatePostgrestServiceConfigWithResponse call
func ParseV1UpdatePostgrestServiceConfigResponse(rsp *http.Response) (*V1UpdatePostgrestServiceConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1UpdatePostgrestServiceConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest V1PostgrestConfigResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1RemoveAReadReplicaResponse parses an HTTP response from a V1RemoveAReadReplicaWithResponse call
func ParseV1RemoveAReadReplicaResponse(rsp *http.Response) (*V1RemoveAReadReplicaResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1RemoveAReadReplicaResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1SetupAReadReplicaResponse parses an HTTP response from a V1SetupAReadReplicaWithResponse call
func ParseV1SetupAReadReplicaResponse(rsp *http.Response) (*V1SetupAReadReplicaResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1SetupAReadReplicaResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1GetReadonlyModeStatusResponse parses an HTTP response from a V1GetReadonlyModeStatusWithResponse call
func ParseV1GetReadonlyModeStatusResponse(rsp *http.Response) (*V1GetReadonlyModeStatusResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetReadonlyModeStatusResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ReadOnlyStatusResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1DisableReadonlyModeTemporarilyResponse parses an HTTP response from a V1DisableReadonlyModeTemporarilyWithResponse call
func ParseV1DisableReadonlyModeTemporarilyResponse(rsp *http.Response) (*V1DisableReadonlyModeTemporarilyResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1DisableReadonlyModeTemporarilyResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1ListAvailableRestoreVersionsResponse parses an HTTP response from a V1ListAvailableRestoreVersionsWithResponse call
func ParseV1ListAvailableRestoreVersionsResponse(rsp *http.Response) (*V1ListAvailableRestoreVersionsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ListAvailableRestoreVersionsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest GetProjectAvailableRestoreVersionsResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1RestoreAProjectResponse parses an HTTP response from a V1RestoreAProjectWithResponse call
func ParseV1RestoreAProjectResponse(rsp *http.Response) (*V1RestoreAProjectResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1RestoreAProjectResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1CancelAProjectRestorationResponse parses an HTTP response from a V1CancelAProjectRestorationWithResponse call
func ParseV1CancelAProjectRestorationResponse(rsp *http.Response) (*V1CancelAProjectRestorationResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1CancelAProjectRestorationResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1BulkDeleteSecretsResponse parses an HTTP response from a V1BulkDeleteSecretsWithResponse call
func ParseV1BulkDeleteSecretsResponse(rsp *http.Response) (*V1BulkDeleteSecretsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1BulkDeleteSecretsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1ListAllSecretsResponse parses an HTTP response from a V1ListAllSecretsWithResponse call
func ParseV1ListAllSecretsResponse(rsp *http.Response) (*V1ListAllSecretsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ListAllSecretsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []SecretResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1BulkCreateSecretsResponse parses an HTTP response from a V1BulkCreateSecretsWithResponse call
func ParseV1BulkCreateSecretsResponse(rsp *http.Response) (*V1BulkCreateSecretsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1BulkCreateSecretsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1GetSslEnforcementConfigResponse parses an HTTP response from a V1GetSslEnforcementConfigWithResponse call
func ParseV1GetSslEnforcementConfigResponse(rsp *http.Response) (*V1GetSslEnforcementConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetSslEnforcementConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest SslEnforcementResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1UpdateSslEnforcementConfigResponse parses an HTTP response from a V1UpdateSslEnforcementConfigWithResponse call
func ParseV1UpdateSslEnforcementConfigResponse(rsp *http.Response) (*V1UpdateSslEnforcementConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1UpdateSslEnforcementConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest SslEnforcementResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1ListAllBucketsResponse parses an HTTP response from a V1ListAllBucketsWithResponse call
func ParseV1ListAllBucketsResponse(rsp *http.Response) (*V1ListAllBucketsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ListAllBucketsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest []V1StorageBucketResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1GenerateTypescriptTypesResponse parses an HTTP response from a V1GenerateTypescriptTypesWithResponse call
func ParseV1GenerateTypescriptTypesResponse(rsp *http.Response) (*V1GenerateTypescriptTypesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GenerateTypescriptTypesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest TypescriptResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1UpgradePostgresVersionResponse parses an HTTP response from a V1UpgradePostgresVersionWithResponse call
func ParseV1UpgradePostgresVersionResponse(rsp *http.Response) (*V1UpgradePostgresVersionResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1UpgradePostgresVersionResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest ProjectUpgradeInitiateResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseV1GetPostgresUpgradeEligibilityResponse parses an HTTP response from a V1GetPostgresUpgradeEligibilityWithResponse call
func ParseV1GetPostgresUpgradeEligibilityResponse(rsp *http.Response) (*V1GetPostgresUpgradeEligibilityResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetPostgresUpgradeEligibilityResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ProjectUpgradeEligibilityResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1GetPostgresUpgradeStatusResponse parses an HTTP response from a V1GetPostgresUpgradeStatusWithResponse call
func ParseV1GetPostgresUpgradeStatusResponse(rsp *http.Response) (*V1GetPostgresUpgradeStatusResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetPostgresUpgradeStatusResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DatabaseUpgradeStatusResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1DeactivateVanitySubdomainConfigResponse parses an HTTP response from a V1DeactivateVanitySubdomainConfigWithResponse call
func ParseV1DeactivateVanitySubdomainConfigResponse(rsp *http.Response) (*V1DeactivateVanitySubdomainConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1DeactivateVanitySubdomainConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	return response, nil
}

// ParseV1GetVanitySubdomainConfigResponse parses an HTTP response from a V1GetVanitySubdomainConfigWithResponse call
func ParseV1GetVanitySubdomainConfigResponse(rsp *http.Response) (*V1GetVanitySubdomainConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetVanitySubdomainConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest VanitySubdomainConfigResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1ActivateVanitySubdomainConfigResponse parses an HTTP response from a V1ActivateVanitySubdomainConfigWithResponse call
func ParseV1ActivateVanitySubdomainConfigResponse(rsp *http.Response) (*V1ActivateVanitySubdomainConfigResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ActivateVanitySubdomainConfigResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest ActivateVanitySubdomainResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseV1CheckVanitySubdomainAvailabilityResponse parses an HTTP response from a V1CheckVanitySubdomainAvailabilityWithResponse call
func ParseV1CheckVanitySubdomainAvailabilityResponse(rsp *http.Response) (*V1CheckVanitySubdomainAvailabilityResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1CheckVanitySubdomainAvailabilityResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 201:
		var dest SubdomainAvailabilityResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON201 = &dest

	}

	return response, nil
}

// ParseV1ListAllSnippetsResponse parses an HTTP response from a V1ListAllSnippetsWithResponse call
func ParseV1ListAllSnippetsResponse(rsp *http.Response) (*V1ListAllSnippetsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1ListAllSnippetsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest SnippetList
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseV1GetASnippetResponse parses an HTTP response from a V1GetASnippetWithResponse call
func ParseV1GetASnippetResponse(rsp *http.Response) (*V1GetASnippetResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &V1GetASnippetResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest SnippetResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}
