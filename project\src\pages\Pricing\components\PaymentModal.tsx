const _handlePayment = async () => {
  setError("");
  setIsLoading(true);

  try {
    // Check if Square is configured
    if (!isSquareConfigured()) {
      setError("Payment processing is not yet configured. Please contact support to complete your order.");
      setIsLoading(false);
      return;
    }

    const _result = await processResidentialPayment(
      formData,
      plan.price,
      user
    );

    // ... existing code ...
  } catch (_error) {
    // ... existing code ...
  }
};