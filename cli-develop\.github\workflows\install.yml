name: Install

on:
  pull_request:
    paths:
      - '.github/workflows/install.yml'
      - 'package.json'
      - 'scripts/**'
  push:
    branches:
      - develop
    paths:
      - '.github/workflows/install.yml'
      - 'package.json'
      - 'scripts/**'

permissions:
  contents: write

jobs:
  pack:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - run: |
          jq -c '.version = "1.28.0"' package.json > tmp.$$.json
          mv tmp.$$.json package.json
          npm pack

      - uses: actions/upload-artifact@v4
        with:
          name: installer
          path: supabase-1.28.0.tgz

  npm:
    needs: pack
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/download-artifact@v4
        with:
          name: installer

      - run: npm init -y
      - run: npm i --save-dev ./supabase-1.28.0.tgz
      - run: npx --no-install supabase --version

  yarn:
    needs: pack
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/download-artifact@v4
        with:
          name: installer

      - run: yarn init -y
      - run: yarn add -D ./supabase-1.28.0.tgz
      - run: yarn supabase --version

  yarn_berry:
    needs: pack
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/download-artifact@v4
        with:
          name: installer

      - run: yarn set version berry
      # - run: yarn config set nodeLinker node-modules
      - run: yarn init -y
      - run: yarn add -D ./supabase-1.28.0.tgz
      - if: ${{ matrix.os != 'windows-latest' }}
        run: yarn supabase --version
      # Workaround for running extensionless executable on windows
      - if: ${{ matrix.os == 'windows-latest' }}
        run: |
          & "$(yarn bin supabase).exe" --version

  pnpm:
    needs: pack
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/download-artifact@v4
        with:
          name: installer

      - run: npm install -g pnpm
      - run: pnpm init
      # https://github.com/pnpm/pnpm/issues/9124#issuecomment-2663021284
      - run: pnpm i --save-dev ./supabase-1.28.0.tgz --allow-build=supabase
      - run: pnpm supabase --version

  bun:
    needs: pack
    strategy:
      fail-fast: false
      matrix:
        # Bun build is experimental on windows
        os: [ubuntu-latest, macos-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/download-artifact@v4
        with:
          name: installer

      - uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest
      - run: |
          echo '{"trustedDependencies": ["supabase"]}' > package.json
      - run: bun add -D ./supabase-1.28.0.tgz
      - run: bunx supabase --version
