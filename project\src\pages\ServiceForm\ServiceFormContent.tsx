import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Shield, Clock, Leaf, Users, Wrench, CheckCircle, LucideIcon } from 'lucide-react';
import { ServiceFormFields } from './ServiceFormFields';
import type { ServiceType } from '../../components/services/data/services';

interface ServiceFormContentProps {
  service: ServiceType;
  onBack: () => void;
}

export function ServiceFormContent({ service, onBack }: ServiceFormContentProps) {
  const Icon = service.icon;

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <button
        onClick={onBack}
        className="mb-8 flex items-center text-gray-600 hover:text-gray-900 transition-colors"
      >
        <ArrowLeft className="w-5 h-5 mr-2" />
        Back to Services
      </button>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Form Section */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-white rounded-2xl shadow-xl p-8"
        >
          <div className="flex items-center space-x-4 mb-8">
            <div className="rounded-xl bg-brand-100 p-4">
              <Icon className="w-8 h-8 text-brand-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{service.title}</h1>
              <p className="text-gray-600">{service.description}</p>
            </div>
          </div>

          <ServiceFormFields serviceId={service.id} />
        </motion.div>

        {/* Info Section */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-8"
        >
          {/* Service Image */}
          <div className="aspect-w-16 aspect-h-9 rounded-2xl overflow-hidden">
            <img
              src={getServiceImage(service.id)}
              alt={service.title}
              className="object-cover w-full h-full"
            />
          </div>

          {/* Service Features */}
          <div className="bg-white rounded-xl p-6 shadow-lg">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Why Choose Our {service.title}
            </h3>
            <ul className="space-y-3">
              {getServiceFeatures(service.id).map((feature, index) => (
                <li key={index} className="flex items-start">
                  <div className="rounded-full bg-brand-100 p-1 mr-3 mt-1">
                    <feature.icon className="w-4 h-4 text-brand-600" />
                  </div>
                  <span className="text-gray-700">{feature.text}</span>
                </li>
              ))}
            </ul>
          </div>
        </motion.div>
      </div>
    </div>
  );
}

// Helper functions to get service-specific content
function getServiceImage(serviceId: string): string {
  const images: Record<string, string> = {
    office: 'https://images.unsplash.com/photo-1497366216548-37526070297c',
    deep: 'https://images.unsplash.com/photo-1584744982491-665216d95f8b',
    window: 'https://images.unsplash.com/photo-1580893246395-52aead8960dc',
    carpet: 'https://images.unsplash.com/photo-1603201667230-bd139210db18',
    construction: 'https://images.unsplash.com/photo-1503387762-592deb58ef4e',
    sanitization: 'https://images.unsplash.com/photo-1584744982491-665216d95f8b',
    tile: 'https://images.unsplash.com/photo-1594212699903-ec8a3eca50f5',
    pressure: 'https://images.unsplash.com/photo-1610557892470-55d9e80c0bce',
    floor: 'https://images.unsplash.com/photo-1581858726788-75bc0f6a952d'
  };
  
  return images[serviceId] || images.office;
}

function getServiceFeatures(serviceId: string) {
  const features: Record<string, Array<{ icon: LucideIcon; text: string }>> = {
    office: [
      { icon: Shield, text: 'Trained & vetted cleaning professionals' },
      { icon: Clock, text: 'Flexible scheduling options' },
      { icon: Leaf, text: 'Eco-friendly cleaning products' },
      { icon: Users, text: 'Dedicated cleaning team' }
    ],
    deep: [
      { icon: Shield, text: 'Professional deep cleaning experts' },
      { icon: Wrench, text: 'Advanced cleaning equipment' },
      { icon: CheckCircle, text: 'Thorough sanitization process' },
      { icon: Leaf, text: 'Safe cleaning solutions' }
    ]
    // Add features for other services as needed
  };
  
  return features[serviceId] || features.office;
}