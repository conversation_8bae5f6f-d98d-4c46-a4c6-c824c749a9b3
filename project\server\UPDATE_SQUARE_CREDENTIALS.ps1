# Square Credentials Update Script
Write-Host "====================================" -ForegroundColor Cyan
Write-Host "Square Credentials Update Helper" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host ""

# Prompt for Access Token
Write-Host "Step 1: Enter your Square Sandbox Access Token" -ForegroundColor Yellow
Write-Host "(It should start with 'EAAAE')" -ForegroundColor Gray
$accessToken = Read-Host "Access Token"

# Prompt for Location ID
Write-Host ""
Write-Host "Step 2: Enter your Square Sandbox Location ID" -ForegroundColor Yellow
Write-Host "(It should start with 'L')" -ForegroundColor Gray
$locationId = Read-Host "Location ID"

# Validate inputs
if ($accessToken -eq "" -or $locationId -eq "") {
    Write-Host ""
    Write-Host "ERROR: Both values are required!" -ForegroundColor Red
    exit
}

# Update the .env file
Write-Host ""
Write-Host "Updating .env file..." -ForegroundColor Green

$envContent = Get-Content .env
$envContent = $envContent -replace "SQUARE_ACCESS_TOKEN=.*", "SQUARE_ACCESS_TOKEN=$accessToken"
$envContent = $envContent -replace "SQUARE_LOCATION_ID=.*", "SQUARE_LOCATION_ID=$locationId"
$envContent | Set-Content .env

Write-Host "✅ .env file updated!" -ForegroundColor Green
Write-Host ""

# Verify the update
Write-Host "Verifying..." -ForegroundColor Yellow
$updatedContent = Get-Content .env | Where-Object { $_ -match "SQUARE_" }
$updatedContent | ForEach-Object { Write-Host $_ -ForegroundColor Cyan }

Write-Host ""
Write-Host "✅ Square credentials have been updated!" -ForegroundColor Green
Write-Host ""
Write-Host "Now restart the server by running:" -ForegroundColor Yellow
Write-Host ".\RESTART_WITH_SQUARE.bat" -ForegroundColor Cyan
Write-Host "" 