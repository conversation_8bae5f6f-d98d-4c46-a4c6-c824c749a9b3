#!/usr/bin/env node

/**
 * <PERSON>ript to automatically fix common ESLint issues
 */

const fs = require('fs');
const path = require('path');

// Common unused imports to remove
const UNUSED_IMPORTS = [
  'DollarSign', 'Star', 'Shield', 'Award', 'MapPin', 'Phone', 'Mail', 'Clock',
  'CheckCircle', 'Button', 'Paperclip', 'Search', 'User', 'CheckCircle2', 
  'AlertCircle', 'Building2', 'Bell', 'GlassWater', 'Brush', 'Sprout',
  'Hammer', 'Users', 'Sparkles', 'Upload', 'Layers', 'Building', 'Ruler',
  'Car', 'motion', 'Grid', 'Check', 'GraduationCap', 'Warehouse', 'Calendar',
  'FormField', 'Client', 'Environment'
];

// Files to process
const SRC_DIR = './src';

function findTsxFiles(dir) {
  const files = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.includes('node_modules')) {
      files.push(...findTsxFiles(fullPath));
    } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

function fixUnusedImports(content) {
  // Remove unused imports from lucide-react
  const importRegex = /import\s*{\s*([^}]+)\s*}\s*from\s*['"]lucide-react['"];?/g;
  
  return content.replace(importRegex, (match, imports) => {
    const importList = imports.split(',').map(imp => imp.trim());
    const usedImports = importList.filter(imp => {
      const cleanImp = imp.trim();
      if (UNUSED_IMPORTS.includes(cleanImp)) {
        // Check if this import is actually used in the file
        const usageRegex = new RegExp(`\\b${cleanImp}\\b`, 'g');
        const matches = content.match(usageRegex) || [];
        // If it appears only in the import statement, it's unused
        return matches.length > 1;
      }
      return true;
    });
    
    if (usedImports.length === 0) {
      return ''; // Remove entire import line
    }
    
    return `import { ${usedImports.join(', ')} } from 'lucide-react';`;
  });
}

function fixUnusedVariables(content) {
  // Add underscore prefix to unused variables
  const patterns = [
    // Function parameters
    /(\w+):\s*\w+\s*\)\s*=>/g,
    // Destructured variables
    /const\s*{\s*([^}]+)\s*}/g,
    // Regular variables
    /const\s+(\w+)\s*=/g,
    // Function parameters in function declarations
    /function\s+\w+\s*\([^)]*(\w+):\s*\w+[^)]*\)/g
  ];
  
  // This is a simplified approach - in practice, you'd need more sophisticated parsing
  return content;
}

function addUnderscoreToUnused(content) {
  // Simple replacements for common unused variables
  const replacements = [
    ['onBack', '_onBack'],
    ['user', '_user'],
    ['error', '_error'],
    ['index', '_index'],
    ['delay', '_delay'],
    ['serviceId', '_serviceId'],
    ['categoryIndex', '_categoryIndex'],
    ['loading', '_loading'],
    ['result', '_result'],
    ['handlePayment', '_handlePayment'],
    ['location', '_location'],
    ['handleBookClick', '_handleBookClick'],
    ['controls', '_controls'],
    ['image', '_image'],
    ['currentCleaningOptions', '_currentCleaningOptions'],
    ['idempotencyKey', '_idempotencyKey']
  ];
  
  let updatedContent = content;
  
  for (const [unused, prefixed] of replacements) {
    // Only replace if it's a variable declaration, not usage
    const patterns = [
      new RegExp(`(const|let|var)\\s+${unused}\\s*=`, 'g'),
      new RegExp(`\\b${unused}:\\s*\\w+\\s*[,)]`, 'g'),
      new RegExp(`\\b${unused}\\s*,`, 'g'),
      new RegExp(`{\\s*${unused}\\s*}`, 'g')
    ];
    
    for (const pattern of patterns) {
      updatedContent = updatedContent.replace(pattern, (match) => {
        return match.replace(unused, prefixed);
      });
    }
  }
  
  return updatedContent;
}

function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    // Apply fixes
    content = fixUnusedImports(content);
    content = addUnderscoreToUnused(content);
    
    // Only write if content changed
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content);
      console.log(`Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('Starting ESLint fixes...');
  
  const files = findTsxFiles(SRC_DIR);
  let fixedCount = 0;
  
  for (const file of files) {
    if (processFile(file)) {
      fixedCount++;
    }
  }
  
  console.log(`\nFixed ${fixedCount} files out of ${files.length} total files.`);
  console.log('Run "npm run lint" to see remaining issues.');
}

if (require.main === module) {
  main();
}

module.exports = { processFile, fixUnusedImports, addUnderscoreToUnused };
