pid pids/nginx.pid;                      # this setting is mandatory
error_log logs/error.log ${{LOG_LEVEL}}; # can be set by kong.conf

daemon ${{NGINX_DAEMON}};                     # can be set by kong.conf
worker_processes ${{NGINX_WORKER_PROCESSES}}; # can be set by kong.conf

events {
    multi_accept on;
}

http {
    # here, we declare our custom location serving our website
    # (or API portal) which we can optimize for serving static assets
    server {
        server_name email_templates;
        listen 0.0.0.0:8088 reuseport backlog=16384;

        access_log logs/email_templates_access.log;
        error_log  logs/error.log notice;

        location /email {
            autoindex on;
            root /home/<USER>/templates;
        }
    }

    # include default Kong Nginx config
    include 'nginx-kong.conf';
}
