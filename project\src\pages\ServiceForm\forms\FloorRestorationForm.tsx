import React from 'react';
import { Form, FormField } from '../components/FormFields';

export function FloorRestorationForm() {
  return (
    <Form onSubmit={(data: Record<string, unknown>) => console.log(data)}>
      <FormField label="Floor Type" required>
        <select
          name="floorType"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        >
          <option value="hardwood">Hardwood</option>
          <option value="marble">Marble</option>
          <option value="terrazzo">Terrazzo</option>
          <option value="vinyl">Vinyl/VCT</option>
          <option value="concrete">Polished Concrete</option>
        </select>
      </FormField>

      <FormField label="Square Footage" required>
        <input
          type="number"
          name="squareFootage"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        />
      </FormField>

      <FormField label="Current Condition" required>
        <select
          name="condition"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        >
          <option value="light">Light Wear</option>
          <option value="moderate">Moderate Wear</option>
          <option value="heavy">Heavy Wear</option>
          <option value="damaged">Damaged/Scratched</option>
        </select>
      </FormField>

      <FormField label="Services Needed">
        <div className="space-y-2">
          {[
            'Deep Cleaning',
            'Stripping & Waxing',
            'Buffing & Polishing',
            'Scratch Removal',
            'Sealing',
            'Color Restoration'
          ].map((service) => (
            <label key={service} className="flex items-center">
              <input
                type="checkbox"
                name="services"
                value={service.toLowerCase().replace(/\s+/g, '-')}
                className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
              />
              <span className="ml-2 text-gray-700">{service}</span>
            </label>
          ))}
        </div>
      </FormField>

      <FormField label="Special Instructions">
        <textarea
          name="instructions"
          rows={3}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          placeholder="Any specific requirements or concerns about the restoration process?"
        />
      </FormField>
    </Form>
  );
}
