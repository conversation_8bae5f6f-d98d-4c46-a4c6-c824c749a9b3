import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Calendar, Clock, MapPin, Building2,
  CheckCircle, XCircle, AlertCircle, ArrowRight,
  FileText, Search, Filter, Loader2
} from 'lucide-react';
import { Button } from '../ui/Button';
import { useAuth } from '../../lib/auth/AuthProvider';
import { getDashboardData, updateBookingStatus } from '../../lib/api/dashboardData';
import { ServiceMenu } from '../services/ServiceMenu';
import { BookingDetailsModal } from './BookingDetailsModal';

export function BookingsList() {
  const { user } = useAuth();
  const [bookings, setBookings] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showServiceMenu, setShowServiceMenu] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState<any>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [activeTab, setActiveTab] = useState<'pending' | 'confirmed'>('pending');
  const [isConfirming, setIsConfirming] = useState(false);

  const fetchBookings = async () => {
    if (!user) {
      setLoading(false);
      return;
    }
    
    try {
      setError(null);
      const data = await getDashboardData(user);
      setBookings(data.requests || []);
    } catch (err) {
      console.error('Error loading bookings:', err);
      setError(err instanceof Error ? err.message : 'Failed to load bookings');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBookings();
  }, [user]);

  const handleBookService = () => {
    setShowServiceMenu(true);
  };

  const handleConfirmBooking = async (bookingId: string) => {
    if (isConfirming) return;

    try {
      setIsConfirming(true);
      setError(null);

      // Update the booking status
      await updateBookingStatus(bookingId, 'confirmed');

      // Update local state immediately for better UX
      setBookings(prevBookings => 
        prevBookings.map(booking => 
          booking.id === bookingId ? { ...booking, status: 'confirmed' } : booking
        )
      );

      // Close any open modals
      setShowDetails(false);
      setSelectedBooking(null);

      // Refresh the bookings list to ensure everything is in sync
      await fetchBookings();

    } catch (err) {
      console.error('Error confirming booking:', err);
      setError(err instanceof Error ? err.message : 'Failed to confirm booking');
    } finally {
      setIsConfirming(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return CheckCircle;
      case 'cancelled':
        return XCircle;
      case 'confirmed':
        return CheckCircle;
      default:
        return AlertCircle;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-50';
      case 'cancelled':
        return 'text-red-600 bg-red-50';
      case 'confirmed':
        return 'text-blue-600 bg-blue-50';
      default:
        return 'text-yellow-600 bg-yellow-50';
    }
  };

  const filteredBookings = bookings.filter(booking => {
    const matchesSearch = 
      booking.service_type.toLowerCase().includes(searchQuery.toLowerCase()) ||
      booking.property_details.propertyAddress.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || booking.status === statusFilter;
    const matchesTab = activeTab === 'pending' ? booking.status === 'pending' : booking.status === 'confirmed';
    
    return matchesSearch && matchesStatus && matchesTab;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 text-brand-600 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 text-red-600 p-4 rounded-lg">
        {error}
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-semibold text-gray-900">Your Bookings</h2>
          <Button onClick={handleBookService}>
            Book New Service
          </Button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('pending')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'pending'
                  ? 'border-brand-500 text-brand-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Pending Bookings
            </button>
            <button
              onClick={() => setActiveTab('confirmed')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'confirmed'
                  ? 'border-brand-500 text-brand-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Confirmed Bookings
            </button>
          </nav>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search bookings..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Filter className="w-5 h-5 text-gray-400" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="confirmed">Confirmed</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
        </div>

        <AnimatePresence mode="wait">
          {filteredBookings.length === 0 ? (
            <motion.div
              key="empty"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="text-center py-12 bg-gray-50 rounded-xl"
            >
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                No {activeTab} bookings found
              </h3>
              <p className="text-gray-600 mb-8">
                {activeTab === 'pending' 
                  ? 'Book a service to get started'
                  : 'Your confirmed bookings will appear here'}
              </p>
              {activeTab === 'pending' && (
                <Button onClick={handleBookService}>
                  Book a Service
                  <ArrowRight className="ml-2 w-4 h-4" />
                </Button>
              )}
            </motion.div>
          ) : (
            <motion.div
              key="bookings"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="space-y-4"
            >
              {filteredBookings.map((booking, index) => {
                const StatusIcon = getStatusIcon(booking.status);
                const statusColor = getStatusColor(booking.status);
                
                return (
                  <motion.div
                    key={booking.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow p-6"
                  >
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                      <div className="space-y-4 md:space-y-2">
                        <div className="flex items-center space-x-3">
                          <Building2 className="w-5 h-5 text-brand-600" />
                          <h3 className="text-lg font-medium text-gray-900">
                            {booking.service_type.charAt(0).toUpperCase() + booking.service_type.slice(1)} Service
                          </h3>
                          <div className={`px-3 py-1 rounded-full text-sm ${statusColor}`}>
                            <div className="flex items-center space-x-1">
                              <StatusIcon className="w-4 h-4" />
                              <span className="capitalize">{booking.status}</span>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-4">
                          <div className="flex items-center space-x-2 text-gray-600">
                            <Calendar className="w-4 h-4" />
                            <span>{new Date(booking.schedule.date).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center space-x-2 text-gray-600">
                            <Clock className="w-4 h-4" />
                            <span>{booking.schedule.timeSlot}</span>
                          </div>
                          <div className="flex items-center space-x-2 text-gray-600">
                            <MapPin className="w-4 h-4" />
                            <span>{booking.property_details.propertyAddress}</span>
                          </div>
                        </div>
                      </div>

                      <div className="mt-4 md:mt-0 flex items-center space-x-4">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => {
                            setSelectedBooking(booking);
                            setShowDetails(true);
                          }}
                        >
                          <FileText className="w-4 h-4 mr-2" />
                          View Details
                        </Button>
                        {booking.status === 'pending' && (
                          <Button 
                            size="sm"
                            onClick={() => handleConfirmBooking(booking.id)}
                            disabled={isConfirming}
                          >
                            {isConfirming ? (
                              <>
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                Confirming...
                              </>
                            ) : (
                              'Confirm Booking'
                            )}
                          </Button>
                        )}
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Service Menu */}
      <ServiceMenu
        isOpen={showServiceMenu}
        onClose={() => setShowServiceMenu(false)}
        onServiceSelect={(serviceId) => {
          setShowServiceMenu(false);
          window.location.href = `/service-form/${serviceId}`;
        }}
      />

      {/* Booking Details Modal */}
      <BookingDetailsModal
        booking={selectedBooking}
        isOpen={showDetails}
        onClose={() => {
          setShowDetails(false);
          setSelectedBooking(null);
        }}
        onConfirm={async () => {
          if (selectedBooking) {
            await handleConfirmBooking(selectedBooking.id);
          }
        }}
      />
    </>
  );
}