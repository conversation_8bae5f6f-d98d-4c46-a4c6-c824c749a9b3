name: CI

on:
  pull_request:
  push:
    branches:
      - develop

permissions:
  contents: read

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          cache: true

      # Required by: internal/utils/credentials/keyring_test.go
      - uses: t1m0thyj/unlock-keyring@v1
      - run: |
          pkgs=$(go list ./pkg/... | grep -Ev 'pkg/api' | paste -sd ',' -)
          go run gotest.tools/gotestsum -- -race -v -count=1 ./... \
          -coverpkg="./cmd/...,./internal/...,${pkgs}" -coverprofile=coverage.out

      - uses: coverallsapp/github-action@v2
        with:
          file: coverage.out
          format: golang

  lint:
    name: Lint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          # Linter requires no cache
          cache: false

      - uses: golangci/golangci-lint-action@v8
        with:
          args: --timeout 3m --verbose

  start:
    name: Start
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          cache: true
      - run: go build main.go
      - run: ./main init
      - run: sed -i '/\[db.pooler\]/{n;s/.*/enabled = true/}' supabase/config.toml
      - run: ./main start
        env:
          SUPABASE_INTERNAL_IMAGE_REGISTRY: ghcr.io

  codegen:
    name: Codegen
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          cache: true

      - run: go generate
      - run: |
          if ! git diff --ignore-space-at-eol --exit-code --quiet pkg; then
            echo "Detected uncommitted changes after codegen. See status below:"
            git diff
            exit 1
          fi
