import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Menu, X, Phone, ArrowRight, Star } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '../ui/Button';
import { Logo } from '../ui/Logo';
import { navigationItems } from '../../config/navigation';
import { useAuth } from '../../lib/auth/AuthProvider';

export function Header() {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, signOut } = useAuth();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isAccountMenuOpen, setIsAccountMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => setIsScrolled(window.scrollY > 0);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    setIsMobileMenuOpen(false);
    setIsAccountMenuOpen(false);
  }, [location.pathname]);

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <header 
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled 
          ? 'bg-white shadow-sm' 
          : 'bg-white'
      }`}
    >
      {/* Main Navigation */}
      <nav className={`transition-all duration-300 ${isScrolled ? 'py-2' : 'py-4'}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <Link to="/" className="relative group flex items-center">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Logo 
                  textColor={isScrolled ? "text-gray-900" : "text-gray-900"}
                  size="lg"
                  showText={true}
                />
              </motion.div>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-8">
              {/* Navigation Links */}
              <div className="flex space-x-1">
                <Link
                  to="/residential"
                  className={`px-5 py-2.5 rounded-lg font-medium transition-all duration-300 ${
                    location.pathname === '/residential'
                      ? 'text-brand-600 bg-brand-50' 
                      : 'text-gray-600 hover:bg-gray-50'
                  }`}
                >
                  Residential
                </Link>
                <Link
                  to="/solutions"
                  className={`px-5 py-2.5 rounded-lg font-medium transition-all duration-300 ${
                    location.pathname === '/solutions'
                      ? 'text-brand-600 bg-brand-50' 
                      : 'text-gray-600 hover:bg-gray-50'
                  }`}
                >
                  Commercial
                </Link>
                {navigationItems.map((item) => (
                  <Link
                    key={item.label}
                    to={item.href}
                    className={`px-5 py-2.5 rounded-lg font-medium transition-all duration-300 ${
                      location.pathname === item.href
                        ? 'text-brand-600 bg-brand-50' 
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    {item.label}
                  </Link>
                ))}
              </div>

              {/* Auth Buttons */}
              {!user ? (
                <div className="flex items-center space-x-4">
                  <Link to="/auth/login">
                    <Button 
                      variant="outline"
                      className="px-5 py-2.5 border-gray-300 text-gray-700 hover:bg-gray-50"
                    >
                      Sign in
                    </Button>
                  </Link>
                  <Link to="/auth/register">
                    <Button 
                      className="px-5 py-2.5 bg-brand-600 text-white hover:bg-brand-700"
                    >
                      Get Started
                      <ArrowRight className="ml-2 w-4 h-4" />
                    </Button>
                  </Link>
                </div>
              ) : (
                <div className="relative">
                  <Button
                    variant="outline"
                    onClick={() => setIsAccountMenuOpen(!isAccountMenuOpen)}
                    className="px-5 py-2.5 border-gray-300 text-gray-700 hover:bg-gray-50"
                  >
                    Account
                  </Button>

                  <AnimatePresence>
                    {isAccountMenuOpen && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 10 }}
                        className="absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg py-1 z-50"
                      >
                        <Link
                          to="/accountdashboard"
                          className="block px-4 py-2 text-gray-700 hover:bg-gray-50"
                        >
                          Dashboard
                        </Link>
                        <Link
                          to="/accountdashboard?tab=settings"
                          className="block px-4 py-2 text-gray-700 hover:bg-gray-50"
                        >
                          Settings
                        </Link>
                        <div className="border-t border-gray-100 my-1" />
                        <button
                          onClick={handleSignOut}
                          className="block w-full text-left px-4 py-2 text-red-600 hover:bg-red-50"
                        >
                          Sign Out
                        </button>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              )}
            </div>

            {/* Mobile Menu Button */}
            <motion.button
              whileTap={{ scale: 0.95 }}
              className="lg:hidden p-2.5 rounded-lg"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label={isMobileMenuOpen ? 'Close menu' : 'Open menu'}
            >
              <AnimatePresence mode="wait">
                {isMobileMenuOpen ? (
                  <motion.div
                    key="close"
                    initial={{ rotate: -90 }}
                    animate={{ rotate: 0 }}
                    exit={{ rotate: 90 }}
                  >
                    <X className="text-gray-900" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="menu"
                    initial={{ rotate: 90 }}
                    animate={{ rotate: 0 }}
                    exit={{ rotate: -90 }}
                  >
                    <Menu className="text-gray-900" />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.button>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="lg:hidden bg-white border-t border-gray-100 shadow-xl"
            >
              <div className="max-w-7xl mx-auto divide-y divide-gray-100">
                {/* Contact Info */}
                <div className="p-4">
                  <div className="flex flex-col space-y-4 p-4 bg-brand-50 rounded-xl">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Star className="w-4 h-4 text-yellow-400 mr-1.5" />
                        <span className="text-sm text-gray-600">4.9/5 Rating</span>
                      </div>
                      <span className="text-sm text-gray-600">500+ Reviews</span>
                    </div>
                    <a 
                      href="tel:+17187171502" 
                      className="flex items-center text-brand-600 font-medium"
                    >
                      <Phone className="w-4 h-4 mr-2" />
                      (*************
                    </a>
                  </div>
                </div>

                {/* Navigation Links */}
                <div className="py-4 px-4 space-y-1">
                  <Link
                    to="/residential"
                    className={`block px-4 py-3 rounded-lg transition-colors ${
                      location.pathname === '/residential'
                        ? 'bg-brand-50 text-brand-600'
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    Residential
                  </Link>
                  <Link
                    to="/solutions"
                    className={`block px-4 py-3 rounded-lg transition-colors ${
                      location.pathname === '/solutions'
                        ? 'bg-brand-50 text-brand-600'
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    Commercial
                  </Link>
                  {navigationItems.map((item) => (
                    <Link
                      key={item.label}
                      to={item.href}
                      className={`block px-4 py-3 rounded-lg transition-colors ${
                        location.pathname === item.href
                          ? 'bg-brand-50 text-brand-600'
                          : 'text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      {item.label}
                    </Link>
                  ))}
                </div>
                
                {/* Auth Actions */}
                {!user ? (
                  <div className="p-4 space-y-3">
                    <Link to="/auth/login" className="block">
                      <Button 
                        variant="outline" 
                        className="w-full justify-center py-3"
                      >
                        Sign in
                      </Button>
                    </Link>
                    <Link to="/auth/register" className="block">
                      <Button className="w-full justify-center py-3">
                        Get Started
                        <ArrowRight className="ml-2 w-4 h-4" />
                      </Button>
                    </Link>
                  </div>
                ) : (
                  <div className="p-4 space-y-3">
                    <Link to="/accountdashboard" className="block">
                      <Button 
                        variant="outline" 
                        className="w-full justify-center py-3"
                      >
                        Dashboard
                      </Button>
                    </Link>
                    <Button 
                      onClick={handleSignOut}
                      className="w-full justify-center py-3 bg-red-600 hover:bg-red-700 text-white"
                    >
                      Sign Out
                    </Button>
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </nav>
    </header>
  );
}