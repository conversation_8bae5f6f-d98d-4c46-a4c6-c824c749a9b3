import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, CheckCircle, ExternalLink, AlertCircle } from 'lucide-react';
import { PaymentForm } from './PaymentForm';
import { Button } from '../ui/Button';
import { User } from '@supabase/supabase-js';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  amount: number;
  description: string;
  customerEmail?: string;
  formData: any;
  user: User | null;
  onPaymentComplete?: () => void;
}

export function PaymentModal({ 
  isOpen,
  onClose,
  amount,
  description,
  customerEmail,
  formData,
  user: _user,
  onPaymentComplete
}: PaymentModalProps) {
  const [paymentLink, setPaymentLink] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handlePaymentSuccess = (link: string) => {
    if (!link) {
      console.error('Payment link is missing');
      return;
    }
    
    // Check if this is a mock payment response
    if (link.startsWith('/contact')) {
      setError('Payment processing is temporarily unavailable. Your booking has been saved and our team will contact you to complete the payment.');
      return;
    }
    
    setPaymentLink(link);
    setIsSuccess(true);
    if (onPaymentComplete) {
      onPaymentComplete();
    }
  };

  const handleRedirect = () => {
    if (paymentLink) {
      window.open(paymentLink, '_blank');
    }
  };

  const handlePaymentError = (error: Error) => {
    console.error('Payment error:', error.message);
    setError(error.message);
  };

  const handleClose = () => {
    setPaymentLink(null);
    setIsSuccess(false);
    setError(null);
    onClose();
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-screen items-center justify-center">
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm"
              onClick={handleClose}
            />

            {/* Modal */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              className="relative w-full max-w-md mx-4 bg-white rounded-2xl shadow-2xl overflow-hidden"
            >
              {/* Close Button */}
              <button
                onClick={handleClose}
                className="absolute top-4 right-4 p-2 rounded-lg hover:bg-gray-100 transition-colors z-10"
              >
                <X className="w-6 h-6 text-gray-500" />
              </button>

              {/* Content */}
              <div className="p-6">
                {isSuccess ? (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-center py-8"
                  >
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                      <CheckCircle className="w-8 h-8 text-green-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">
                      Payment Link Created!
                    </h3>
                    <p className="text-gray-600 mb-8">
                      Your payment link has been created. Click the button below to complete your payment.
                    </p>
                    <Button onClick={handleRedirect} className="flex items-center justify-center">
                      Complete Payment
                      <ExternalLink className="ml-2 w-4 h-4" />
                    </Button>
                  </motion.div>
                ) : (
                  <>
                    <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                      Secure Payment
                    </h2>
                    {error && (
                      <div className="mb-6 p-4 bg-amber-50 rounded-lg">
                        <div className="flex items-start">
                          <AlertCircle className="w-5 h-5 text-amber-600 mr-2 flex-shrink-0 mt-0.5" />
                          <div>
                            <p className="text-amber-700 text-sm font-medium mb-2">{error}</p>
                            {error.includes('temporarily unavailable') && (
                              <div className="space-y-2">
                                <p className="text-amber-600 text-sm">What happens next:</p>
                                <ul className="list-disc list-inside text-amber-600 text-sm space-y-1">
                                  <li>Your booking details have been saved</li>
                                  <li>Our team will contact you within 24 hours</li>
                                  <li>Payment will be processed securely over the phone</li>
                                </ul>
                                <Button
                                  onClick={() => window.location.href = '/contact'}
                                  className="mt-4 bg-amber-600 hover:bg-amber-700"
                                  size="sm"
                                >
                                  Contact Us Now
                                </Button>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                    <PaymentForm
                      amount={amount}
                      description={description}
                      customerEmail={customerEmail}
                      formData={formData}
                      onSuccess={handlePaymentSuccess}
                      onError={handlePaymentError}
                    />
                  </>
                )}
              </div>
            </motion.div>
          </div>
        </div>
      )}
    </AnimatePresence>
  );
}