import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Building2, Home, ArrowRight, Shield, Star, Clock,
  CheckCircle, Users, Sparkles,
  X, ChevronDown, ChevronUp, Search
} from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { ServiceMenu } from '../../components/services/ServiceMenu';
import { Header } from '../../components/layout/Header';
import { Footer } from '../../components/layout/Footer';
import { ReviewCarousel } from '../../components/testimonials/ReviewCarousel';
import { submitLead } from '../../lib/api/leads';
import { useAuth } from '../../lib/auth/AuthProvider';

// Banner text animation options
const bannerTexts = [
  "Home cleaning, made easy.",
  "Office cleaning, made simple.",
  "Commercial spaces, made spotless.",
  "Your space, our expertise."
];

// City rotation options
const cities = [
  "Brooklyn, New York",
  "Los Angeles, California",
  "Erie, Pennsylvania",
  "Pittsburgh, Pennsylvania",
  "Charlotte, North Carolina",
  "Miami, Florida",
  "Dallas, Texas",
  "Chicago, Illinois"
];

export function HomePage() {
  const navigate = useNavigate();
  const _location = useLocation();
  const { user } = useAuth();
  const [showServiceMenu, setShowServiceMenu] = useState(false);
  const [_activeTab, _setActiveTab] = useState<'residential' | 'commercial'>('residential');
  const [showQuickForm, setShowQuickForm] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    serviceType: '',
    message: '',
    zipCode: ''
  });
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showFAQ, setShowFAQ] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);
  const [isChangingBanner, setIsChangingBanner] = useState(false);
  const [currentCityIndex, setCurrentCityIndex] = useState(0);
  const [isChangingCity, setIsChangingCity] = useState(false);

  // Banner text animation
  useEffect(() => {
    const interval = setInterval(() => {
      setIsChangingBanner(true);
      setTimeout(() => {
        setCurrentBannerIndex((prevIndex) => (prevIndex + 1) % bannerTexts.length);
        setIsChangingBanner(false);
      }, 500); // Wait for fade out animation to complete
    }, 4000); // Change every 4 seconds

    return () => clearInterval(interval);
  }, []);

  // City rotation animation
  useEffect(() => {
    const interval = setInterval(() => {
      setIsChangingCity(true);
      setTimeout(() => {
        setCurrentCityIndex((prevIndex) => (prevIndex + 1) % cities.length);
        setIsChangingCity(false);
      }, 500); // Wait for fade out animation to complete
    }, 3000); // Change every 3 seconds

    return () => clearInterval(interval);
  }, []);

  const _handleBookClick = () => {
    setShowServiceMenu(true);
  };

  const handleResidentialClick = () => {
    navigate('/residential');
  };

  const handleCommercialClick = () => {
    navigate('/solutions');
  };

  const handleQuickFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);
    setIsSubmitting(true);
    
    try {
      // Prepare lead data
      const leadData = {
        full_name: formData.name,
        email: formData.email,
        phone: formData.phone,
        zip_code: formData.zipCode || '00000',
        service_interests: formData.serviceType ? [formData.serviceType] : [],
        notes: formData.message,
        preferred_contact_method: 'any' as const
      };
      
      await submitLead(leadData, user);
      console.log('Lead submitted successfully');
      setFormSubmitted(true);
      
      // Reset form after 3 seconds
      setTimeout(() => {
        setFormSubmitted(false);
        setFormData({
          name: '',
          email: '',
          phone: '',
          serviceType: '',
          message: '',
          zipCode: ''
        });
        setShowQuickForm(false);
      }, 3000);
    } catch (err) {
      console.error('Error submitting lead:', err);
      setFormError(err instanceof Error ? err.message : 'Failed to submit request');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!searchQuery.trim()) return;
    
    // Determine which service to navigate to based on search query
    const query = searchQuery.toLowerCase();
    let serviceId = '';
    
    if (query.includes('office') || query.includes('commercial')) {
      serviceId = 'office';
    } else if (query.includes('carpet')) {
      serviceId = 'carpet';
    } else if (query.includes('window')) {
      serviceId = 'window';
    } else if (query.includes('deep')) {
      serviceId = 'deep';
    } else if (query.includes('construction')) {
      serviceId = 'construction';
    } else if (query.includes('sanitization') || query.includes('disinfect')) {
      serviceId = 'sanitization';
    } else if (query.includes('tile') || query.includes('grout')) {
      serviceId = 'tile';
    } else if (query.includes('pressure') || query.includes('wash')) {
      serviceId = 'pressure';
    } else if (query.includes('floor')) {
      serviceId = 'floor';
    } else {
      // Default to service menu if no specific match
      setShowServiceMenu(true);
      return;
    }
    
    navigate(`/service-form/${serviceId}`);
  };

  const faqs = [
    {
      question: "How often should I schedule cleaning services?",
      answer: "For residential properties, we recommend weekly or bi-weekly cleaning for high-traffic homes, and monthly for less frequently used spaces. For commercial properties, daily or weekly cleaning is typically recommended depending on your business type and foot traffic."
    },
    {
      question: "Do you bring your own cleaning supplies?",
      answer: "Yes, our professional team brings all necessary cleaning supplies and equipment. We use eco-friendly, commercial-grade products that are effective yet safe for your family, pets, and the environment."
    },
    {
      question: "Are your cleaning staff insured and background checked?",
      answer: "Absolutely. All our cleaning professionals undergo thorough background checks and are fully insured. We prioritize your security and peace of mind with every service."
    },
    {
      question: "What if I'm not satisfied with the cleaning service?",
      answer: "Your satisfaction is our priority. If you're not completely satisfied with our service, contact us within 24 hours and we'll return to re-clean the areas in question at no additional cost."
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main>
        {/* Hero Section */}
        <section className="relative min-h-screen flex items-center justify-center pt-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div className="text-center max-w-3xl mx-auto">
              <motion.div className="h-[220px] sm:h-[240px] md:h-[280px] flex items-center justify-center mb-16">
                <AnimatePresence mode="wait">
                  {!isChangingBanner && (
                    <motion.h1
                      key={currentBannerIndex}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.5 }}
                      className="text-5xl sm:text-6xl md:text-7xl font-bold text-gray-900 leading-tight px-4"
                    >
                      {bannerTexts[currentBannerIndex]}
                    </motion.h1>
                  )}
                </AnimatePresence>
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="relative max-w-xl mx-auto mb-12"
              >
                <form onSubmit={handleSearch}>
                  <div className="flex rounded-full border border-gray-300 bg-white shadow-sm overflow-hidden">
                    <input
                      type="text"
                      placeholder="Search for cleaning services..."
                      className="flex-grow py-4 px-6 focus:outline-none text-gray-700"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                    <button 
                      type="submit"
                      className="bg-brand-600 text-white px-6 flex items-center justify-center hover:bg-brand-700 transition-colors"
                    >
                      <Search className="w-5 h-5" />
                    </button>
                  </div>
                </form>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="text-sm text-gray-500 mb-16"
              >
                4.9 stars from 4,500+ trusted reviews
              </motion.div>
            </div>
          </div>

          {/* Curved Image Section */}
          <div className="w-full max-w-5xl mx-auto overflow-hidden">
            <div className="rounded-t-[50%] h-80 overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80"
                alt="Professional cleaning service"
                className="w-full h-full object-cover object-center"
              />
            </div>
          </div>
        </section>

        {/* Service Types Section */}
        <section className="py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                className="text-3xl font-bold text-gray-900 mb-4"
              >
                Popular services near{" "}
                <AnimatePresence mode="wait">
                  {!isChangingCity && (
                    <motion.span
                      key={currentCityIndex}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.5 }}
                      className="text-brand-600 inline-block"
                    >
                      {cities[currentCityIndex]}
                    </motion.span>
                  )}
                </AnimatePresence>
              </motion.h2>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {[
                { icon: Home, title: "House Cleaning", id: "regular" },
                { icon: Sparkles, title: "Deep Cleaning", id: "deep" },
                { icon: Building2, title: "Office Cleaning", id: "office" },
                { icon: Shield, title: "Sanitization", id: "sanitization" },
                { icon: Users, title: "Post-Construction", id: "construction" },
                { icon: Clock, title: "Carpet Cleaning", id: "carpet" },
                { icon: Star, title: "Window Cleaning", id: "window" },
                { icon: Building2, title: "Floor Restoration", id: "floor" }
              ].map((service, index) => {
                const Icon = service.icon;
                return (
                  <motion.div
                    key={service.title}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: 0.1 + (index * 0.05) }}
                    className="flex flex-col items-center text-center cursor-pointer group"
                    onClick={() => navigate(`/service-form/${service.id}`)}
                  >
                    <div className="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center mb-4 group-hover:bg-brand-50 transition-colors">
                      <Icon className="w-8 h-8 text-gray-600 group-hover:text-brand-600 transition-colors" />
                    </div>
                    <span className="text-gray-800 font-medium">{service.title}</span>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Service Selection Section */}
        <section className="py-24 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-16">
              {/* Residential Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.2 }}
                className="group relative"
              >
                <button
                  onClick={handleResidentialClick}
                  className="w-full text-left"
                >
                  <div className="relative bg-white rounded-2xl shadow-sm overflow-hidden hover:shadow-md transition-all duration-300">
                    {/* Image */}
                    <div className="aspect-w-16 aspect-h-9 relative">
                      <img
                        src="https://images.unsplash.com/photo-1484154218962-a197022b5858?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80"
                        alt="Residential cleaning"
                        className="w-full h-full object-cover"
                      />
                    </div>

                    {/* Content */}
                    <div className="p-8">
                      <div className="flex items-center space-x-3 mb-4">
                        <div className="p-2 rounded-full bg-brand-100">
                          <Home className="w-6 h-6 text-brand-600" />
                        </div>
                        <h3 className="text-2xl font-bold text-gray-900">Residential</h3>
                      </div>
                      
                      <p className="text-gray-600 mb-6">
                        Professional cleaning services for homes, apartments, and residential properties.
                      </p>

                      <div className="flex items-center text-brand-600 font-medium">
                        View Residential Services
                        <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                      </div>
                    </div>
                  </div>
                </button>
              </motion.div>

              {/* Commercial Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.3 }}
                className="group relative"
              >
                <button
                  onClick={handleCommercialClick}
                  className="w-full text-left"
                >
                  <div className="relative bg-white rounded-2xl shadow-sm overflow-hidden hover:shadow-md transition-all duration-300">
                    {/* Image */}
                    <div className="aspect-w-16 aspect-h-9 relative">
                      <img
                        src="https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80"
                        alt="Commercial cleaning"
                        className="w-full h-full object-cover"
                      />
                    </div>

                    {/* Content */}
                    <div className="p-8">
                      <div className="flex items-center space-x-3 mb-4">
                        <div className="p-2 rounded-full bg-brand-100">
                          <Building2 className="w-6 h-6 text-brand-600" />
                        </div>
                        <h3 className="text-2xl font-bold text-gray-900">Commercial</h3>
                      </div>
                      
                      <p className="text-gray-600 mb-6">
                        Specialized cleaning solutions for offices, medical facilities, and businesses.
                      </p>

                      <div className="flex items-center text-brand-600 font-medium">
                        View Commercial Services
                        <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                      </div>
                    </div>
                  </div>
                </button>
              </motion.div>
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section className="py-24 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-20">
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                className="text-3xl font-bold text-gray-900 mb-4"
              >
                How it works
              </motion.h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-16">
              {[
                {
                  number: "1",
                  title: "Tell us what you need",
                  description: "Answer a few questions about your cleaning needs and we'll connect you with the right professionals."
                },
                {
                  number: "2",
                  title: "Get matched with pros",
                  description: "We'll match you with qualified cleaning professionals who are ready to help."
                },
                {
                  number: "3",
                  title: "Hire the right pro",
                  description: "Compare quotes, profiles, and reviews, then hire the pro that's right for you."
                }
              ].map((step, index) => (
                <motion.div
                  key={step.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.2 + (index * 0.1) }}
                  className="text-center"
                >
                  <div className="w-16 h-16 rounded-full bg-brand-100 flex items-center justify-center text-2xl font-bold text-brand-600 mx-auto mb-6">
                    {step.number}
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{step.title}</h3>
                  <p className="text-gray-600 max-w-xs mx-auto">{step.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Testimonials Section - Using ReviewCarousel from Residential page */}
        <ReviewCarousel />

        {/* FAQ Section */}
        <section className="py-24 bg-white">
          <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                className="text-3xl font-bold text-gray-900 mb-4"
              >
                Frequently asked questions
              </motion.h2>
            </div>

            <div className="space-y-4">
              {faqs.map((faq, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.2 + (index * 0.1) }}
                  className="border-b border-gray-200 pb-4"
                >
                  <button
                    className="w-full py-4 text-left flex justify-between items-center"
                    onClick={() => setShowFAQ(showFAQ === index ? null : index)}
                  >
                    <h3 className="text-lg font-medium text-gray-900">{faq.question}</h3>
                    {showFAQ === index ? (
                      <ChevronUp className="w-5 h-5 text-gray-500" />
                    ) : (
                      <ChevronDown className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  
                  <AnimatePresence>
                    {showFAQ === index && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="overflow-hidden"
                      >
                        <div className="py-4 text-gray-600">
                          {faq.answer}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-24 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-3xl font-bold text-gray-900 mb-6"
            >
              Ready to experience a cleaner space?
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 }}
              className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto"
            >
              Get started with a free quote today
            </motion.p>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
              className="flex flex-col sm:flex-row justify-center gap-4"
            >
              <Button
                size="lg"
                onClick={() => setShowQuickForm(true)}
                className="bg-brand-600 text-white hover:bg-brand-700"
              >
                Get Free Quote
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                onClick={() => navigate('/contact')}
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                Contact Us
              </Button>
            </motion.div>
          </div>
        </section>

        {/* Quick Quote Form Modal */}
        <AnimatePresence>
          {showQuickForm && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              onClick={() => setShowQuickForm(false)}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="bg-white rounded-2xl shadow-2xl max-w-lg w-full p-6 sm:p-8"
                onClick={e => e.stopPropagation()}
              >
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-2xl font-bold text-gray-900">Get Your Free Quote</h3>
                  <button 
                    onClick={() => setShowQuickForm(false)}
                    className="p-2 rounded-full hover:bg-gray-100"
                  >
                    <X className="w-5 h-5 text-gray-500" />
                  </button>
                </div>

                {formSubmitted ? (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-center py-8"
                  >
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <CheckCircle className="w-8 h-8 text-green-600" />
                    </div>
                    <h4 className="text-xl font-semibold text-gray-900 mb-2">Thank You!</h4>
                    <p className="text-gray-600">
                      Your request has been submitted. We'll contact you shortly with your personalized quote.
                    </p>
                  </motion.div>
                ) : (
                  <form onSubmit={handleQuickFormSubmit} className="space-y-4">
                    {formError && (
                      <div className="p-4 bg-red-50 border border-red-200 rounded-lg text-red-600 text-sm">
                        {formError}
                      </div>
                    )}
                    
                    {/* Form Tabs */}
                    <div className="flex rounded-lg bg-gray-100 p-1 mb-4">
                      <button
                        type="button"
                        className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                          activeTab === 'residential' 
                            ? 'bg-white shadow-sm text-brand-600' 
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                        onClick={() => setActiveTab('residential')}
                      >
                        <Home className="w-4 h-4 inline mr-2" />
                        Residential
                      </button>
                      <button
                        type="button"
                        className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                          activeTab === 'commercial' 
                            ? 'bg-white shadow-sm text-brand-600' 
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                        onClick={() => setActiveTab('commercial')}
                      >
                        <Building2 className="w-4 h-4 inline mr-2" />
                        Commercial
                      </button>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Full Name <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Email <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Phone Number <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="tel"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                          required
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          ZIP Code <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          name="zipCode"
                          value={formData.zipCode}
                          onChange={handleInputChange}
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                          required
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Service Type <span className="text-red-500">*</span>
                      </label>
                      <select
                        name="serviceType"
                        value={formData.serviceType}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                        required
                      >
                        <option value="">Select a service</option>
                        {activeTab === 'residential' ? (
                          <>
                            <option value="regular-cleaning">Regular Cleaning</option>
                            <option value="deep-cleaning">Deep Cleaning</option>
                            <option value="move-in-out">Move In/Out Cleaning</option>
                            <option value="post-construction">Post Construction Cleaning</option>
                          </>
                        ) : (
                          <>
                            <option value="office-cleaning">Office Cleaning</option>
                            <option value="medical-facility">Medical Facility Cleaning</option>
                            <option value="retail-cleaning">Retail Cleaning</option>
                            <option value="industrial-cleaning">Industrial Cleaning</option>
                            <option value="educational-facility">Educational Facility</option>
                          </>
                        )}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Message
                      </label>
                      <textarea
                        name="message"
                        value={formData.message}
                        onChange={handleInputChange}
                        rows={3}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                        placeholder="Tell us about your cleaning needs..."
                      ></textarea>
                    </div>

                    <Button type="submit" className="w-full" disabled={isSubmitting}>
                      {isSubmitting ? 'Submitting...' : 'Submit Request'}
                    </Button>

                    <p className="text-xs text-gray-500 text-center mt-4">
                      By submitting this form, you agree to our <a href="/privacy" className="text-brand-600 hover:underline">Privacy Policy</a> and <a href="/terms" className="text-brand-600 hover:underline">Terms of Service</a>.
                    </p>
                  </form>
                )}
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </main>
      <Footer />

      {/* Service Menu */}
      <ServiceMenu
        isOpen={showServiceMenu}
        onClose={() => setShowServiceMenu(false)}
        onServiceSelect={(serviceId) => {
          setShowServiceMenu(false);
          navigate(`/service-form/${serviceId}`);
        }}
      />
    </div>
  );
}