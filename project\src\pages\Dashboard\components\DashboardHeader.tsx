import React from 'react';
import { useAuth } from '../../../lib/auth/AuthProvider';
import { motion } from 'framer-motion';
import {
  Building2, Calendar, CheckCircle, Clock
} from 'lucide-react';

interface DashboardStats {
  totalBookings: number;
  pendingBookings: number;
  completedBookings: number;
  upcomingServices: number;
}

interface DashboardHeaderProps {
  stats?: DashboardStats;
  loading?: boolean;
}

export function DashboardHeader({ stats, loading = false }: DashboardHeaderProps) {
  const { user } = useAuth();
  const displayStats = stats || {
    totalBookings: 0,
    pendingBookings: 0,
    completedBookings: 0,
    upcomingServices: 0
  };

  // Get user's first name from email or metadata
  const firstName = user?.user_metadata?.full_name?.split(' ')[0] || 
                   user?.email?.split('@')[0] || 
                   'there';

  return (
    <div className="relative bg-gradient-to-br from-brand-600 to-brand-700 overflow-hidden">
      {/* Enhanced Background Pattern */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-black/10" />
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 2px 2px, rgba(255,255,255,0.1) 1px, transparent 0)`,
          backgroundSize: '24px 24px'
        }} />
      </div>

      {/* Content */}
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 mt-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-white">
            Welcome back, {firstName}! 👋
          </h1>
          <p className="text-brand-100 mt-2">
            Here's an overview of your cleaning services and bookings
          </p>
        </motion.div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[
            {
              label: 'Active Services',
              value: displayStats.upcomingServices,
              icon: Building2,
              color: 'from-blue-500/20 to-blue-600/20'
            },
            {
              label: 'Pending Bookings',
              value: displayStats.pendingBookings,
              icon: Calendar,
              color: 'from-green-500/20 to-green-600/20'
            },
            {
              label: 'Completed Services',
              value: displayStats.completedBookings,
              icon: CheckCircle,
              color: 'from-purple-500/20 to-purple-600/20'
            },
            {
              label: 'Total Bookings',
              value: displayStats.totalBookings,
              icon: Clock,
              color: 'from-amber-500/20 to-amber-600/20'
            }
          ].map((stat, index) => {
            const Icon = stat.icon;
            return (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="relative group"
              >
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/10
                              hover:bg-white/20 transition-all duration-300">
                  {/* Content */}
                  <div className="relative">
                    <div className="p-3 rounded-xl bg-white/10 mb-4 w-fit">
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    
                    <div className="space-y-1">
                      <div className="text-3xl font-bold text-white">
                        {loading ? '-' : stat.value}
                      </div>
                      <div className="text-brand-100">{stat.label}</div>
                    </div>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Decorative Bottom Wave */}
      <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-gray-50 to-transparent" />
    </div>
  );
}