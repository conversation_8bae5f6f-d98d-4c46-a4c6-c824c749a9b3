import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, XCircle, Play, AlertTriangle } from 'lucide-react';
import { Button } from '../ui/Button';
import { runFormTests, checkFormConfiguration } from '../../lib/utils/formTesting';

interface TestResult {
  passed: number;
  failed: number;
  results: Record<string, boolean>;
}

export function FormTestRunner() {
  const [testResults, setTestResults] = useState<TestResult | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [configCheck, setConfigCheck] = useState<{ isConfigured: boolean; issues: string[] } | null>(null);

  const runTests = async () => {
    setIsRunning(true);
    setTestResults(null);
    setConfigCheck(null);

    try {
      // Check configuration first
      const config = checkFormConfiguration();
      setConfigCheck(config);

      // Run tests
      await new Promise(resolve => setTimeout(resolve, 500)); // Small delay for UX
      const results = runFormTests();
      setTestResults(results);
    } catch (error) {
      console.error('Error running tests:', error);
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Form Functionality Test</h2>
        <p className="text-gray-600">
          Test form persistence, validation, and configuration to ensure forms work correctly.
        </p>
      </div>

      <div className="flex justify-center mb-6">
        <Button
          onClick={runTests}
          disabled={isRunning}
          className="flex items-center space-x-2"
        >
          <Play className="w-4 h-4" />
          <span>{isRunning ? 'Running Tests...' : 'Run Form Tests'}</span>
        </Button>
      </div>

      {/* Configuration Check */}
      {configCheck && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <h3 className="text-lg font-semibold mb-3 flex items-center">
            {configCheck.isConfigured ? (
              <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
            ) : (
              <AlertTriangle className="w-5 h-5 text-amber-600 mr-2" />
            )}
            Configuration Check
          </h3>
          
          {configCheck.isConfigured ? (
            <div className="p-3 bg-green-50 rounded-lg">
              <p className="text-green-700">✅ All form configurations are properly set up</p>
            </div>
          ) : (
            <div className="p-3 bg-amber-50 rounded-lg">
              <p className="text-amber-700 font-medium mb-2">Configuration Issues:</p>
              <ul className="list-disc list-inside text-amber-600 space-y-1">
                {configCheck.issues.map((issue, index) => (
                  <li key={index}>{issue}</li>
                ))}
              </ul>
            </div>
          )}
        </motion.div>
      )}

      {/* Test Results */}
      {testResults && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-4"
        >
          <h3 className="text-lg font-semibold mb-3">Test Results</h3>
          
          {/* Summary */}
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="p-3 bg-green-50 rounded-lg text-center">
              <div className="text-2xl font-bold text-green-600">{testResults.passed}</div>
              <div className="text-green-700">Passed</div>
            </div>
            <div className="p-3 bg-red-50 rounded-lg text-center">
              <div className="text-2xl font-bold text-red-600">{testResults.failed}</div>
              <div className="text-red-700">Failed</div>
            </div>
          </div>

          {/* Individual Test Results */}
          <div className="space-y-2">
            {Object.entries(testResults.results).map(([testName, passed]) => (
              <div
                key={testName}
                className={`p-3 rounded-lg flex items-center justify-between ${
                  passed ? 'bg-green-50' : 'bg-red-50'
                }`}
              >
                <span className={`font-medium ${passed ? 'text-green-700' : 'text-red-700'}`}>
                  {testName}
                </span>
                {passed ? (
                  <CheckCircle className="w-5 h-5 text-green-600" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-600" />
                )}
              </div>
            ))}
          </div>

          {/* Overall Status */}
          <div className={`p-4 rounded-lg text-center ${
            testResults.failed === 0 ? 'bg-green-50' : 'bg-amber-50'
          }`}>
            {testResults.failed === 0 ? (
              <div className="text-green-700">
                <CheckCircle className="w-6 h-6 mx-auto mb-2" />
                <p className="font-semibold">All tests passed! Forms are working correctly.</p>
              </div>
            ) : (
              <div className="text-amber-700">
                <AlertTriangle className="w-6 h-6 mx-auto mb-2" />
                <p className="font-semibold">
                  {testResults.failed} test(s) failed. Please check the form implementation.
                </p>
              </div>
            )}
          </div>
        </motion.div>
      )}

      {/* Loading State */}
      {isRunning && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-8"
        >
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Running form tests...</p>
        </motion.div>
      )}
    </div>
  );
}
