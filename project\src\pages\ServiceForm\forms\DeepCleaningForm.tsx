import React from 'react';
import { Form, FormField } from '../components/FormFields';
import { Building2, Sparkles, Shield, Clock } from 'lucide-react';

interface DeepCleaningFormProps {
  onSubmit: (data: Record<string, unknown>) => void;
}

export function DeepCleaningForm({ onSubmit }: DeepCleaningFormProps) {
  return (
    <Form onSubmit={onSubmit}>
      <div className="space-y-8">
        {/* Property Information */}
        <div className="space-y-6">
          <div className="flex items-center space-x-4 mb-4">
            <div className="p-3 rounded-full bg-brand-100">
              <Building2 className="w-6 h-6 text-brand-600" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">Property Information</h3>
              <p className="text-gray-600">Tell us about your space</p>
            </div>
          </div>

          <FormField label="Property Type" required>
            <select
              name="propertyType"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg"
              required
            >
              <option value="">Select property type</option>
              <option value="office">Office Space</option>
              <option value="medical">Medical Facility</option>
              <option value="retail">Retail Space</option>
              <option value="restaurant">Restaurant</option>
              <option value="industrial">Industrial Facility</option>
              <option value="educational">Educational Institution</option>
            </select>
          </FormField>

          <FormField label="Square Footage" required>
            <input
              type="number"
              name="squareFootage"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg"
              placeholder="Total area to be cleaned"
              required
            />
          </FormField>
        </div>

        {/* Cleaning Scope */}
        <div className="space-y-6">
          <div className="flex items-center space-x-4 mb-4">
            <div className="p-3 rounded-full bg-brand-100">
              <Sparkles className="w-6 h-6 text-brand-600" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">Cleaning Scope</h3>
              <p className="text-gray-600">Select areas that need attention</p>
            </div>
          </div>

          <FormField label="Areas to Clean" required>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {[
                'Reception/Lobby',
                'Private Offices',
                'Conference Rooms',
                'Break Room/Kitchen',
                'Restrooms',
                'Common Areas',
                'Stairwells',
                'Storage Areas',
                'Server Rooms',
                'Elevators',
                'Windows (Interior)',
                'HVAC Vents'
              ].map((area) => (
                <label key={area} className="flex items-center p-3 border rounded-lg hover:bg-gray-50">
                  <input
                    type="checkbox"
                    name="areas"
                    value={area.toLowerCase().replace(/\s+/g, '-')}
                    className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
                  />
                  <span className="ml-2 text-gray-700">{area}</span>
                </label>
              ))}
            </div>
          </FormField>
        </div>

        {/* Service Options */}
        <div className="space-y-6">
          <div className="flex items-center space-x-4 mb-4">
            <div className="p-3 rounded-full bg-brand-100">
              <Shield className="w-6 h-6 text-brand-600" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">Service Options</h3>
              <p className="text-gray-600">Customize your cleaning service</p>
            </div>
          </div>

          <FormField label="Additional Services">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {[
                'Carpet Deep Cleaning',
                'Floor Waxing/Buffing',
                'High Dusting',
                'Window Cleaning',
                'Upholstery Cleaning',
                'Sanitization Service',
                'Pressure Washing',
                'Grout Cleaning'
              ].map((service) => (
                <label key={service} className="flex items-center p-3 border rounded-lg hover:bg-gray-50">
                  <input
                    type="checkbox"
                    name="additionalServices"
                    value={service.toLowerCase().replace(/\s+/g, '-')}
                    className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
                  />
                  <span className="ml-2 text-gray-700">{service}</span>
                </label>
              ))}
            </div>
          </FormField>

          <FormField label="Special Requirements">
            <div className="space-y-3">
              <label className="flex items-center p-3 border rounded-lg hover:bg-gray-50">
                <input
                  type="checkbox"
                  name="ecoFriendly"
                  className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
                />
                <span className="ml-2 text-gray-700">Eco-Friendly Products Only</span>
              </label>
              <label className="flex items-center p-3 border rounded-lg hover:bg-gray-50">
                <input
                  type="checkbox"
                  name="hypoallergenic"
                  className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
                />
                <span className="ml-2 text-gray-700">Hypoallergenic Products Required</span>
              </label>
            </div>
          </FormField>
        </div>

        {/* Schedule */}
        <div className="space-y-6">
          <div className="flex items-center space-x-4 mb-4">
            <div className="p-3 rounded-full bg-brand-100">
              <Clock className="w-6 h-6 text-brand-600" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">Schedule</h3>
              <p className="text-gray-600">Choose your preferred timing</p>
            </div>
          </div>

          <FormField label="Service Frequency" required>
            <select
              name="frequency"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg"
              required
            >
              <option value="">Select frequency</option>
              <option value="one-time">One-Time Deep Clean</option>
              <option value="monthly">Monthly</option>
              <option value="quarterly">Quarterly</option>
              <option value="semi-annual">Semi-Annual</option>
              <option value="annual">Annual</option>
            </select>
          </FormField>

          <FormField label="Preferred Time">
            <select
              name="timePreference"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg"
            >
              <option value="">Select preferred time</option>
              <option value="morning">Morning (8AM - 12PM)</option>
              <option value="afternoon">Afternoon (12PM - 4PM)</option>
              <option value="evening">Evening (4PM - 8PM)</option>
              <option value="night">Night (8PM - 12AM)</option>
              <option value="weekend">Weekends Only</option>
            </select>
          </FormField>

          <FormField label="Special Instructions">
            <textarea
              name="specialInstructions"
              rows={4}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg"
              placeholder="Any specific requirements, access instructions, or areas that need special attention?"
            />
          </FormField>
        </div>
      </div>
    </Form>
  );
}
